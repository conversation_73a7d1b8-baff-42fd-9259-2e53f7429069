{"name": "@bagistoplus/purple", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "editor:build": "vite build --config vite.editor.config.ts", "editor:dev": "vite build --watch --config vite.editor.config.ts"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.5", "@types/alpinejs": "^3.13.11", "@types/node": "^22.15.3", "@vitejs/plugin-vue": "^5.2.3", "laravel-vite-plugin": "^1.2.0", "tailwindcss": "^4.1.5", "unplugin-auto-import": "^19.2.0", "vite": "^6.3.4", "vite-plugin-static-copy": "^3.0.0", "vue": "^3.5.13"}}