import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import tailwindcss from '@tailwindcss/vite';

export default defineConfig({
  plugins: [
    tailwindcss(),
    laravel({
      input: ['resources/assets/css/theme.css', 'resources/assets/js/theme.js'],
      refresh: true,
      buildDirectory: 'themes/shop/new-theme',
      hotFile: 'public/themes/shop/new-theme/new-theme-vite.hot',
    }),
  ],
});
