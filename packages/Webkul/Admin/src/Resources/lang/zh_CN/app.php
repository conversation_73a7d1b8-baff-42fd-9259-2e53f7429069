<?php

return [
    'users' => [
        'sessions' => [
            'email'                  => '电子邮件地址',
            'forget-password-link'   => '忘记密码？',
            'password'               => '密码',
            'powered-by-description' => '由 :bagisto 提供支持，一个由 :webkul 社区支持的项目。',
            'submit-btn'             => '登录',
            'title'                  => '登录',
        ],

        'forget-password' => [
            'create' => [
                'email'                  => '注册电子邮件',
                'email-not-exist'        => '电子邮件不存在',
                'page-title'             => '忘记密码',
                'powered-by-description' => '由 :bagisto 提供支持，一个由 :webkul 社区支持的项目。',
                'reset-link-sent'        => '重置密码链接已发送',
                'sign-in-link'           => '返回登录？',
                'submit-btn'             => '重置',
                'title'                  => '找回密码',
            ],
        ],

        'reset-password' => [
            'back-link-title'        => '返回登录？',
            'confirm-password'       => '确认密码',
            'email'                  => '注册电子邮件',
            'password'               => '密码',
            'powered-by-description' => '由 :bagisto 提供支持，一个由 :webkul 社区支持的项目。',
            'submit-btn'             => '重置密码',
            'title'                  => '重置密码',
        ],
    ],

    'notifications' => [
        'description-text' => '列出所有通知',
        'marked-success'   => '所有通知已标记为已读',
        'no-record'        => '未找到记录',
        'of'               => '的',
        'per-page'         => '每页',
        'read-all'         => '标记为已读',
        'title'            => '通知',
        'view-all'         => '查看全部',

        'order-status-messages' => [
            'all'             => '全部',
            'canceled'        => '订单已取消',
            'closed'          => '订单已关闭',
            'completed'       => '订单已完成',
            'pending'         => '订单待处理',
            'pending-payment' => '待付款订单',
            'processing'      => '订单处理中',
        ],
    ],

    'account' => [
        'edit' => [
            'back-btn'          => '返回',
            'change-password'   => '修改密码',
            'confirm-password'  => '确认密码',
            'current-password'  => '当前密码',
            'email'             => '电子邮件',
            'general'           => '通用',
            'invalid-password'  => '您输入的当前密码不正确。',
            'name'              => '姓名',
            'password'          => '密码',
            'profile-image'     => '个人资料图片',
            'save-btn'          => '保存账户',
            'title'             => '我的账户',
            'update-success'    => '账户已成功更新',
            'upload-image-info' => '上传个人资料图片（110px X 110px），格式为PNG或JPG',
        ],
    ],

    'dashboard' => [
        'index' => [
            'add-customer'                => '添加客户',
            'add-product'                 => '添加产品',
            'all-channels'                => '所有渠道',
            'attribute-code'              => '属性代码',
            'average-sale'                => '平均订单销售额',
            'color'                       => '颜色',
            'customer-info'               => '未找到销售额最高的客户',
            'customer-with-most-sales'    => '销售额最高的客户',
            'date-duration'               => ':start - :end',
            'decreased'                   => ':progress%',
            'empty-threshold'             => '空阈值',
            'empty-threshold-description' => '没有可用的产品',
            'end-date'                    => '结束日期',
            'from'                        => '从',
            'increased'                   => ':progress%',
            'more-products'               => ':product_count+ 更多图片',
            'order'                       => ':total_orders 订单',
            'order-count'                 => ':count 订单',
            'order-id'                    => '#:id',
            'overall-details'             => '总体详情',
            'pay-by'                      => '支付方式 - :method',
            'product-count'               => ':count 产品',
            'product-image'               => '产品图片',
            'product-info'                => '在路上添加相关产品。',
            'product-number'              => '产品 - :product_number',
            'revenue'                     => '收入 :total',
            'sale-count'                  => ':count 销售',
            'sales'                       => '销售',
            'sku'                         => 'SKU - :sku',
            'start-date'                  => '开始日期',
            'stock-threshold'             => '库存阈值',
            'store-stats'                 => '商店统计',
            'title'                       => '仪表盘',
            'to'                          => '至',
            'today-customers'             => '今日客户',
            'today-details'               => '今日详情',
            'today-orders'                => '今日订单',
            'today-sales'                 => '今日销售',
            'top-performing-categories'   => '表现最佳的类别',
            'top-selling-products'        => '畅销产品',
            'total-customers'             => '总客户数',
            'total-orders'                => '总订单数',
            'total-sales'                 => '总销售额',
            'total-stock'                 => ':total_stock 库存',
            'total-unpaid-invoices'       => '未付发票总数',
            'unique-visitors'             => ':count 唯一',
            'user-info'                   => '快速查看商店的最新情况',
            'user-name'                   => '你好！:user_name',
            'visitors'                    => '访客',
        ],
    ],

    'sales' => [
        'orders' => [
            'index' => [
                'create-btn' => '创建订单',
                'title'      => '订单',

                'search-customer' => [
                    'create-btn'  => '创建客户',
                    'empty-info'  => '没有可用于搜索条件的客户。',
                    'empty-title' => '未找到客户',
                    'search-by'   => '按电子邮件或姓名搜索',
                    'title'       => '选择客户',
                ],

                'datagrid' => [
                    'canceled'        => '已取消',
                    'channel-name'    => '渠道',
                    'closed'          => '已关闭',
                    'completed'       => '已完成',
                    'customer'        => '客户',
                    'date'            => '日期',
                    'email'           => '电子邮件',
                    'fraud'           => '欺诈',
                    'grand-total'     => '总计',
                    'id'              => '订单号：#:id',
                    'items'           => '商品',
                    'location'        => '地点',
                    'order-id'        => '订单编号',
                    'pay-by'          => '支付方式 - :method',
                    'pay-via'         => '支付方式',
                    'pending-payment' => '待付款',
                    'pending'         => '待处理',
                    'processing'      => '处理中',
                    'product-count'   => ':count + 更多产品',
                    'status'          => '状态',
                    'success'         => '成功',
                    'view'            => '查看',
                ],
            ],

            'create' => [
                'add-to-cart'             => '添加到购物车',
                'back-btn'                => '返回',
                'check-billing-address'   => '缺少账单地址。',
                'check-shipping-address'  => '缺少送货地址。',
                'configuration'           => '配置',
                'coupon-already-applied'  => '优惠券已经应用。',
                'coupon-applied'          => '优惠券已成功应用。',
                'coupon-error'            => '无法应用优惠券。',
                'coupon-not-found'        => '找不到优惠券',
                'coupon-remove'           => '优惠券已成功移除。',
                'error'                   => '出现错误',
                'minimum-order-error'     => '未达到最低订单金额。',
                'order-placed-success'    => '订单已成功下单。',
                'payment-not-supported'   => '不支持此支付方式',
                'save-btn'                => '创建订单',
                'specify-payment-method'  => '缺少支付方式。',
                'specify-shipping-method' => '缺少配送方式。',
                'title'                   => '为 :name 创建订单',

                'types' => [
                    'simple' => [
                        'none'         => '无',
                        'total-amount' => '总金额',
                    ],

                    'configurable' => [
                        'select-options' => '请选择一个选项',
                    ],

                    'bundle' => [
                        'none'         => '无',
                        'total-amount' => '总金额',
                    ],

                    'grouped' => [
                        'name' => '名称',
                    ],

                    'downloadable' => [
                        'title' => '链接',
                    ],

                    'virtual' => [
                        'none'         => '无',
                        'total-amount' => '总金额',
                    ],
                ],

                'cart' => [
                    'success-add-to-cart' => '产品已成功添加到购物车',
                    'success-remove'      => '项目已成功从购物车中移除',
                    'success-update'      => '购物车项目已成功更新',

                    'items' => [
                        'add-product'       => '添加产品',
                        'amount-per-unit'   => ':amount 每单位 x :qty 数量',
                        'delete'            => '删除',
                        'empty-description' => '购物车中没有找到任何商品。',
                        'empty-title'       => '购物车为空',
                        'excl-tax'          => '不含税',
                        'move-to-wishlist'  => '移到心愿单',
                        'see-details'       => '查看详情',
                        'sku'               => 'SKU - :sku',
                        'sub-total'         => '小计 - :sub_total',
                        'title'             => '购物车商品',

                        'search' => [
                            'add-to-cart'   => '添加到购物车',
                            'available-qty' => ':qty 可用',
                            'empty-info'    => '没有可用于搜索词的产品。',
                            'empty-title'   => '未找到产品',
                            'product-image' => '产品图片',
                            'qty'           => '数量',
                            'sku'           => 'SKU - :sku',
                            'title'         => '搜索产品',
                        ],
                    ],

                    'address' => [
                        'add-btn'          => '添加地址',
                        'add-new'          => '添加新地址',
                        'add-new-address'  => '添加新地址',
                        'addresses'        => '地址',
                        'back'             => '返回',
                        'billing-address'  => '账单地址',
                        'city'             => '城市',
                        'company-name'     => '公司名称',
                        'confirm'          => '确认',
                        'country'          => '国家',
                        'edit-btn'         => '编辑地址',
                        'email'            => '电子邮件',
                        'first-name'       => '名字',
                        'last-name'        => '姓氏',
                        'postcode'         => '邮政编码',
                        'proceed'          => '继续',
                        'same-as-billing'  => '使用相同地址作为送货地址？',
                        'save'             => '保存',
                        'save-address'     => '保存到地址簿',
                        'select-country'   => '选择国家',
                        'select-state'     => '选择省/州',
                        'shipping-address' => '送货地址',
                        'state'            => '省/州',
                        'street-address'   => '街道地址',
                        'telephone'        => '电话',
                        'title'            => '地址',
                        'vat-id'           => '增值税号码',
                    ],

                    'payment' => [
                        'title' => '支付',
                    ],

                    'shipping' => [
                        'title' => '配送',
                    ],

                    'summary' => [
                        'apply-coupon'             => '应用优惠券',
                        'discount-amount'          => '折扣金额',
                        'enter-your-code'          => '输入您的代码',
                        'grand-total'              => '总计',
                        'place-order'              => '下单',
                        'processing'               => '处理中',
                        'shipping-amount-excl-tax' => '运费（不含税）',
                        'shipping-amount-incl-tax' => '运费（含税）',
                        'shipping-amount'          => '运费',
                        'sub-total-excl-tax'       => '小计（不含税）',
                        'sub-total-incl-tax'       => '小计（含税）',
                        'sub-total'                => '小计',
                        'tax'                      => '税费',
                        'title'                    => '订单摘要',
                    ],
                ],

                'cart-items' => [
                    'add-to-cart'       => '添加到购物车',
                    'delete'            => '删除',
                    'empty-description' => '购物车中没有找到任何商品。',
                    'empty-title'       => '购物车为空',
                    'excl-tax'          => '不含税',
                    'see-details'       => '查看详情',
                    'sku'               => 'SKU - :sku',
                    'title'             => '购物车商品',
                ],

                'recent-order-items' => [
                    'add-to-cart'       => '添加到购物车',
                    'empty-description' => '最近订单中没有找到任何商品。',
                    'empty-title'       => '订单为空',
                    'see-details'       => '查看详情',
                    'sku'               => 'SKU - :sku',
                    'title'             => '最近订单商品',
                    'view'              => '查看',
                ],

                'wishlist-items' => [
                    'add-to-cart'       => '添加到购物车',
                    'delete'            => '删除',
                    'empty-description' => '心愿单中没有找到任何商品。',
                    'empty-title'       => '心愿单为空',
                    'see-details'       => '查看详情',
                    'sku'               => 'SKU - :sku',
                    'title'             => '心愿单商品',
                ],

                'compare-items' => [
                    'add-to-cart'       => '添加到购物车',
                    'delete'            => '删除',
                    'empty-description' => '比较列表中没有找到任何商品。',
                    'empty-title'       => '比较列表为空',
                    'sku'               => 'SKU - :sku',
                    'title'             => '比较商品',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount 每单位 x :qty 数量',
                'billing-address'                => '账单地址',
                'cancel'                         => '取消',
                'cancel-msg'                     => '确定要取消此订单吗？',
                'cancel-success'                 => '订单取消成功',
                'canceled'                       => '已取消',
                'channel'                        => '渠道',
                'closed'                         => '已关闭',
                'comment-success'                => '评论添加成功。',
                'comments'                       => '评论',
                'completed'                      => '已完成',
                'contact'                        => '联系人',
                'create-success'                 => '订单创建成功',
                'currency'                       => '货币',
                'customer'                       => '客户',
                'customer-group'                 => '客户组',
                'customer-not-notified'          => ':date | 客户 <b>未通知</b>',
                'customer-notified'              => ':date | 客户 <b>已通知</b>',
                'discount'                       => '折扣 - :discount',
                'download-pdf'                   => '下载PDF',
                'fraud'                          => '欺诈',
                'grand-total'                    => '总计 - :grand_total',
                'invoice-id'                     => '发票号 #:invoice',
                'invoices'                       => '发票',
                'item-canceled'                  => '已取消 (:qty_canceled)',
                'item-invoice'                   => '已开票 (:qty_invoiced)',
                'item-ordered'                   => '已下单 (:qty_ordered)',
                'item-refunded'                  => '已退款 (:qty_refunded)',
                'item-shipped'                   => '已发货 (:qty_shipped)',
                'name'                           => '名称',
                'no-invoice-found'               => '未找到发票',
                'no-refund-found'                => '未找到退款',
                'no-shipment-found'              => '未找到发货',
                'notify-customer'                => '通知客户',
                'order-date'                     => '订单日期',
                'order-information'              => '订单信息',
                'order-status'                   => '订单状态',
                'payment-and-shipping'           => '付款和配送',
                'payment-method'                 => '付款方式',
                'pending'                        => '待处理',
                'pending_payment'                => '待付款',
                'per-unit'                       => '每单位',
                'price'                          => '价格 - :price',
                'price-excl-tax'                 => '价格（不含税） - :price',
                'price-incl-tax'                 => '价格（含税） - :price',
                'processing'                     => '处理中',
                'quantity'                       => '数量',
                'refund'                         => '退款',
                'refund-id'                      => '退款号 #:refund',
                'refunded'                       => '已退款',
                'reorder'                        => '重新下单',
                'ship'                           => '发货',
                'shipment'                       => '发货 #:shipment',
                'shipments'                      => '发货',
                'shipping-address'               => '配送地址',
                'shipping-and-handling'          => '配送和处理',
                'shipping-and-handling-excl-tax' => '配送和处理（不含税）',
                'shipping-and-handling-incl-tax' => '配送和处理（含税）',
                'shipping-method'                => '配送方式',
                'shipping-price'                 => '配送费用',
                'sku'                            => 'SKU - :sku',
                'status'                         => '状态',
                'sub-total'                      => '小计 - :sub_total',
                'sub-total-excl-tax'             => '小计（不含税） - :sub_total',
                'sub-total-incl-tax'             => '小计（含税） - :sub_total',
                'submit-comment'                 => '提交评论',
                'summary-discount'               => '折扣',
                'summary-grand-total'            => '总计',
                'summary-sub-total'              => '小计',
                'summary-sub-total-excl-tax'     => '小计（不含税）',
                'summary-sub-total-incl-tax'     => '小计（含税）',
                'summary-tax'                    => '税费',
                'tax'                            => '税费 (:percent) - :tax',
                'title'                          => '订单 #:order_id',
                'total-due'                      => '应付总额',
                'total-paid'                     => '已付总额',
                'total-refund'                   => '退款总额',
                'view'                           => '查看',
                'write-your-comment'             => '写下您的评论',
            ],
        ],

        'shipments' => [
            'index' => [
                'title' => '发货',

                'datagrid' => [
                    'id'               => 'ID',
                    'inventory-source' => '库存来源',
                    'order-date'       => '订单日期',
                    'order-id'         => '订单ID',
                    'shipment-date'    => '发货日期',
                    'shipment-to'      => '发货至',
                    'total-qty'        => '总数量',
                    'view'             => '查看',
                ],
            ],

            'create' => [
                'amount-per-unit'  => ':amount 每单位 x :qty 数量',
                'cancel-error'     => '无法取消订单',
                'carrier-name'     => '承运人名称',
                'create-btn'       => '创建发货',
                'creation-error'   => '发货创建出错',
                'item-canceled'    => '已取消 (:qty_canceled)',
                'item-invoice'     => '已开票 (:qty_invoiced)',
                'item-ordered'     => '已下单 (:qty_ordered)',
                'item-refunded'    => '已退款 (:qty_refunded)',
                'item-shipped'     => '已发货 (:qty_shipped)',
                'order-error'      => '发货无效',
                'per-unit'         => '每单位',
                'qty-available'    => '可用数量',
                'qty-to-ship'      => '待发货数量',
                'quantity-invalid' => '数量无效',
                'sku'              => 'SKU - :sku',
                'source'           => '来源',
                'success'          => '发货创建成功',
                'title'            => '创建新的发货',
                'tracking-number'  => '跟踪编号',
            ],

            'view' => [
                'billing-address'      => '账单地址',
                'carrier-title'        => '承运人标题',
                'channel'              => '渠道',
                'currency'             => '货币',
                'customer'             => '客户',
                'email'                => '电子邮件 - :email',
                'inventory-source'     => '库存来源',
                'order-date'           => '订单日期',
                'order-id'             => '订单ID',
                'order-information'    => '订单信息',
                'order-status'         => '订单状态',
                'ordered-items'        => '已下单项目',
                'payment-and-shipping' => '付款和发货',
                'payment-method'       => '付款方式',
                'product-image'        => '产品图片',
                'qty'                  => '数量 - :qty',
                'shipping-address'     => '发货地址',
                'shipping-method'      => '发货方式',
                'shipping-price'       => '发货价格',
                'sku'                  => 'SKU - :sku ',
                'title'                => '发货 #:shipment_id',
                'tracking-number'      => '跟踪编号',
            ],
        ],

        'refunds' => [
            'index' => [
                'title' => '退款',

                'datagrid' => [
                    'billed-to'       => '账单至',
                    'id'              => 'ID',
                    'order-id'        => '订单ID',
                    'refund-date'     => '退款日期',
                    'refunded-amount' => '退款金额',
                    'view'            => '查看',
                ],
            ],

            'view' => [
                'account-information'        => '账户信息',
                'adjustment-fee'             => '调整费用',
                'adjustment-refund'          => '调整退款',
                'base-discounted-amount'     => '基准折扣金额 - :base_discounted_amount',
                'billing-address'            => '账单地址',
                'currency'                   => '货币',
                'sub-total-amount-excl-tax'  => '小计金额（不含税） - :discounted_amount',
                'sub-total-amount-incl-tax'  => '小计金额（含税） - :discounted_amount',
                'sub-total-amount'           => '小计金额 - :discounted_amount',
                'grand-total'                => '总计',
                'order-channel'              => '订单渠道',
                'order-date'                 => '订单日期',
                'order-id'                   => '订单ID',
                'order-information'          => '订单信息',
                'order-status'               => '订单状态',
                'payment-information'        => '付款信息',
                'payment-method'             => '付款方式',
                'price-excl-tax'             => '价格（不含税） - :price',
                'price-incl-tax'             => '价格（含税） - :price',
                'price'                      => '价格 - :price',
                'product-image'              => '产品图片',
                'product-ordered'            => '已订购产品',
                'qty'                        => '数量 - :qty',
                'refund'                     => '退款',
                'shipping-address'           => '发货地址',
                'shipping-handling-excl-tax' => '配送和处理（不含税）',
                'shipping-handling-incl-tax' => '配送和处理（含税）',
                'shipping-handling'          => '配送和处理',
                'shipping-method'            => '配送方式',
                'shipping-price'             => '配送费用',
                'sku'                        => 'SKU - :sku',
                'sub-total-excl-tax'         => '小计（不含税）',
                'sub-total-incl-tax'         => '小计（含税）',
                'sub-total'                  => '小计',
                'tax'                        => '税费',
                'tax-amount'                 => '税费金额 - :tax_amount',
                'title'                      => '退款 #:refund_id',
            ],

            'create' => [
                'adjustment-fee'              => '调整费用',
                'adjustment-refund'           => '调整退款',
                'amount-per-unit'             => ':amount 每单位 x :qty 数量',
                'create-success'              => '退款创建成功',
                'creation-error'              => '退款创建不允许。',
                'discount-amount'             => '折扣金额',
                'grand-total'                 => '总计',
                'invalid-qty'                 => '我们发现有无效的数量要开票。',
                'invalid-refund-amount-error' => '退款金额应为非零。',
                'item-canceled'               => '已取消 (:qty_canceled)',
                'item-invoice'                => '已开票 (:qty_invoiced)',
                'item-ordered'                => '已下单 (:qty_ordered)',
                'item-refunded'               => '已退款 (:qty_refunded)',
                'item-shipped'                => '已发货 (:qty_shipped)',
                'per-unit'                    => '每单位',
                'price'                       => '价格',
                'qty-to-refund'               => '待退款数量',
                'refund-btn'                  => '退款',
                'refund-limit-error'          => '退款金额 :amount 无法进行。',
                'refund-shipping'             => '退款运费',
                'sku'                         => 'SKU - :sku',
                'subtotal'                    => '小计',
                'tax-amount'                  => '税额',
                'title'                       => '创建退款',
                'update-totals-btn'           => '更新总计',
            ],
        ],

        'invoices' => [
            'index' => [
                'title' => '发票',

                'datagrid' => [
                    'action'              => '操作',
                    'days-left'           => '还有 :count 天',
                    'days-overdue'        => '已逾期 :count 天',
                    'grand-total'         => '总计',
                    'id'                  => 'ID',
                    'invoice-date'        => '发票日期',
                    'mass-update-success' => '所选发票已成功更新。',
                    'order-id'            => '订单ID',
                    'overdue'             => '逾期',
                    'overdue-by'          => '逾期 :count 天',
                    'paid'                => '已支付',
                    'pending'             => '待支付',
                    'status'              => '状态',
                    'update-status'       => '更新状态',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount 每单位 x :qty 数量',
                'channel'                        => '渠道',
                'customer-email'                 => '邮箱 - :email',
                'customer'                       => '客户',
                'discount'                       => '折扣金额 - :discount',
                'email'                          => '邮箱',
                'grand-total'                    => '总计',
                'invoice-items'                  => '发票项目',
                'invoice-sent'                   => '发票发送成功',
                'invoice-status'                 => '发票状态',
                'order-date'                     => '订单日期',
                'order-id'                       => '订单ID',
                'order-information'              => '订单信息',
                'order-status'                   => '订单状态',
                'price-excl-tax'                 => '价格（不含税） - :price',
                'price-incl-tax'                 => '价格（含税） - :price',
                'price'                          => '价格 - :price',
                'print'                          => '打印',
                'product-image'                  => '产品图片',
                'qty'                            => '数量 - :qty',
                'send-btn'                       => '发送',
                'send-duplicate-invoice'         => '发送重复发票',
                'send'                           => '发送',
                'shipping-and-handling-excl-tax' => '配送和处理（不含税）',
                'shipping-and-handling-incl-tax' => '配送和处理（含税）',
                'shipping-and-handling'          => '配送和处理',
                'sku'                            => 'SKU - :sku',
                'sub-total-excl-tax'             => '小计（不含税） - :sub_total',
                'sub-total-incl-tax'             => '小计（含税） - :sub_total',
                'sub-total-summary-excl-tax'     => '小计（不含税）',
                'sub-total-summary-incl-tax'     => '小计（含税）',
                'sub-total-summary'              => '小计',
                'sub-total'                      => '小计 - :sub_total',
                'summary-discount'               => '折扣金额',
                'summary-tax'                    => '税费金额',
                'tax'                            => '税费金额 - :tax',
                'title'                          => '发票 #:invoice_id',
            ],

            'create' => [
                'amount-per-unit'    => ':amount 每单位 x :qty 数量',
                'create-invoice'     => '创建发票',
                'create-success'     => '发票创建成功',
                'create-transaction' => '创建交易',
                'creation-error'     => '不允许创建订单发票。',
                'invalid-qty'        => '我们发现无效的数量来开发票商品。',
                'invoice'            => '发票',
                'new-invoice'        => '新发票',
                'product-error'      => '无法没有产品创建发票。',
                'product-image'      => '产品图片',
                'qty-to-invoiced'    => '要开发票的数量',
                'sku'                => 'SKU - :sku',
            ],

            'invoice-pdf' => [
                'bank-details'               => '银行详细信息',
                'bill-to'                    => '账单给',
                'contact-number'             => '联系电话',
                'contact'                    => '联系人',
                'date'                       => '发票日期',
                'discount'                   => '折扣',
                'excl-tax'                   => '不含税：',
                'grand-total'                => '总计',
                'invoice-id'                 => '发票ID',
                'invoice'                    => '发票',
                'order-date'                 => '订单日期',
                'order-id'                   => '订单ID',
                'payment-method'             => '付款方式',
                'payment-terms'              => '付款条件',
                'price'                      => '价格',
                'product-name'               => '产品名称',
                'qty'                        => '数量',
                'ship-to'                    => '收货地址',
                'shipping-handling-excl-tax' => '运输和处理（不含税）',
                'shipping-handling-incl-tax' => '运输和处理（含税）',
                'shipping-handling'          => '运输和处理',
                'shipping-method'            => '运输方式',
                'sku'                        => 'SKU',
                'subtotal-excl-tax'          => '小计（不含税）',
                'subtotal-incl-tax'          => '小计（含税）',
                'subtotal'                   => '小计',
                'tax-amount'                 => '税费金额',
                'tax'                        => '税费',
                'vat-number'                 => '增值税号码',
            ],
        ],

        'invoice-transaction' => [
            'id'               => 'ID',
            'transaction-date' => '交易日期',
            'transaction-id'   => '交易ID',
            'view'             => '查看',
        ],

        'transactions' => [
            'index' => [
                'create-btn' => '创建交易',
                'title'      => '交易',

                'datagrid' => [
                    'completed'          => '已完成',
                    'id'                 => 'ID',
                    'invoice-id'         => '发票ID',
                    'order-id'           => '订单ID',
                    'paid'               => '已支付',
                    'pending'            => '待定',
                    'status'             => '状态',
                    'transaction-amount' => '金额',
                    'transaction-date'   => '日期',
                    'transaction-id'     => '交易ID',
                    'view'               => '查看',
                ],

                'create' => [
                    'already-paid'               => '已支付',
                    'amount'                     => '金额',
                    'create-transaction'         => '创建交易',
                    'invoice-id'                 => '发票号',
                    'invoice-missing'            => '发票丢失',
                    'payment-method'             => '付款方式',
                    'save-transaction'           => '保存交易',
                    'transaction-amount-exceeds' => '交易金额超过',
                    'transaction-amount-zero'    => '交易金额为零',
                    'transaction-saved'          => '交易已成功保存。',
                ],

                'view' => [
                    'amount'           => '金额',
                    'created-at'       => '创建时间',
                    'invoice-id'       => '发票 ID',
                    'order-id'         => '订单 ID',
                    'payment-details'  => '付款详情',
                    'payment-method'   => '付款方式',
                    'status'           => '状态',
                    'title'            => '交易详情',
                    'transaction-id'   => '交易 ID',
                ],
            ],
        ],

        'booking' => [
            'index' => [
                'datagrid' => [
                    'created-date' => '创建日期',
                    'from'         => '从',
                    'id'           => 'ID',
                    'order-id'     => '订单ID',
                    'qty'          => '数量',
                    'to'           => '至',
                    'view'         => '查看',
                ],

                'title'    => '预订',
            ],

            'calendar' => [
                'booking-date'     => '预订日期',
                'booking-details'  => '预订详情',
                'canceled'         => '已取消',
                'closed'           => '已关闭',
                'done'             => '已完成',
                'order-id'         => '订单ID',
                'pending'          => '待处理',
                'price'            => '价格',
                'status'           => '状态',
                'time-slot'        => '时间段：',
                'view-details'     => '查看详情',
            ],

            'title' => '预订产品',
        ],
    ],

    'catalog' => [
        'products' => [
            'index' => [
                'already-taken' => ':name 已经被占用。',
                'create-btn'    => '创建产品',
                'title'         => '产品',

                'create' => [
                    'back-btn'                => '返回',
                    'configurable-attributes' => '可配置属性',
                    'create-btn'              => '创建产品',
                    'family'                  => '家庭',
                    'save-btn'                => '保存产品',
                    'sku'                     => 'SKU',
                    'title'                   => '创建新产品',
                    'type'                    => '类型',
                ],

                'datagrid' => [
                    'active'                 => '激活',
                    'attribute-family'       => '属性家族',
                    'attribute-family-value' => '属性家族 - :attribute_family',
                    'category'               => '类别',
                    'channel'                => '渠道',
                    'copy-of'                => '复制 :value',
                    'copy-of-slug'           => '复制-:value',
                    'delete'                 => '删除',
                    'disable'                => '禁用',
                    'id'                     => 'ID',
                    'id-value'               => 'ID - :id',
                    'image'                  => '图像',
                    'mass-delete-success'    => '已成功删除所选产品',
                    'mass-update-success'    => '已成功更新所选产品',
                    'name'                   => '名称',
                    'out-of-stock'           => '缺货',
                    'price'                  => '价格',
                    'product-image'          => '产品图像',
                    'qty'                    => '数量',
                    'qty-value'              => ':qty 可用',
                    'sku'                    => 'SKU',
                    'sku-value'              => 'SKU - :sku',
                    'status'                 => '状态',
                    'type'                   => '类型',
                    'update-status'          => '更新状态',
                ],
            ],

            'edit' => [
                'preview'  => '预览',
                'remove'   => '删除',
                'save-btn' => '保存产品',
                'title'    => '编辑产品',

                'channels' => [
                    'title' => '渠道',
                ],

                'price' => [
                    'group' => [
                        'add-group-price'           => '添加组价格',
                        'all-groups'                => '所有群组',
                        'create-btn'                => '添加新的',
                        'discount-group-price-info' => '对于 :qty 个数量，折扣价格为 :price',
                        'edit-btn'                  => '编辑',
                        'empty-info'                => '针对属于特定组的客户的特殊定价。',
                        'fixed-group-price-info'    => '对于 :qty 个数量，固定价格为 :price',
                        'title'                     => '客户组价格',

                        'create' => [
                            'all-groups'     => '所有群组',
                            'create-title'   => '创建客户组价格',
                            'customer-group' => '客户组',
                            'delete-btn'     => '删除',
                            'discount'       => '折扣',
                            'fixed'          => '固定',
                            'price'          => '价格',
                            'price-type'     => '价格类型',
                            'qty'            => '最小数量',
                            'save-btn'       => '保存',
                            'update-title'   => '更新客户组价格',
                        ],
                    ],
                ],

                'inventories' => [
                    'pending-ordered-qty'      => '待发货数量: :qty',
                    'pending-ordered-qty-info' => '待发货数量将在发货后从相应的库存来源中扣减。在取消的情况下，待处理数量将可供销售。',
                    'title'                    => '库存',
                ],

                'categories' => [
                    'title' => '分类',
                ],

                'images' => [
                    'info'  => '图像分辨率应为560px X 609px',
                    'title' => '图片',
                ],

                'videos' => [
                    'error' => ':attribute 不能大于:max KB。请选择较小的文件。',
                    'title' => '视频',
                    'info'  => '最大视频尺寸应为 :size',
                ],

                'links' => [
                    'related-products' => [
                        'empty-info' => '在路上添加相关产品。',
                        'info'       => '除了客户当前查看的产品外，他们还会看到相关产品。',
                        'title'      => '相关产品',
                    ],

                    'up-sells' => [
                        'empty-info' => '在路上添加额外的销售产品。',
                        'info'       => '客户将看到上卖产品，这些产品是当前查看的产品的高级或更高质量的替代品。',
                        'title'      => '上卖产品',
                    ],

                    'cross-sells' => [
                        'empty-info' => '在路上添加交叉销售产品。',
                        'info'       => '在购物车旁边，您将找到这些“冲动购买”产品，它们被定位为与已经添加到购物车的商品相补充的交叉销售产品。',
                        'title'      => '交叉销售产品',
                    ],

                    'add-btn'           => '添加产品',
                    'delete'            => '删除',
                    'empty-info'        => '随时添加 :type 产品。',
                    'empty-title'       => '添加产品',
                    'image-placeholder' => '产品图片',
                    'sku'               => 'SKU - :sku',
                ],

                'types' => [
                    'simple' => [
                        'customizable-options' => [
                            'add-btn'           => '添加选项',
                            'empty-info'        => '快速创建可定制选项。',
                            'empty-title'       => '添加选项',
                            'info'              => '这将定制简单的产品。',
                            'title'             => '可定制项目',

                            'update-create' => [
                                'is-required'               => '是否必填',
                                'max-characters'            => '最大字符数',
                                'name'                      => '标题',
                                'no'                        => '否',
                                'price'                     => '价格',
                                'save-btn'                  => '保存',
                                'supported-file-extensions' => '支持的文件扩展名',
                                'title'                     => '选项',
                                'type'                      => '类型',
                                'yes'                       => '是',
                            ],

                            'option' => [
                                'add-btn'     => '添加选项',
                                'delete'      => '删除',
                                'delete-btn'  => '删除',
                                'edit-btn'    => '编辑',
                                'empty-info'  => '快速创建不同产品组合。',
                                'empty-title' => '添加选项',

                                'types' => [
                                    'text' => [
                                        'title' => '文本',
                                    ],

                                    'textarea' => [
                                        'title' => '文本区域',
                                    ],

                                    'checkbox' => [
                                        'title' => '复选框',
                                    ],

                                    'radio' => [
                                        'title' => '单选框',
                                    ],

                                    'select' => [
                                        'title' => '选择',
                                    ],

                                    'multiselect' => [
                                        'title' => '多选',
                                    ],

                                    'date' => [
                                        'title' => '日期',
                                    ],

                                    'datetime' => [
                                        'title' => '日期和时间',
                                    ],

                                    'time' => [
                                        'title' => '时间',
                                    ],

                                    'file' => [
                                        'title' => '文件',
                                    ],
                                ],

                                'items' => [
                                    'update-create' => [
                                        'label'    => '标签',
                                        'price'    => '价格',
                                        'save-btn' => '保存',
                                        'title'    => '选项',
                                    ],
                                ],
                            ],

                            'validations' => [
                                'associated-product' => '产品已关联到配置、组或捆绑产品。',
                            ],
                        ],
                    ],

                    'configurable' => [
                        'add-btn'           => '添加变体',
                        'delete-btn'        => '删除',
                        'edit-btn'          => '编辑',
                        'empty-info'        => '随时创建产品的各种组合。',
                        'empty-title'       => '添加变体',
                        'image-placeholder' => '产品图片',
                        'info'              => '变体产品取决于所有可能的属性组合。',
                        'qty'               => ':qty 个',
                        'sku'               => 'SKU - :sku',
                        'title'             => '变体',

                        'create' => [
                            'description'            => '描述',
                            'name'                   => '名称',
                            'save-btn'               => '添加',
                            'title'                  => '添加变体',
                            'variant-already-exists' => '此变体已存在',
                        ],

                        'edit' => [
                            'disabled'        => '禁用',
                            'edit-info'       => '如果您想详细更新产品信息，请转至',
                            'edit-link-title' => '产品详细信息页面',
                            'enabled'         => '启用',
                            'images'          => '图片',
                            'name'            => '名称',
                            'price'           => '价格',
                            'quantities'      => '数量',
                            'save-btn'        => '保存',
                            'sku'             => 'SKU',
                            'status'          => '状态',
                            'title'           => '产品',
                            'weight'          => '重量',
                        ],

                        'mass-edit' => [
                            'add-images'          => '添加图片',
                            'apply-to-all-btn'    => '应用于所有',
                            'apply-to-all-name'   => '将名称应用于所有变体。',
                            'apply-to-all-sku'    => '将价格应用于所有 SKU。',
                            'apply-to-all-status' => '将状态应用于所有变体。',
                            'apply-to-all-weight' => '对所有变体应用权重。',
                            'edit-inventories'    => '编辑库存',
                            'edit-names'          => '编辑名称',
                            'edit-prices'         => '编辑价格',
                            'edit-sku'            => '编辑 SKU',
                            'edit-status'         => '编辑状态',
                            'edit-weight'         => '编辑重量',
                            'name'                => '名称',
                            'price'               => '价格',
                            'remove-images'       => '移除图片',
                            'remove-variants'     => '移除变体',
                            'select-action'       => '选择操作',
                            'select-variants'     => '选择变体',
                            'status'              => '状态',
                            'variant-name'        => '变体名称',
                            'variant-sku'         => '变体 SKU',
                            'weight'              => '重量',
                        ],
                    ],

                    'grouped' => [
                        'add-btn'           => '添加产品',
                        'default-qty'       => '默认数量',
                        'delete'            => '删除',
                        'empty-info'        => '随时创建产品的各种组合。',
                        'empty-title'       => '添加产品',
                        'image-placeholder' => '产品图片',
                        'info'              => '分组产品包括独立销售的项目，作为一个集合呈现，允许按季节或主题进行变化或协调。每个产品可以单独购买，也可以作为该组的一部分购买。',
                        'sku'               => 'SKU - :sku',
                        'title'             => '组合产品',
                    ],

                    'bundle' => [
                        'add-btn'           => '添加选项',
                        'empty-info'        => '随时创建捆绑选项。',
                        'empty-title'       => '添加选项',
                        'image-placeholder' => '产品图片',
                        'info'              => '捆绑产品是一组多个项目或服务，以特价一起销售，为客户提供价值和便利。',
                        'title'             => '捆绑项目',

                        'update-create' => [
                            'checkbox'    => '复选框',
                            'is-required' => '必填',
                            'multiselect' => '多选框',
                            'name'        => '标题',
                            'no'          => '否',
                            'radio'       => '单选按钮',
                            'save-btn'    => '保存',
                            'select'      => '选择',
                            'title'       => '选项',
                            'type'        => '类型',
                            'yes'         => '是',
                        ],

                        'option' => [
                            'add-btn'     => '添加产品',
                            'default-qty' => '默认数量',
                            'delete'      => '删除',
                            'delete-btn'  => '删除',
                            'edit-btn'    => '编辑',
                            'empty-info'  => '随时创建产品的各种组合。',
                            'empty-title' => '添加产品',
                            'sku'         => 'SKU - :sku',

                            'types' => [
                                'checkbox' => [
                                    'info'  => '使用复选框设置默认产品',
                                    'title' => '复选框',
                                ],

                                'multiselect' => [
                                    'info'  => '使用复选框按钮设置默认产品',
                                    'title' => '多选框',
                                ],

                                'radio' => [
                                    'info'  => '使用单选按钮设置默认产品',
                                    'title' => '单选按钮',
                                ],

                                'select' => [
                                    'info'  => '使用单选按钮设置默认产品',
                                    'title' => '选择',
                                ],
                            ],
                        ],
                    ],

                    'booking' => [
                        'available-from' => '可用起始日期',
                        'available-to'   => '可用结束日期',
                        'location'       => '位置',
                        'qty'            => '数量',
                        'title'          => '预订类型',

                        'available-every-week' => [
                            'no'    => '否',
                            'title' => '每周可用',
                            'yes'   => '是',
                        ],

                        'appointment' => [
                            'break-duration' => '每个时段之间的休息时间（分钟）',
                            'slot-duration'  => '时段持续时间（分钟）',

                            'same-slot-for-all-days' => [
                                'no'    => '否',
                                'title' => '所有天数相同的时段',
                                'yes'   => '是',
                            ],
                        ],

                        'default' => [
                            'add'              => '添加',
                            'break-duration'   => '每个时段之间的休息时间（分钟）',
                            'close'            => '关闭',
                            'description'      => '预订信息',
                            'description-info' => '时间持续时间将根据时段创建并显示。它将在所有时段中是唯一的，并在商店前台可见。',
                            'edit'             => '编辑',
                            'many'             => '一天多次预订',
                            'one'              => '多天一次预订',
                            'open'             => '打开',
                            'slot-add'         => '添加时段',
                            'slot-duration'    => '时段持续时间（分钟）',
                            'slot-title'       => '时段时间持续时间',
                            'title'            => '默认',
                            'unavailable'      => '不可用',

                            'modal'            => [
                                'slot' => [
                                    'add-title'  => '添加时段',
                                    'close'      => '关闭',
                                    'day'        => '天',
                                    'edit-title' => '编辑时段',
                                    'friday'     => '星期五',
                                    'from'       => '从',
                                    'from-day'   => '从天',
                                    'from-time'  => '从时间',
                                    'monday'     => '星期一',
                                    'open'       => '打开',
                                    'saturday'   => '星期六',
                                    'save'       => '保存',
                                    'select'     => '选择',
                                    'status'     => '状态',
                                    'sunday'     => '星期天',
                                    'thursday'   => '星期四',
                                    'to'         => '到',
                                    'to-day'     => '到天',
                                    'to-time'    => '到时间',
                                    'tuesday'    => '星期二',
                                    'wednesday'  => '星期三',
                                    'week'       => ':day',
                                ],
                            ],
                        ],

                        'event' => [
                            'add'                => '添加票',
                            'delete'             => '删除',
                            'description'        => '描述',
                            'description-info'   => '没有可用的票。',
                            'edit'               => '编辑',
                            'name'               => '名称',
                            'price'              => '价格',
                            'qty'                => '数量',
                            'special-price'      => '特价',
                            'special-price-from' => '特价起始日期',
                            'special-price-to'   => '特价结束日期',
                            'title'              => '票',
                            'valid-from'         => '有效起始日期',
                            'valid-until'        => '有效结束日期',

                            'modal'              => [
                                'edit' => '编辑票',
                                'save' => '保存',
                            ],
                        ],

                        'empty-info' => [
                            'tickets' => [
                                'add' => '添加票',
                            ],

                            'slots'   => [
                                'add'         => '添加时段',
                                'description' => '可用时段和时间持续时间。',
                            ],
                        ],

                        'rental' => [
                            'daily'                  => '按天计费',
                            'daily-hourly'           => '按天和按小时计费',
                            'daily-price'            => '每日价格',
                            'hourly'                 => '按小时计费',
                            'hourly-price'           => '每小时价格',
                            'title'                  => '租赁类型',

                            'same-slot-for-all-days' => [
                                'no'    => '否',
                                'title' => '所有天数相同的时段',
                                'yes'   => '是',
                            ],
                        ],

                        'slots' => [
                            'add'              => '添加时段',
                            'description-info' => '时间持续时间将根据时段创建并显示。它将在所有时段中是唯一的，并在商店前台可见。',
                            'save'             => '保存',
                            'title'            => '时段时间持续时间',
                            'unavailable'      => '不可用',

                            'action'           => [
                                'add' => '添加',
                            ],

                            'modal'            => [
                                'slot' => [
                                    'friday'     => '星期五',
                                    'from'       => '从',
                                    'monday'     => '星期一',
                                    'saturday'   => '星期六',
                                    'sunday'     => '星期天',
                                    'thursday'   => '星期四',
                                    'to'         => '到',
                                    'tuesday'    => '星期二',
                                    'wednesday'  => '星期三',
                                ],
                            ],
                        ],

                        'table' => [
                            'break-duration'            => '每个时段之间的休息时间（分钟）',
                            'guest-capacity'            => '客人容量',
                            'guest-limit'               => '每桌客人限制',
                            'prevent-scheduling-before' => '防止提前安排',
                            'slot-duration'             => '时段持续时间（分钟）',

                            'charged-per'               => [
                                'guest'  => '客人',
                                'table'  => '桌子',
                                'title'  => '按每个收费',
                            ],

                            'same-slot-for-all-days'    => [
                                'no'    => '否',
                                'title' => '所有天数相同的时段',
                                'yes'   => '是',
                            ],
                        ],

                        'type' => [
                            'appointment' => '预约预订',
                            'default'     => '默认预订',
                            'event'       => '活动预订',
                            'many'        => '多个',
                            'one'         => '一个',
                            'rental'      => '租赁预订',
                            'table'       => '桌子预订',
                            'title'       => '类型',
                        ],
                    ],

                    'downloadable' => [
                        'links' => [
                            'add-btn'     => '添加链接',
                            'delete-btn'  => '删除',
                            'edit-btn'    => '编辑',
                            'empty-info'  => '随时创建链接。',
                            'empty-title' => '添加链接',
                            'file'        => '文件：',
                            'info'        => '可下载产品类型允许销售数字产品，如电子书，软件应用程序，音乐，游戏等。',
                            'sample-file' => '示例文件：',
                            'sample-url'  => '示例网址：',
                            'title'       => '可下载链接',
                            'url'         => '网址：',

                            'update-create' => [
                                'downloads'   => '允许下载',
                                'file'        => '文件',
                                'file-type'   => '文件类型',
                                'name'        => '标题',
                                'price'       => '价格',
                                'sample'      => '示例',
                                'sample-type' => '示例类型',
                                'save-btn'    => '保存',
                                'title'       => '链接',
                                'url'         => '网址',
                            ],
                        ],

                        'samples' => [
                            'add-btn'     => '添加示例',
                            'delete-btn'  => '删除',
                            'edit-btn'    => '编辑',
                            'empty-info'  => '随时创建示例。',
                            'empty-title' => '添加示例',
                            'file'        => '文件：',
                            'info'        => '可下载产品类型允许销售数字产品，如电子书，软件应用程序，音乐，游戏等。',
                            'title'       => '可下载示例',
                            'url'         => '网址：',

                            'update-create' => [
                                'file'        => '文件',
                                'file-type'   => '文件类型',
                                'name'        => '标题',
                                'save-btn'    => '保存',
                                'title'       => '链接',
                                'url'         => '网址',
                            ],
                        ],
                    ],
                ],
            ],

            'create-success'          => '产品创建成功',
            'delete-failed'           => '产品删除失败',
            'delete-success'          => '产品删除成功',
            'product-copied'          => '产品复制成功',
            'saved-inventory-message' => '产品保存成功',
            'update-success'          => '产品更新成功',
        ],

        'attributes' => [
            'index' => [
                'create-btn' => '创建属性',
                'title'      => '属性',

                'datagrid' => [
                    'boolean'             => '布尔值',
                    'channel-based'       => '基于频道',
                    'checkbox'            => '复选框',
                    'code'                => '代码',
                    'created-at'          => '创建时间',
                    'date'                => '日期',
                    'date-time'           => '日期时间',
                    'delete'              => '删除',
                    'edit'                => '编辑',
                    'false'               => '假',
                    'file'                => '文件',
                    'id'                  => '标识',
                    'image'               => '图片',
                    'locale-based'        => '基于区域设置',
                    'mass-delete-success' => '已成功删除所选属性',
                    'multiselect'         => '多选',
                    'name'                => '名称',
                    'price'               => '价格',
                    'required'            => '必填',
                    'select'              => '选择',
                    'text'                => '文本',
                    'textarea'            => '文本区域',
                    'true'                => '真',
                    'type'                => '类型',
                    'unique'              => '唯一',
                ],
            ],

            'create' => [
                'add-attribute-options' => '添加属性选项',
                'add-option'            => '添加选项',
                'add-options-info'      => '快速创建各种属性选项组合。',
                'add-row'               => '添加行',
                'admin'                 => '管理员',
                'admin-name'            => '管理员名称',
                'back-btn'              => '返回',
                'boolean'               => '布尔',
                'checkbox'              => '复选框',
                'code'                  => '属性编码',
                'color'                 => '颜色',
                'configuration'         => '配置',
                'create-empty-option'   => '创建默认空选项',
                'date'                  => '日期',
                'datetime'              => '日期时间',
                'decimal'               => '小数',
                'default-value'         => '默认值',
                'email'                 => '电子邮件',
                'enable-wysiwyg'        => '启用所见即所得编辑器',
                'file'                  => '文件',
                'general'               => '常规',
                'image'                 => '图片',
                'input-options'         => '输入选项',
                'input-validation'      => '输入验证',
                'is-comparable'         => '可比较的属性',
                'is-configurable'       => '用于创建可配置产品',
                'is-filterable'         => '用于分层导航',
                'is-required'           => '必填',
                'is-unique'             => '唯一',
                'is-visible-on-front'   => '在前端产品视图页面上可见',
                'label'                 => '标签',
                'multiselect'           => '多选',
                'no'                    => '否',
                'numeric'               => '数字',
                'option-deleted'        => '选项删除成功',
                'options'               => '选项',
                'position'              => '位置',
                'price'                 => '价格',
                'regex'                 => '正则表达式',
                'regex-info'            => '表达式应该用双引号括起来。',
                'save-btn'              => '保存属性',
                'select'                => '选择',
                'select-type'           => '选择属性类型',
                'swatch'                => '颜色样本',
                'text'                  => '文本',
                'textarea'              => '文本区域',
                'title'                 => '添加属性',
                'type'                  => '属性类型',
                'url'                   => 'URL',
                'use-in-flat'           => '在产品平坦表中创建',
                'validations'           => '验证',
                'value-per-channel'     => '渠道性值',
                'value-per-locale'      => '区域性值',
                'yes'                   => '是',

                'option' => [
                    'color'    => '颜色样本',
                    'dropdown' => '下拉菜单',
                    'image'    => '图片样本',
                    'save-btn' => '保存选项',
                    'text'     => '文本样本',
                ],
            ],

            'edit' => [
                'add-attribute-options' => '添加属性选项',
                'add-option'            => '添加选项',
                'add-options-info'      => '快速创建各种属性选项组合。',
                'add-row'               => '添加行',
                'admin'                 => '管理员',
                'admin-name'            => '管理员名称',
                'back-btn'              => '返回',
                'boolean'               => '布尔',
                'checkbox'              => '复选框',
                'code'                  => '属性编码',
                'color'                 => '颜色',
                'configuration'         => '配置',
                'create-empty-option'   => '创建默认空选项',
                'date'                  => '日期',
                'datetime'              => '日期时间',
                'decimal'               => '小数',
                'default-value'         => '默认值',
                'email'                 => '电子邮件',
                'enable-wysiwyg'        => '启用所见即所得编辑器',
                'file'                  => '文件',
                'general'               => '常规',
                'image'                 => '图片',
                'input-options'         => '输入选项',
                'input-validation'      => '输入验证',
                'is-comparable'         => '可比较的属性',
                'is-configurable'       => '用于创建可配置产品',
                'is-filterable'         => '用于分层导航',
                'is-required'           => '必填',
                'is-unique'             => '唯一',
                'is-visible-on-front'   => '在前端产品视图页面上可见',
                'label'                 => '标签',
                'multiselect'           => '多选',
                'no'                    => '否',
                'numeric'               => '数字',
                'option-deleted'        => '选项删除成功',
                'options'               => '选项',
                'position'              => '位置',
                'price'                 => '价格',
                'regex'                 => '正则表达式',
                'regex-info'            => '表达式应该用双引号括起来。',
                'save-btn'              => '保存属性',
                'select'                => '选择',
                'select-type'           => '选择属性类型',
                'swatch'                => '颜色样本',
                'text'                  => '文本',
                'textarea'              => '文本区域',
                'title'                 => '编辑属性',
                'type'                  => '属性类型',
                'url'                   => 'URL',
                'use-in-flat'           => '在产品平坦表中创建',
                'validations'           => '验证',
                'value-per-channel'     => '渠道性值',
                'value-per-locale'      => '区域性值',
                'yes'                   => '是',

                'option' => [
                    'color'    => '颜色样本',
                    'dropdown' => '下拉菜单',
                    'image'    => '图片样本',
                    'save-btn' => '保存选项',
                    'text'     => '文本样本',
                ],
            ],

            'create-success'    => '属性创建成功',
            'delete-failed'     => '属性删除失败',
            'delete-success'    => '属性删除成功',
            'update-success'    => '属性更新成功',
            'user-define-error' => '无法删除系统属性',
        ],

        'categories' => [
            'index' => [
                'add-btn' => '创建分类',
                'title'   => '分类',

                'datagrid' => [
                    'active'         => '激活',
                    'delete'         => '删除',
                    'delete-success' => '已成功删除所选 :resource',
                    'edit'           => '编辑',
                    'id'             => 'ID',
                    'inactive'       => '禁用',
                    'name'           => '名称',
                    'no-of-products' => '产品数量',
                    'position'       => '位置',
                    'status'         => '菜单可见',
                    'update-status'  => '更新状态',
                ],
            ],

            'create' => [
                'add-banner'               => '添加横幅',
                'add-logo'                 => '添加徽标',
                'back-btn'                 => '返回',
                'banner'                   => '横幅',
                'banner-size'              => '横幅尺寸（1320px X 300px）',
                'company-name'             => '名称',
                'description'              => '描述',
                'description-and-images'   => '描述和图片',
                'description-only'         => '仅描述',
                'display-mode'             => '显示模式',
                'enter-position'           => '输入位置',
                'filterable-attributes'    => '可筛选属性',
                'general'                  => '常规',
                'logo'                     => '徽标',
                'logo-size'                => '徽标分辨率应为（110px X 110px）',
                'meta-description'         => 'Meta 描述',
                'meta-keywords'            => 'Meta 关键词',
                'meta-title'               => 'Meta 标题',
                'parent-category'          => '父级分类',
                'position'                 => '位置',
                'products-and-description' => '产品和描述',
                'products-only'            => '仅产品',
                'save-btn'                 => '保存分类',
                'select-display-mode'      => '选择显示模式',
                'seo-details'              => 'SEO 详情',
                'settings'                 => '设置',
                'slug'                     => 'Slug',
                'title'                    => '创建新分类',
                'visible-in-menu'          => '在菜单中可见',
            ],

            'edit' => [
                'add-banner'               => '添加横幅',
                'add-logo'                 => '添加徽标',
                'back-btn'                 => '返回',
                'banner'                   => '横幅',
                'banner-size'              => '横幅尺寸（1320px X 300px）',
                'company-name'             => '名称*',
                'description'              => '描述',
                'description-and-images'   => '描述和图片',
                'description-only'         => '仅描述',
                'display-mode'             => '显示模式',
                'enter-position'           => '输入位置',
                'filterable-attributes'    => '可筛选属性',
                'general'                  => '常规',
                'logo'                     => '徽标',
                'logo-size'                => '徽标分辨率应为（110px X 110px）',
                'meta-description'         => 'Meta 描述',
                'meta-keywords'            => 'Meta 关键词',
                'meta-title'               => 'Meta 标题',
                'position'                 => '位置*',
                'products-and-description' => '产品和描述',
                'products-only'            => '仅产品',
                'save-btn'                 => '保存分类',
                'select-display-mode'      => '选择显示模式',
                'select-parent-category'   => '选择父级分类*',
                'seo-details'              => 'SEO 详情',
                'settings'                 => '设置',
                'slug'                     => 'Slug',
                'title'                    => '编辑分类',
                'visible-in-menu'          => '在菜单中可见',
            ],

            'category'             => '分类',
            'create-success'       => '分类创建成功。',
            'delete-category-root' => '无法删除根类别 ',
            'delete-failed'        => '删除类别时发生错误',
            'delete-success'       => '分类已成功删除。',
            'update-success'       => '分类更新成功。',
        ],

        'families' => [
            'index' => [
                'add'   => '创建属性家族',
                'title' => '属性家族',

                'datagrid' => [
                    'code'           => '代码',
                    'delete'         => '删除',
                    'delete-success' => '已成功删除所选 :resource',
                    'edit'           => '编辑',
                    'id'             => 'ID',
                    'method-error'   => '错误！检测到错误的方法，请检查批量操作配置',
                    'name'           => '名称',
                    'no-resource'    => '提供的资源不足以执行操作',
                    'partial-action' => '由于系统约束限制，某些操作未执行',
                    'update-success' => '已成功更新所选 :resource',
                ],
            ],

            'create' => [
                'add-group-btn'                    => '添加分组',
                'add-group-title'                  => '添加新分组',
                'back-btn'                         => '返回',
                'code'                             => '代码',
                'column'                           => '列',
                'delete-group-btn'                 => '删除分组',
                'edit-group-info'                  => '双击编辑分组',
                'enter-code'                       => '输入代码',
                'enter-name'                       => '输入名称',
                'general'                          => '常规',
                'group-code-already-exists'        => '属性组代码已经存在。',
                'group-contains-system-attributes' => '此分组包含系统属性。请先将系统属性移至其他分组，然后重试。',
                'group-name-already-exists'        => '已存在属性分组名称。',
                'groups'                           => '分组',
                'groups-info'                      => '管理属性家族分组',
                'main-column'                      => '主列',
                'name'                             => '名称',
                'removal-not-possible'             => '您无法从属性家族中移除系统属性。',
                'right-column'                     => '右侧列',
                'save-btn'                         => '保存属性家族',
                'select-group'                     => '请选择属性分组。',
                'title'                            => '创建属性家族',
                'unassigned-attributes'            => '未分配的属性',
                'unassigned-attributes-info'       => '拖动这些属性以添加到列或分组中。',
            ],

            'edit' => [
                'add-group-btn'                    => '添加分组',
                'add-group-title'                  => '添加新分组',
                'back-btn'                         => '返回',
                'code'                             => '代码',
                'column'                           => '列',
                'delete-group-btn'                 => '删除分组',
                'edit-group-info'                  => '双击编辑分组',
                'enter-code'                       => '输入代码',
                'enter-name'                       => '输入名称',
                'general'                          => '一般的',
                'group-code-already-exists'        => '属性组代码已经存在。',
                'group-contains-system-attributes' => '此分组包含系统属性。请先将系统属性移至其他分组，然后重试。',
                'group-name-already-exists'        => '已存在属性分组名称。',
                'groups'                           => '分组',
                'groups-info'                      => '管理属性家族分组',
                'main-column'                      => '主列',
                'name'                             => '名称',
                'removal-not-possible'             => '您无法从属性家族中移除系统属性。',
                'right-column'                     => '右侧列',
                'save-btn'                         => '保存属性家族',
                'select-group'                     => '请选择属性分组。',
                'title'                            => '编辑属性家族',
                'unassigned-attributes'            => '未分配的属性',
                'unassigned-attributes-info'       => '拖动这些属性以添加到列或分组中。',
            ],

            'attribute-family'        => '属性家族',
            'attribute-product-error' => '家族在产品中使用。',
            'create-success'          => '成功创建家族。',
            'delete-failed'           => '删除家族时遇到错误。',
            'delete-success'          => '成功删除家族。',
            'family'                  => '属性家族',
            'last-delete-error'       => '至少需要一个家族。',
            'update-success'          => '成功更新家族。',
            'user-define-error'       => '无法删除系统属性家族。',
        ],
    ],

    'customers' => [
        'customers' => [
            'index' => [
                'title'         => '客户',
                'login-message' => '您以 :customer_name 的身份登录',

                'datagrid' => [
                    'active'         => '活跃',
                    'address'        => ':address  地址',
                    'address-count'  => '地址数量',
                    'channel'        => '渠道',
                    'delete'         => '删除',
                    'delete-success' => '成功删除所选数据',
                    'email'          => '电子邮件',
                    'gender'         => '性别',
                    'group'          => '分组',
                    'id'             => '客户 ID',
                    'id-value'       => 'ID - :id',
                    'inactive'       => '不活跃',
                    'method-error'   => '错误！检测到错误的方法，请检查大规模操作配置',
                    'name'           => '客户姓名',
                    'no-resource'    => '提供的资源不足以执行操作',
                    'order'          => ':order 订单',
                    'order-count'    => '订单数量',
                    'order-pending'  => '客户有待处理订单',
                    'partial-action' => '由于 :resource 上的受限制的系统约束，未执行一些操作',
                    'phone'          => '联系号码',
                    'revenue'        => '收入',
                    'status'         => '状态',
                    'suspended'      => '已暂停',
                    'update-status'  => '更新状态',
                    'update-success' => '成功更新所选客户',
                ],

                'create' => [
                    'contact-number'        => '联系号码',
                    'create-btn'            => '创建客户',
                    'create-success'        => '成功创建客户',
                    'customer-group'        => '客户组',
                    'date-of-birth'         => '出生日期',
                    'email'                 => '电子邮件',
                    'female'                => '女性',
                    'first-name'            => '名字',
                    'gender'                => '性别',
                    'last-name'             => '姓氏',
                    'male'                  => '男性',
                    'other'                 => '其他',
                    'save-btn'              => '保存客户',
                    'select-customer-group' => '选择客户组',
                    'select-gender'         => '选择性别',
                    'title'                 => '创建新客户',
                ],
            ],

            'view' => [
                'account-delete-confirmation' => '您确定要删除此账户吗？',
                'active'                      => '活跃',
                'address-delete-confirmation' => '您确定要删除此地址吗？',
                'back-btn'                    => '返回',
                'create-order'                => '创建订单',
                'customer'                    => '客户',
                'date-of-birth'               => '生日 - :dob',
                'default-address'             => '默认地址',
                'delete-account'              => '删除账户',
                'delete'                      => '删除',
                'email'                       => '电子邮件 - :email',
                'empty-description'           => '为客户创建新地址',
                'empty-title'                 => '添加客户地址',
                'gender'                      => '性别 - :gender',
                'group'                       => '分组 - :group_code',
                'inactive'                    => '不活跃',
                'login-as-customer'           => '以客户身份登录',
                'note-created-success'        => '成功创建备注',
                'order-create-confirmation'   => '您确定要为此客户创建订单吗?',
                'phone'                       => '电话 - :phone',
                'set-as-default'              => '设为默认',
                'suspended'                   => '已暂停',
                'title'                       => '客户视图',

                'address' => [
                    'count' => '地址 (:count)',

                    'create' => [
                        'city'               => '城市',
                        'company-name'       => '公司名称',
                        'country'            => '国家',
                        'create-btn'         => '创建',
                        'create-address-btn' => '添加新地址',
                        'default-address'    => '默认地址',
                        'email'              => '电子邮件',
                        'first-name'         => '名字',
                        'last-name'          => '姓氏',
                        'phone'              => '电话',
                        'post-code'          => '邮政编码',
                        'save-btn-title'     => '保存地址',
                        'select-country'     => '选择国家',
                        'state'              => '州',
                        'street-address'     => '街道地址',
                        'title'              => '创建地址',
                        'vat-id'             => 'VAT 号码',
                    ],

                    'edit' => [
                        'city'            => '城市',
                        'company-name'    => '公司名称',
                        'country'         => '国家',
                        'default-address' => '默认地址',
                        'edit-btn'        => '编辑',
                        'email'           => '电子邮件',
                        'first-name'      => '名字',
                        'last-name'       => '姓氏',
                        'phone'           => '电话',
                        'post-code'       => '邮政编码',
                        'save-btn-title'  => '保存地址',
                        'select-country'  => '选择国家',
                        'state'           => '州',
                        'street-address'  => '街道地址',
                        'title'           => '编辑地址',
                        'vat-id'          => 'VAT 号码',
                    ],

                    'address-delete-success' => '成功删除地址',
                    'create-success'         => '成功创建地址',
                    'set-default-success'    => '默认地址更新成功',
                    'success-mass-delete'    => '成功进行地址批量删除',
                    'update-success'         => '成功更新地址',
                ],

                'datagrid' => [
                    'invoices' => [
                        'empty-invoice'  => '没有评论',
                        'increment-id'   => '发票 ID',
                        'invoice-amount' => '发票金额',
                        'invoice-date'   => '发票日期',
                        'order-id'       => '订单 ID',
                        'view'           => '查看',
                    ],

                    'orders' => [
                        'canceled'        => '已取消',
                        'channel-name'    => '渠道名称',
                        'closed'          => '已关闭',
                        'completed'       => '已完成',
                        'customer-name'   => '客户姓名',
                        'date'            => '日期',
                        'empty-order'     => '没有订单',
                        'email'           => '电子邮件',
                        'fraud'           => '欺诈',
                        'grand-total'     => '总计',
                        'location'        => '位置',
                        'order-id'        => '订单 ID',
                        'pay-via'         => '支付方式',
                        'pending'         => '待处理',
                        'pending-payment' => '待付款',
                        'processing'      => '处理中',
                        'status'          => '状态',
                        'view'            => '查看',
                    ],

                    'reviews' => [
                        'approved'      => '已批准',
                        'comment'       => '评论',
                        'created-at'    => '创建于',
                        'disapproved'   => '未批准',
                        'empty-reviews' => '没有发票',
                        'id'            => 'ID',
                        'invoice-date'  => '发票日期',
                        'pending'       => '待处理',
                        'product-id'    => '产品 ID',
                        'product-name'  => '产品名称',
                        'rating'        => '评分',
                        'status'        => '状态',
                        'title'         => '标题',
                    ],
                ],

                'edit' => [
                    'contact-number'        => '联系号码',
                    'customer-group'        => '客户组',
                    'date-of-birth'         => '出生日期',
                    'edit-btn'              => '编辑',
                    'email'                 => '电子邮件',
                    'female'                => '女性',
                    'first-name'            => '名字',
                    'gender'                => '性别',
                    'last-name'             => '姓氏',
                    'male'                  => '男性',
                    'other'                 => '其他',
                    'save-btn'              => '保存客户',
                    'select-customer-group' => '选择客户组',
                    'select-gender'         => '选择性别',
                    'status'                => '状态',
                    'suspended'             => '已暂停',
                    'title'                 => '编辑客户',
                ],

                'invoices' => [
                    'count'        => '发票 (:count)',
                    'increment-id' => '# :increment_id',
                ],

                'notes' => [
                    'add-note'              => '添加备注',
                    'customer-not-notified' => ':date | 客户 <b>未通知</b>',
                    'customer-notified'     => ':date | 客户 <b>已通知</b>',
                    'note'                  => '备注',
                    'note-placeholder'      => '在这里写下您的备注',
                    'notify-customer'       => '通知客户',
                    'submit-btn-title'      => '提交备注',
                ],

                'orders' => [
                    'count'         => '订单 (:count)',
                    'increment-id'  => '# :increment_id',
                    'total-revenue' => '总收入 - :revenue',
                ],

                'reviews' => [
                    'id'    => 'ID - :id',
                    'count' => '评论 (:count)',
                ],

                'cart' => [
                    'delete-success' => '成功删除购物车商品。',
                ],

                'wishlist' => [
                    'delete-success' => '成功删除心愿单商品。',
                ],

                'compare' => [
                    'delete-success' => '成功删除对比商品。',
                ],
            ],

            'delete-failed'  => '删除客户失败',
            'delete-success' => '成功删除客户',
            'order-pending'  => '订单待处理',
            'update-success' => '成功更新客户',
        ],

        'groups' => [
            'index' => [
                'title' => '分组',

                'create' => [
                    'code'       => '代码',
                    'create-btn' => '创建分组',
                    'name'       => '名称',
                    'save-btn'   => '保存分组',
                    'success'    => '分组创建成功',
                    'title'      => '创建新分组',
                ],

                'edit' => [
                    'delete-failed'  => '分组删除失败',
                    'delete-success' => '分组删除成功',
                    'group-default'  => '默认分组无法删除',
                    'success'        => '分组更新成功',
                    'title'          => '编辑分组',
                ],

                'datagrid' => [
                    'code'   => '代码',
                    'delete' => '删除',
                    'edit'   => '编辑',
                    'id'     => 'ID',
                    'name'   => '名称',
                ],
            ],
        ],

        'gdpr' => [
            'index' => [
                'title' => 'GDPR 请求',

                'datagrid' => [
                    'completed'     => '已完成',
                    'created-at'    => '创建时间',
                    'customer-name' => '客户名称',
                    'declined'      => '已拒绝',
                    'delete'        => '删除',
                    'edit'          => '编辑',
                    'id'            => 'ID',
                    'message'       => '消息',
                    'pending'       => '待处理',
                    'processing'    => '处理中',
                    'revoked'       => '已撤销',
                    'status'        => '状态',
                    'type'          => '类型',
                ],

                'modal' => [
                    'completed'     => '已完成',
                    'declined'      => '已拒绝',
                    'message'       => '消息',
                    'pending'       => '待处理',
                    'processing'    => '处理中',
                    'revoked'       => '已撤销',
                    'save-btn'      => '保存',
                    'status'        => '状态',
                    'title'         => '编辑 GDPR 数据请求',
                    'type'          => '类型',
                ],

                'update-success'              => '数据请求更新成功并已发送邮件给客户。',
                'delete-success'              => '数据请求删除成功。',
                'attribute-reason-error'      => '无法删除。',
                'update-success-unsent-email' => '数据请求更新成功但邮件未发送给客户。',
            ],
        ],

        'reviews' => [
            'index' => [
                'date'        => '日期',
                'description' => '描述',
                'id'          => 'ID',
                'name'        => '姓名',
                'product'     => '产品',
                'rating'      => '评分',
                'status'      => '状态',
                'title'       => '评论',

                'edit' => [
                    'approved'       => '已批准',
                    'customer'       => '顾客',
                    'date'           => '日期',
                    'disapproved'    => '未批准',
                    'id'             => 'ID',
                    'images'         => '图片',
                    'pending'        => '待处理',
                    'product'        => '产品',
                    'rating'         => '评分',
                    'review-comment' => '评论',
                    'review-title'   => '标题',
                    'save-btn'       => '保存',
                    'status'         => '状态',
                    'title'          => '编辑评论',
                    'update-success' => '更新成功',
                ],

                'datagrid' => [
                    'approved'            => '已批准',
                    'comment'             => '评论',
                    'customer-names'      => '姓名',
                    'date'                => '日期',
                    'delete'              => '删除',
                    'delete-success'      => '评论删除成功',
                    'disapproved'         => '未批准',
                    'edit'                => '编辑',
                    'id'                  => 'ID',
                    'mass-delete-error'   => '出现了一些问题',
                    'mass-delete-success' => '所选评论删除成功',
                    'mass-update-success' => '所选评论更新成功',
                    'pending'             => '待处理',
                    'product'             => '产品',
                    'rating'              => '评分',
                    'review-id'           => 'ID - :review_id',
                    'status'              => '状态',
                    'title'               => '标题',
                    'update-status'       => '更新状态',
                ],
            ],
        ],
    ],

    'marketing' => [
        'communications' => [
            'templates' => [
                'index' => [
                    'create-btn' => '创建模板',
                    'title'      => '电子邮件模板',

                    'datagrid' => [
                        'active'   => '激活',
                        'draft'    => '草稿',
                        'id'       => 'ID',
                        'inactive' => '未激活',
                        'name'     => '名称',
                        'status'   => '状态',
                    ],
                ],

                'create' => [
                    'active'         => '激活',
                    'back-btn'       => '返回',
                    'content'        => '内容',
                    'create-success' => '电子邮件模板创建成功。',
                    'draft'          => '草稿',
                    'general'        => '常规',
                    'inactive'       => '未激活',
                    'name'           => '名称',
                    'save-btn'       => '保存模板',
                    'select-status'  => '选择状态',
                    'status'         => '状态',
                    'title'          => '创建模板',
                ],

                'edit' => [
                    'active'         => '激活',
                    'back-btn'       => '返回',
                    'content'        => '内容*',
                    'draft'          => '草稿',
                    'general'        => '常规',
                    'inactive'       => '未激活',
                    'name'           => '名称',
                    'save-btn'       => '保存模板',
                    'status'         => '状态',
                    'title'          => '编辑模板',
                    'update-success' => '更新成功',
                ],

                'delete-failed'  => ':name 删除失败',
                'delete-success' => '模板删除成功',
                'email-template' => '电子邮件模板',
            ],

            'campaigns' => [
                'index' => [
                    'create-btn' => '创建活动',
                    'title'      => '活动',

                    'datagrid' => [
                        'active'   => '激活',
                        'delete'   => '删除',
                        'edit'     => '编辑',
                        'id'       => 'ID',
                        'inactive' => '未激活',
                        'name'     => '名称',
                        'status'   => '状态',
                        'subject'  => '主题',
                    ],
                ],

                'create' => [
                    'active'          => '激活',
                    'back-btn'        => '返回',
                    'channel'         => '渠道',
                    'customer-group'  => '顾客组',
                    'email-template'  => '电子邮件模板',
                    'event'           => '事件',
                    'general'         => '常规',
                    'inactive'        => '未激活',
                    'name'            => '名称',
                    'save-btn'        => '保存活动',
                    'select-channel'  => '选择频道',
                    'select-event'    => '选择事件',
                    'select-group'    => '选择群组',
                    'select-status'   => '选择状态',
                    'select-template' => '选择模板',
                    'setting'         => '设置',
                    'status'          => '状态',
                    'subject'         => '主题',
                    'title'           => '创建活动',
                ],

                'edit' => [
                    'active'          => '激活',
                    'audience'        => '受众',
                    'back-btn'        => '返回',
                    'channel'         => '渠道',
                    'customer-group'  => '顾客组',
                    'email-template'  => '电子邮件模板',
                    'event'           => '事件',
                    'general'         => '常规',
                    'inactive'        => '未激活',
                    'name'            => '名称',
                    'save-btn'        => '保存活动',
                    'select-event'    => '选择事件',
                    'select-status'   => '选择状态',
                    'select-template' => '选择模板',
                    'status'          => '状态',
                    'subject'         => '主题',
                    'title'           => '编辑活动',
                ],

                'create-success' => '活动创建成功。',
                'delete-failed'  => ':name 删除失败',
                'delete-success' => '活动删除成功',
                'email-campaign' => '电子邮件活动',
                'update-success' => '活动更新成功。',
            ],

            'events' => [
                'index' => [
                    'create-btn' => '创建事件',
                    'event'      => '事件',
                    'title'      => '事件',

                    'datagrid' => [
                        'actions' => '操作',
                        'date'    => '日期',
                        'delete'  => '删除',
                        'edit'    => '编辑',
                        'id'      => 'ID',
                        'name'    => '名称',
                    ],

                    'create' => [
                        'date'           => '日期',
                        'delete-warning' => '您确定要执行此操作吗？',
                        'description'    => '描述',
                        'general'        => '常规',
                        'name'           => '名称',
                        'save-btn'       => '保存事件',
                        'success'        => '事件创建成功',
                        'title'          => '创建事件',
                    ],

                    'edit' => [
                        'success' => '事件更新成功',
                        'title'   => '编辑事件',
                    ],
                ],

                'delete-failed'  => ':name 删除失败',
                'delete-success' => '事件删除成功',
                'edit-error'     => '事件无法编辑',
            ],

            'subscribers' => [
                'index' => [
                    'title' => '通讯订阅',

                    'datagrid' => [
                        'actions'    => '操作',
                        'delete'     => '删除',
                        'edit'       => '编辑',
                        'email'      => '电子邮件',
                        'false'      => '否',
                        'id'         => 'ID',
                        'subscribed' => '已订阅',
                        'true'       => '是',
                    ],

                    'edit' => [
                        'back-btn'      => '返回',
                        'email'         => '电子邮件',
                        'false'         => '否',
                        'save-btn'      => '保存订阅者',
                        'subscribed'    => '已订阅',
                        'success'       => '通讯订阅更新成功',
                        'title'         => '编辑通讯订阅者',
                        'true'          => '是',
                        'update-failed' => '通讯订阅未更新',
                    ],
                ],

                'delete-failed'  => '订阅者删除失败',
                'delete-success' => '订阅者删除成功',
                'delete-warning' => '您确定要执行此操作吗？',
            ],
        ],

        'promotions' => [
            'index' => [
                'cart-rule-title'    => '购物车规则',
                'catalog-rule-title' => '产品目录规则',
            ],

            'cart-rules' => [
                'index' => [
                    'create-btn' => '创建购物车规则',
                    'title'      => '购物车规则',

                    'datagrid' => [
                        'active'      => '活动',
                        'copy'        => '复制z',
                        'copy-of'     => ':value',
                        'coupon-code' => '优惠券码',
                        'delete'      => '删除',
                        'draft'       => '草稿',
                        'edit'        => '编辑',
                        'end'         => '结束',
                        'id'          => 'ID',
                        'inactive'    => '不活动',
                        'name'        => '名称',
                        'priority'    => '优先级',
                        'start'       => '开始',
                        'status'      => '状态',
                    ],
                ],

                'create' => [
                    'action-type'                               => '操作类型',
                    'actions'                                   => '操作',
                    'add-condition'                             => '添加条件',
                    'additional'                                => '附加',
                    'all-conditions-true'                       => '所有条件为真',
                    'any-conditions-true'                       => '任意条件为真',
                    'apply-to-shipping'                         => '应用于运费',
                    'attribute-family'                          => '属性系列',
                    'attribute-name-children-only'              => '仅限子类别的属性名称',
                    'attribute-name-parent-only'                => '仅限父类别的属性名称',
                    'auto-generate-coupon'                      => '自动生成优惠券',
                    'back-btn'                                  => '返回',
                    'buy-x-get-y-free'                          => '买 X 送 Y',
                    'buy-x-quantity'                            => '买 X 数量',
                    'cart-attribute'                            => '购物车属性',
                    'cart-item-attribute'                       => '购物车项目属性',
                    'categories'                                => '类别',
                    'channels'                                  => '渠道',
                    'children-categories'                       => '子类别',
                    'choose-condition-to-add'                   => '选择要添加的条件',
                    'condition-type'                            => '条件类型',
                    'conditions'                                => '条件',
                    'contain'                                   => '包含',
                    'contains'                                  => '包含',
                    'coupon-code'                               => '优惠券码',
                    'coupon-type'                               => '优惠券类型',
                    'create-success'                            => '购物车规则创建成功',
                    'customer-groups'                           => '顾客群体',
                    'description'                               => '描述',
                    'discount-amount'                           => '折扣金额',
                    'does-not-contain'                          => '不包含',
                    'end-of-other-rules'                        => '结束其他规则',
                    'equals-or-greater-than'                    => '等于或大于',
                    'equals-or-less-than'                       => '等于或小于',
                    'fixed-amount'                              => '固定金额',
                    'fixed-amount-whole-cart'                   => '整个购物车的固定金额',
                    'free-shipping'                             => '免费运费',
                    'from'                                      => '从',
                    'general'                                   => '通用',
                    'greater-than'                              => '大于',
                    'is-equal-to'                               => '等于',
                    'is-not-equal-to'                           => '不等于',
                    'less-than'                                 => '小于',
                    'marketing-time'                            => '营销时间',
                    'maximum-quantity-allowed-to-be-discounted' => '允许折扣的最大数量',
                    'name'                                      => '名称',
                    'no'                                        => '否',
                    'no-coupon'                                 => '无优惠券',
                    'parent-categories'                         => '父类别',
                    'payment-method'                            => '支付方式',
                    'percentage-product-price'                  => '产品价格百分比',
                    'price-in-cart'                             => '购物车中的价格',
                    'priority'                                  => '优先级',
                    'product-attribute'                         => '产品属性',
                    'qty-in-cart'                               => '购物车中的数量',
                    'save-btn'                                  => '保存购物车规则',
                    'settings'                                  => '设置',
                    'shipping-country'                          => '运费国家',
                    'shipping-method'                           => '运送方式',
                    'shipping-postcode'                         => '运费邮编',
                    'shipping-state'                            => '运费州',
                    'specific-coupon'                           => '特定优惠券',
                    'status'                                    => '状态',
                    'subtotal'                                  => '小计',
                    'title'                                     => '创建购物车规则',
                    'to'                                        => '到',
                    'total-items-qty'                           => '总项目数量',
                    'total-weight'                              => '总重量',
                    'uses-per-coupon'                           => '每张优惠券使用次数',
                    'uses-per-customer'                         => '每位顾客使用次数',
                    'uses-per-customer-control-info'            => '仅适用于已登录的顾客。',
                    'yes'                                       => '是',
                ],

                'edit' => [
                    'action-type'                               => '操作类型',
                    'actions'                                   => '操作',
                    'add-condition'                             => '添加条件',
                    'additional'                                => '附加',
                    'all-conditions-true'                       => '所有条件为真',
                    'alphabetical'                              => '字母',
                    'alphanumeric'                              => '字母数字混合',
                    'any-conditions-true'                       => '任意条件为真',
                    'apply-to-shipping'                         => '应用于运费',
                    'attribute-family'                          => '属性系列',
                    'attribute-name-children-only'              => '仅限子类别的属性名称',
                    'attribute-name-parent-only'                => '仅限父类别的属性名称',
                    'auto-generate-coupon'                      => '自动生成优惠券',
                    'back-btn'                                  => '返回',
                    'buy-x-get-y-free'                          => '买 X 送 Y',
                    'buy-x-quantity'                            => '买 X 数量',
                    'cart-attribute'                            => '购物车属性',
                    'cart-item-attribute'                       => '购物车项目属性',
                    'categories'                                => '类别',
                    'channels'                                  => '渠道',
                    'children-categories'                       => '子类别',
                    'choose-condition-to-add'                   => '选择要添加的条件',
                    'code-format'                               => '代码格式',
                    'code-prefix'                               => '代码前缀',
                    'code-suffix'                               => '代码后缀',
                    'condition-type'                            => '条件类型',
                    'conditions'                                => '条件',
                    'contain'                                   => '包含',
                    'contains'                                  => '包含',
                    'coupon-code'                               => '优惠券码',
                    'coupon-length'                             => '优惠券长度',
                    'coupon-qty'                                => '优惠券数量',
                    'coupon-type'                               => '优惠券类型',
                    'customer-group'                            => '顾客群体',
                    'customer-groups'                           => '顾客群体',
                    'description'                               => '描述',
                    'discount-amount'                           => '折扣金额',
                    'does-not-contain'                          => '不包含',
                    'end-of-other-rules'                        => '结束其他规则',
                    'equals-or-greater-than'                    => '等于或大于',
                    'equals-or-less-than'                       => '等于或小于',
                    'fixed-amount'                              => '固定金额',
                    'fixed-amount-whole-cart'                   => '整个购物车的固定金额',
                    'free-shipping'                             => '免费运费',
                    'from'                                      => '从',
                    'general'                                   => '通用',
                    'generate'                                  => '生成',
                    'greater-than'                              => '大于',
                    'is-equal-to'                               => '等于',
                    'is-not-equal-to'                           => '不等于',
                    'less-than'                                 => '小于',
                    'marketing-time'                            => '营销时间',
                    'maximum-quantity-allowed-to-be-discounted' => '允许折扣的最大数量',
                    'name'                                      => '名称',
                    'no'                                        => '否',
                    'no-coupon'                                 => '无优惠券',
                    'numeric'                                   => '数字',
                    'parent-categories'                         => '父类别',
                    'payment-method'                            => '支付方式',
                    'percentage-product-price'                  => '产品价格百分比',
                    'price-in-cart'                             => '购物车中的价格',
                    'priority'                                  => '优先级',
                    'product-attribute'                         => '产品属性',
                    'qty-in-cart'                               => '购物车中的数量',
                    'save-btn'                                  => '保存购物车规则',
                    'settings'                                  => '设置',
                    'shipping-country'                          => '运费国家',
                    'shipping-method'                           => '运送方式',
                    'shipping-postcode'                         => '运费邮编',
                    'shipping-state'                            => '运费州',
                    'specific-coupon'                           => '特定优惠券',
                    'status'                                    => '状态',
                    'subtotal'                                  => '小计',
                    'title'                                     => '编辑购物车规则',
                    'to'                                        => '到',
                    'total-items-qty'                           => '总项目数量',
                    'total-weight'                              => '总重量',
                    'update-success'                            => '购物车规则更新成功',
                    'uses-per-coupon'                           => '每张优惠券使用次数',
                    'uses-per-customer'                         => '每位顾客使用次数',
                    'uses-per-customer-control-info'            => '仅适用于已登录的顾客。',
                    'yes'                                       => '是',
                ],

                'delete-failed'  => '购物车规则删除失败',
                'delete-success' => '购物车规则删除成功',
            ],

            'catalog-rules' => [
                'index' => [
                    'create-btn' => '创建目录规则',
                    'title'      => '目录规则',

                    'datagrid' => [
                        'active'   => '活动',
                        'delete'   => '删除',
                        'edit'     => '编辑',
                        'end'      => '结束',
                        'id'       => 'ID',
                        'inactive' => '不活动',
                        'name'     => '名称',
                        'priority' => '优先级',
                        'start'    => '开始',
                        'status'   => '状态',
                    ],
                ],

                'create' => [
                    'action-type'              => '操作类型',
                    'actions'                  => '操作',
                    'add-condition'            => '添加条件',
                    'all-conditions-true'      => '所有条件为真',
                    'any-conditions-true'      => '任意条件为真',
                    'attribute-family'         => '属性系列',
                    'back-btn'                 => '返回',
                    'categories'               => '类别',
                    'channels'                 => '渠道',
                    'choose-condition-to-add'  => '选择要添加的条件',
                    'condition-type'           => '条件类型',
                    'conditions'               => '条件',
                    'contain'                  => '包含',
                    'contains'                 => '包含',
                    'customer-groups'          => '顾客群体',
                    'description'              => '描述',
                    'discount-amount'          => '折扣金额',
                    'does-not-contain'         => '不包含',
                    'end-other-rules'          => '结束其他规则',
                    'equals-or-greater-than'   => '等于或大于',
                    'equals-or-less-than'      => '等于或小于',
                    'fixed-amount'             => '固定金额',
                    'from'                     => '从',
                    'general'                  => '通用',
                    'greater-than'             => '大于',
                    'is-equal-to'              => '等于',
                    'is-not-equal-to'          => '不等于',
                    'less-than'                => '小于',
                    'marketing-time'           => '营销时间',
                    'name'                     => '名称',
                    'no'                       => '否',
                    'percentage-product-price' => '产品价格百分比',
                    'priority'                 => '优先级',
                    'product-attribute'        => '产品属性',
                    'save-btn'                 => '保存目录规则',
                    'settings'                 => '设置',
                    'status'                   => '状态',
                    'title'                    => '创建目录规则',
                    'to'                       => '到',
                    'yes'                      => '是',
                ],

                'edit' => [
                    'action-type'              => '操作类型',
                    'actions'                  => '操作',
                    'add-condition'            => '添加条件',
                    'all-conditions-true'      => '所有条件为真',
                    'any-conditions-true'      => '任意条件为真',
                    'back-btn'                 => '返回',
                    'categories'               => '类别',
                    'channels'                 => '渠道',
                    'choose-condition-to-add'  => '选择要添加的条件',
                    'condition-type'           => '条件类型',
                    'conditions'               => '条件',
                    'contain'                  => '包含',
                    'contains'                 => '包含',
                    'customer-groups'          => '顾客群体',
                    'description'              => '描述',
                    'discount-amount'          => '折扣金额',
                    'does-not-contain'         => '不包含',
                    'end-other-rules'          => '结束其他规则',
                    'equals-or-greater-than'   => '等于或大于',
                    'equals-or-less-than'      => '等于或小于',
                    'fixed-amount'             => '固定金额',
                    'from'                     => '从',
                    'general'                  => '通用',
                    'greater-than'             => '大于',
                    'is-equal-to'              => '等于',
                    'is-not-equal-to'          => '不等于',
                    'less-than'                => '小于',
                    'marketing-time'           => '营销时间',
                    'name'                     => '名称',
                    'no'                       => '否',
                    'percentage-product-price' => '产品价格百分比',
                    'priority'                 => '优先级',
                    'product-attribute'        => '产品属性',
                    'save-btn'                 => '保存目录规则',
                    'settings'                 => '设置',
                    'status'                   => '状态',
                    'title'                    => '编辑目录规则',
                    'to'                       => '到',
                    'yes'                      => '是',
                ],

                'create-success' => '目录规则创建成功',
                'delete-success' => '目录规则删除成功',
                'update-success' => '目录规则更新成功',
            ],

            'cart-rules-coupons' => [
                'cart-rule-not-defined-error' => '购物车规则无法删除',
                'delete-success'              => '购物车规则优惠券成功删除',
                'mass-delete-success'         => '成功删除所选项目',
                'success'                     => '成功创建 :name',

                'datagrid' => [
                    'coupon-code'     => '优惠券代码',
                    'created-date'    => '创建日期',
                    'delete'          => '删除',
                    'expiration-date' => '到期日期',
                    'id'              => 'ID',
                    'times-used'      => '使用次数',
                ],
            ],
        ],

        'search-seo' => [
            'search-terms' => [
                'index' => [
                    'create-btn' => '创建新搜索词条',
                    'title'      => '搜索词条',

                    'datagrid' => [
                        'actions'             => '操作',
                        'channel'             => '频道',
                        'delete'              => '删除',
                        'edit'                => '编辑',
                        'id'                  => 'ID',
                        'locale'              => '本地化',
                        'mass-delete-success' => '已成功删除选定的搜索词条',
                        'redirect-url'        => '重定向 URL',
                        'results'             => '结果',
                        'search-query'        => '搜索查询',
                        'uses'                => '使用',
                    ],

                    'create' => [
                        'channel'        => '频道',
                        'delete-warning' => '您确定要执行此操作吗？',
                        'locale'         => '本地化',
                        'redirect-url'   => '重定向 URL',
                        'results'        => '结果',
                        'save-btn'       => '保存搜索词条',
                        'search-query'   => '搜索查询',
                        'success'        => '搜索词条已成功创建',
                        'title'          => '创建新搜索词条',
                        'uses'           => '使用',
                    ],

                    'edit' => [
                        'delete-success' => '搜索词条已成功删除',
                        'success'        => '搜索词条已成功更新',
                        'title'          => '编辑搜索词条',
                    ],
                ],
            ],

            'search-synonyms' => [
                'index' => [
                    'create-btn' => '创建搜索同义词',
                    'title'      => '搜索同义词',

                    'datagrid' => [
                        'actions'             => '操作',
                        'delete'              => '删除',
                        'edit'                => '编辑',
                        'id'                  => '标识',
                        'mass-delete-success' => '成功删除所选的搜索同义词',
                        'name'                => '名称',
                        'terms'               => '术语',
                    ],

                    'create' => [
                        'delete-warning' => '您确定要执行此操作吗？',
                        'name'           => '名称',
                        'save-btn'       => '保存搜索同义词',
                        'success'        => '成功创建搜索同义词',
                        'terms'          => '术语',
                        'terms-info'     => '将同义词输入为以逗号分隔的列表，例如“鞋子，鞋类”。这扩展了搜索以包括所有术语。',
                        'title'          => '创建搜索同义词',
                    ],

                    'edit' => [
                        'delete-success' => '成功删除搜索同义词',
                        'success'        => '成功更新搜索同义词',
                        'title'          => '编辑搜索同义词',
                    ],
                ],
            ],

            'sitemaps' => [
                'index' => [
                    'create-btn' => '创建站点地图',
                    'sitemap'    => '站点地图',
                    'title'      => '站点地图',

                    'datagrid' => [
                        'actions'         => '操作',
                        'delete'          => '删除',
                        'edit'            => '编辑',
                        'file-name'       => '文件名',
                        'id'              => 'ID',
                        'link-for-google' => 'Google链接',
                        'path'            => '路径',
                    ],

                    'create' => [
                        'delete-warning' => '您确定要执行此操作吗？',
                        'file-name'      => '文件名',
                        'file-name-info' => '示例：sitemap.xml',
                        'path'           => '路径',
                        'path-info'      => '示例："/sitemap/" 或 "/" 用于基本路径',
                        'save-btn'       => '保存站点地图',
                        'success'        => '站点地图创建成功',
                        'title'          => '创建站点地图',
                    ],

                    'edit' => [
                        'delete-success' => '站点地图删除成功',
                        'success'        => '站点地图更新成功',
                        'title'          => '编辑站点地图',
                    ],
                ],

                'edit' => [
                    'back-btn'       => '返回',
                    'file-name'      => '文件名',
                    'file-name-info' => '示例：sitemap.xml',
                    'general'        => '通用',
                    'path'           => '路径',
                    'path-info'      => '示例："/sitemap/" 或 "/" 用于基本路径',
                    'save-btn'       => '保存站点地图',
                ],

                'delete-failed' => ':name 删除失败',
            ],

            'url-rewrites' => [
                'index' => [
                    'create-btn' => '创建 URL 重写',
                    'title'      => 'URL 重写',

                    'datagrid' => [
                        'actions'             => '操作',
                        'category'            => '类别',
                        'cms-page'            => 'CMS 页面',
                        'delete'              => '删除',
                        'edit'                => '编辑',
                        'for'                 => '适用对象',
                        'id'                  => 'ID',
                        'locale'              => '语言环境',
                        'mass-delete-success' => '已成功删除选定的 URL 重写。',
                        'permanent-redirect'  => '永久（301）',
                        'product'             => '产品',
                        'redirect-type'       => '重定向类型',
                        'request-path'        => '请求路径',
                        'target-path'         => '目标路径',
                        'temporary-redirect'  => '临时（302）',
                    ],

                    'create' => [
                        'category'           => '类别',
                        'cms-page'           => 'CMS 页面',
                        'delete-warning'     => '您确定要执行此操作吗？',
                        'for'                => '适用对象',
                        'locale'             => '语言环境',
                        'permanent-redirect' => '永久（301）',
                        'product'            => '产品',
                        'redirect-type'      => '重定向类型',
                        'request-path'       => '请求路径',
                        'save-btn'           => '保存 URL 重写',
                        'success'            => '已成功创建 URL 重写。',
                        'target-path'        => '目标路径',
                        'temporary-redirect' => '临时（302）',
                        'title'              => '创建 URL 重写',
                    ],

                    'edit' => [
                        'delete-success' => '已成功删除 URL 重写。',
                        'success'        => '已成功更新 URL 重写。',
                        'title'          => '编辑 URL 重写',
                    ],
                ],
            ],
        ],
    ],

    'cms' => [
        'index' => [
            'already-taken' => '该 :name 已被使用。',
            'channel'       => '渠道',
            'create-btn'    => '创建页面',
            'language'      => '语言',
            'title'         => '页面',

            'datagrid' => [
                'channel'             => '渠道',
                'delete'              => '删除',
                'edit'                => '编辑',
                'id'                  => 'ID',
                'mass-delete-success' => '所选数据已成功删除',
                'page-title'          => '页面标题',
                'url-key'             => 'URL键',
                'view'                => '查看',
            ],
        ],

        'create' => [
            'channels'         => '渠道',
            'content'          => '内容',
            'description'      => '描述',
            'general'          => '常规',
            'meta-description' => 'Meta描述',
            'meta-keywords'    => 'Meta关键词',
            'meta-title'       => 'Meta标题',
            'page-title'       => '标题',
            'save-btn'         => '保存页面',
            'seo'              => 'SEO',
            'title'            => '创建页面',
            'url-key'          => 'URL键',
        ],

        'edit' => [
            'back-btn'         => '返回',
            'channels'         => '渠道',
            'content'          => '内容',
            'description'      => '描述',
            'general'          => '常规',
            'meta-description' => 'Meta描述',
            'meta-keywords'    => 'Meta关键词',
            'meta-title'       => 'Meta标题',
            'page-title'       => '页面标题',
            'preview-btn'      => '预览页面',
            'save-btn'         => '保存页面',
            'seo'              => 'SEO',
            'title'            => '编辑页面',
            'url-key'          => 'URL键',
        ],

        'create-success' => 'CMS页面创建成功。',
        'delete-success' => 'CMS页面删除成功。',
        'no-resource'    => '资源不存在。',
        'update-success' => 'CMS页面更新成功。',
    ],

    'settings' => [
        'locales' => [
            'index' => [
                'create-btn' => '创建区域',
                'locale'     => '区域',
                'logo-size'  => '图像分辨率应该为24px x 16px',
                'title'      => '区域',

                'datagrid' => [
                    'actions'   => '操作',
                    'code'      => '代码',
                    'delete'    => '删除',
                    'direction' => '方向',
                    'edit'      => '编辑',
                    'id'        => 'ID',
                    'ltr'       => 'LTR',
                    'name'      => '名称',
                    'rtl'       => 'RTL',
                ],

                'create' => [
                    'code'             => '代码',
                    'direction'        => '方向',
                    'locale-logo'      => '区域标志',
                    'name'             => '名称',
                    'save-btn'         => '保存区域',
                    'select-direction' => '选择方向',
                    'title'            => '创建区域',
                ],

                'edit' => [
                    'title' => '编辑区域',
                ],

                'create-success'    => '区域创建成功。',
                'delete-failed'     => '区域删除失败',
                'delete-success'    => '区域删除成功。',
                'delete-warning'    => '确定要执行此操作吗？',
                'last-delete-error' => '至少需要一个区域。',
                'update-success'    => '区域更新成功。',
            ],
        ],

        'currencies' => [
            'index' => [
                'create-btn' => '创建货币',
                'currency'   => '货币',
                'title'      => '货币',

                'datagrid' => [
                    'actions'        => '操作',
                    'code'           => '代码',
                    'delete'         => '删除',
                    'edit'           => '编辑',
                    'id'             => 'ID',
                    'method-error'   => '错误！检测到错误的方法，请检查批量操作配置',
                    'name'           => '名称',
                    'no-resource'    => '提供的资源不足以执行此操作',
                    'partial-action' => '由于系统对 :resource 的限制，未执行某些操作',
                    'update-success' => '已成功更新选定的 :resource',
                ],

                'create' => [
                    'code'                   => '代码',
                    'create-btn'             => '创建货币',
                    'currency-position'      => '货币位置',
                    'decimal'                => '小数',
                    'decimal-separator'      => '小数点分隔符',
                    'decimal-separator-note' => ':attribute 字段只能接受逗号（,）和点（.）操作符',
                    'delete-warning'         => '确定要执行此操作吗？',
                    'general'                => '常规',
                    'group-separator'        => '分组分隔符',
                    'group-separator-note'   => ':attribute 字段只能接受逗号 (,)、点 (.)、撇号 (\') 和空格 ( ) 字符。',
                    'name'                   => '名称',
                    'save-btn'               => '保存货币',
                    'symbol'                 => '符号',
                    'title'                  => '创建新货币',
                ],

                'edit' => [
                    'title' => '编辑货币',
                ],

                'create-success'    => '货币创建成功。',
                'delete-failed'     => '货币删除失败',
                'delete-success'    => '货币删除成功。',
                'last-delete-error' => '至少需要一个货币。',
                'update-success'    => '货币更新成功。',
            ],
        ],

        'data-transfer' => [
            'imports' => [
                'create' => [
                    'action'              => '動作',
                    'allowed-errors'      => '允許的錯誤',
                    'back-btn'            => '返回',
                    'create-update'       => '建立/更新',
                    'delete'              => '刪除',
                    'download-sample'     => '下載範例',
                    'field-separator'     => '欄位分隔符',
                    'file'                => '檔案',
                    'file-info'           => '使用相對於 /project-root/storage/app/import 的路徑，例如 product-images, import-images。',
                    'file-info-example'   => '例如，在 product-images 的情況下，檔案應放置在 /project-root/storage/app/import/product-images 資料夾中。',
                    'general'             => '一般',
                    'images-directory'    => '圖片目錄路徑',
                    'process-in-queue'    => '佇列中處理',
                    'results'             => '結果',
                    'save-btn'            => '儲存匯入',
                    'settings'            => '設定',
                    'skip-errors'         => '跳過錯誤',
                    'stop-on-errors'      => '在發生錯誤時停止',
                    'title'               => '建立匯入',
                    'type'                => '類型',
                    'validation-strategy' => '驗證策略',
                ],

                'edit' => [
                    'action'              => '動作',
                    'allowed-errors'      => '允許的錯誤',
                    'back-btn'            => '返回',
                    'create-update'       => '建立/更新',
                    'current-file'        => '当前上传的文件',
                    'delete'              => '刪除',
                    'download-sample'     => '下載範例',
                    'field-separator'     => '欄位分隔符',
                    'file'                => '檔案',
                    'file-info'           => '使用相對於 /project-root/storage/app/import 的路徑，例如 product-images, import-images。',
                    'file-info-example'   => '例如，在 product-images 的情況下，檔案應放置在 /project-root/storage/app/import/product-images 資料夾中。',
                    'general'             => '一般',
                    'images-directory'    => '圖片目錄路徑',
                    'process-in-queue'    => '佇列中處理',
                    'results'             => '結果',
                    'save-btn'            => '儲存匯入',
                    'settings'            => '設定',
                    'skip-errors'         => '跳過錯誤',
                    'stop-on-errors'      => '在發生錯誤時停止',
                    'title'               => '編輯匯入',
                    'type'                => '類型',
                    'validation-strategy' => '驗證策略',
                ],

                'index' => [
                    'button-title' => '建立匯入',
                    'title'        => '匯入',

                    'datagrid' => [
                        'actions'       => '動作',
                        'completed-at'  => '已完成於',
                        'created'       => '已建立',
                        'delete'        => '刪除',
                        'deleted'       => '已刪除',
                        'edit'          => '編輯',
                        'error-file'    => '錯誤檔案',
                        'id'            => 'ID',
                        'started-at'    => '已開始於',
                        'state'         => '狀態',
                        'summary'       => '摘要',
                        'updated'       => '已更新',
                        'uploaded-file' => '已上傳的檔案',
                    ],
                ],

                'import' => [
                    'back-btn'                => '返回',
                    'completed-batches'       => '已完成的批次：',
                    'download-error-report'   => '下載完整報告',
                    'edit-btn'                => '編輯',
                    'imported-info'           => '恭喜！您的匯入已成功完成。',
                    'importing-info'          => '匯入處理中',
                    'indexing-info'           => '價格、庫存和 Elasticsearch 索引中的資源處理中',
                    'linking-info'            => '資源連結中',
                    'progress'                => '進度：',
                    'title'                   => '匯入',
                    'total-batches'           => '總批次：',
                    'total-created'           => '已建立記錄：',
                    'total-deleted'           => '已刪除記錄：',
                    'total-errors'            => '總錯誤：',
                    'total-invalid-rows'      => '無效的行數：',
                    'total-rows-processed'    => '處理的行數：',
                    'total-updated'           => '已更新記錄：',
                    'validate-info'           => '按一下 „驗證數據“ 以檢查您的匯入。',
                    'validate'                => '驗證',
                    'validating-info'         => '數據開始閱讀和驗證',
                    'validation-failed-info'  => '您的匯入無效。請修正以下錯誤並再試一次。',
                    'validation-success-info' => '您的匯入有效。按一下 „匯入“ 以開始匯入過程。',
                ],

                'create-success'    => '已成功建立匯入。',
                'delete-failed'     => '匯入刪除失敗。',
                'delete-success'    => '已成功刪除匯入。',
                'not-valid'         => '匯入無效',
                'nothing-to-import' => '沒有要匯入的資源。',
                'setup-queue-error' => '請將您的佇列驅動程序更改為 „database“ 或 „redis“，以開始匯入過程。',
                'update-success'    => '已成功更新匯入。',
            ],
        ],

        'exchange-rates' => [
            'index' => [
                'create-btn'    => '创建汇率',
                'exchange-rate' => '汇率',
                'title'         => '汇率',
                'update-rates'  => '更新汇率',

                'create' => [
                    'delete-warning'         => '确定要执行此操作吗？',
                    'rate'                   => '汇率',
                    'save-btn'               => '保存汇率',
                    'select-target-currency' => '选择目标货币',
                    'source-currency'        => '源货币',
                    'target-currency'        => '目标货币',
                    'title'                  => '创建汇率',
                ],

                'edit' => [
                    'title' => '编辑汇率',
                ],

                'datagrid' => [
                    'actions'       => '操作',
                    'currency-name' => '货币名称',
                    'delete'        => '删除',
                    'edit'          => '编辑',
                    'exchange-rate' => '汇率',
                    'id'            => 'ID',
                ],

                'create-success'  => '汇率创建成功',
                'delete-error'    => '汇率删除失败',
                'delete-success'  => '汇率删除成功',
                'update-success'  => '汇率更新成功',
            ],
        ],

        'inventory-sources' => [
            'index' => [
                'create-btn' => '创建库存源',
                'title'      => '库存来源',

                'datagrid' => [
                    'active'   => '激活',
                    'code'     => '代码',
                    'delete'   => '删除',
                    'edit'     => '编辑',
                    'id'       => 'ID',
                    'inactive' => '未激活',
                    'name'     => '名称',
                    'priority' => '优先级',
                    'status'   => '状态',
                ],
            ],

            'create' => [
                'add-title'      => '添加库存来源',
                'address'        => '来源地址',
                'back-btn'       => '返回',
                'city'           => '城市',
                'code'           => '代码',
                'contact-email'  => '电子邮件',
                'contact-fax'    => '传真',
                'contact-info'   => '联系信息',
                'contact-name'   => '名称',
                'contact-number' => '联系电话',
                'country'        => '国家',
                'description'    => '描述',
                'general'        => '常规',
                'latitude'       => '纬度',
                'longitude'      => '经度',
                'name'           => '名称',
                'postcode'       => '邮政编码',
                'priority'       => '优先级',
                'save-btn'       => '保存库存来源',
                'select-country' => '选择国家',
                'select-state'   => '选择州/省',
                'settings'       => '设置',
                'state'          => '州/省',
                'status'         => '状态',
                'street'         => '街道',
                'title'          => '库存来源',
            ],

            'edit' => [
                'back-btn'       => '返回',
                'city'           => '城市',
                'code'           => '代码',
                'contact-email'  => '电子邮件',
                'contact-fax'    => '传真',
                'contact-info'   => '联系信息',
                'contact-name'   => '名称',
                'contact-number' => '联系电话',
                'country'        => '国家',
                'description'    => '描述',
                'general'        => '常规',
                'latitude'       => '纬度',
                'longitude'      => '经度',
                'name'           => '名称',
                'postcode'       => '邮政编码',
                'priority'       => '优先级',
                'save-btn'       => '保存库存来源',
                'select-country' => '选择国家',
                'select-state'   => '选择州/省',
                'settings'       => '设置',
                'source-address' => '来源地址',
                'state'          => '州/省',
                'status'         => '状态',
                'street'         => '街道',
                'title'          => '编辑库存来源',
            ],

            'create-success'    => '库存来源创建成功',
            'delete-failed'     => '库存来源删除失败',
            'delete-success'    => '库存来源删除成功',
            'last-delete-error' => '至少需要一个库存来源',
            'update-success'    => '库存来源更新成功',
        ],

        'taxes' => [
            'categories' => [
                'index' => [
                    'delete-warning' => '您确定要删除吗？',
                    'tax-category'   => '税收分类',
                    'title'          => '税收分类',

                    'datagrid' => [
                        'actions' => '操作',
                        'code'    => '代码',
                        'delete'  => '删除',
                        'edit'    => '编辑',
                        'id'      => 'ID',
                        'name'    => '名称',
                    ],

                    'create' => [
                        'add-tax-rates' => '添加税率',
                        'code'          => '代码',
                        'description'   => '描述',
                        'empty-text'    => '税率不可用，请创建新的税率。',
                        'general'       => '税收分类',
                        'name'          => '名称',
                        'save-btn'      => '保存税收分类',
                        'select'        => '选择',
                        'tax-rates'     => '税率',
                        'title'         => '创建税收分类',
                    ],

                    'edit' => [
                        'title'   => '编辑税收分类',
                    ],

                    'can-not-delete' => '分配了税率的类别无法删除。',
                    'create-success' => '新税收分类已创建',
                    'delete-failed'  => '税收分类删除失败',
                    'delete-success' => '税收分类已成功删除',
                    'update-success' => '税收分类成功更新',
                ],
            ],

            'rates' => [
                'index' => [
                    'button-title' => '创建税率',
                    'tax-rate'     => '税率',
                    'title'        => '税率',

                    'datagrid' => [
                        'country'    => '国家',
                        'delete'     => '删除',
                        'edit'       => '编辑',
                        'id'         => 'ID',
                        'identifier' => '标识符',
                        'state'      => '州',
                        'tax-rate'   => '税率',
                        'zip-code'   => '邮政编码',
                        'zip-from'   => '从',
                        'zip-to'     => '至',
                    ],
                ],

                'create' => [
                    'back-btn'       => '返回',
                    'country'        => '国家',
                    'general'        => '常规',
                    'identifier'     => '标识符',
                    'is-zip'         => '启用邮政编码范围',
                    'save-btn'       => '保存税率',
                    'select-country' => '选择国家',
                    'select-state'   => '选择州',
                    'settings'       => '设置',
                    'state'          => '州',
                    'tax-rate'       => '税率',
                    'title'          => '创建税率',
                    'zip-code'       => '邮政编码',
                    'zip-from'       => '从',
                    'zip-to'         => '至',
                ],

                'edit' => [
                    'back-btn'       => '返回',
                    'country'        => '国家',
                    'identifier'     => '标识符',
                    'save-btn'       => '保存税率',
                    'select-country' => '选择国家',
                    'select-state'   => '选择州',
                    'settings'       => '设置',
                    'state'          => '州',
                    'tax-rate'       => '税率',
                    'title'          => '编辑税率',
                    'zip-code'       => '邮政编码',
                    'zip-from'       => '从',
                    'zip-to'         => '至',
                ],

                'create-success' => '税率创建成功。',
                'delete-failed'  => '税率删除失败',
                'delete-success' => '税率删除成功',
                'update-success' => '税率更新成功',
            ],
        ],

        'channels' => [
            'index' => [
                'create-btn'        => '创建渠道',
                'delete-failed'     => '频道 删除失败',
                'delete-success'    => '渠道删除成功。',
                'last-delete-error' => '最后一个渠道删除失败。',
                'title'             => '渠道',

                'datagrid' => [
                    'code'      => '代码',
                    'delete'    => '删除',
                    'edit'      => '编辑',
                    'host-name' => '主机名',
                    'id'        => 'ID',
                    'name'      => '名称',
                ],
            ],

            'create' => [
                'allowed-ips'             => '允许的IP',
                'cancel'                  => '返回',
                'code'                    => '代码',
                'create-success'          => '渠道创建成功。',
                'currencies'              => '货币',
                'currencies-and-locales'  => '货币和区域设置',
                'default-currency'        => '默认货币',
                'default-locale'          => '默认区域设置',
                'description'             => '描述',
                'design'                  => '设计',
                'favicon'                 => '网站图标',
                'favicon-size'            => '图像分辨率应为16px x 16px',
                'general'                 => '通用',
                'hostname'                => '主机名',
                'hostname-placeholder'    => 'https://www.example.com（不要在结尾添加斜杠。）',
                'inventory-sources'       => '库存来源',
                'last-delete-error'       => '至少需要一个渠道。',
                'locales'                 => '区域设置',
                'logo'                    => '标志',
                'logo-size'               => '图像分辨率应为192px x 50px',
                'maintenance-mode-text'   => '消息',
                'name'                    => '名称',
                'root-category'           => '根分类',
                'save-btn'                => '保存渠道',
                'select-default-currency' => '选择默认货币',
                'select-default-locale'   => '选择默认区域设置',
                'select-root-category'    => '选择根类别',
                'select-theme'            => '选择主题',
                'seo'                     => '主页SEO',
                'seo-description'         => '元描述',
                'seo-keywords'            => '元关键字',
                'seo-title'               => '元标题',
                'settings'                => '设置',
                'status'                  => '状态',
                'theme'                   => '主题',
                'title'                   => '创建渠道',
            ],

            'edit' => [
                'allowed-ips'            => '允许的IP',
                'back-btn'               => '返回',
                'code'                   => '代码',
                'currencies'             => '货币',
                'currencies-and-locales' => '货币和区域设置',
                'default-currency'       => '默认货币',
                'default-locale'         => '默认区域设置',
                'description'            => '描述',
                'design'                 => '设计',
                'favicon'                => '网站图标',
                'favicon-size'           => '图像分辨率应为16px x 16px',
                'general'                => '通用',
                'hostname'               => '主机名',
                'hostname-placeholder'   => 'https://www.example.com（不要在结尾添加斜杠。）',
                'inventory-sources'      => '库存来源',
                'last-delete-error'      => '至少需要一个渠道。',
                'locales'                => '区域设置',
                'logo'                   => '标志',
                'logo-size'              => '图像分辨率应为192px x 50px',
                'maintenance-mode'       => '维护模式',
                'maintenance-mode-text'  => '消息',
                'name'                   => '名称',
                'root-category'          => '根分类',
                'save-btn'               => '保存渠道',
                'seo'                    => '主页SEO',
                'seo-description'        => '元描述',
                'seo-keywords'           => '元关键字',
                'seo-title'              => '元标题',
                'status'                 => '状态',
                'theme'                  => '主题',
                'title'                  => '编辑渠道',
                'update-success'         => '渠道更新成功',
            ],
        ],

        'users' => [
            'index' => [
                'admin' => '管理员',
                'title' => '用户',
                'user'  => '用户',

                'create' => [
                    'confirm-password'  => '确认密码',
                    'email'             => '电子邮件',
                    'name'              => '姓名',
                    'password'          => '密码',
                    'role'              => '角色',
                    'save-btn'          => '保存用户',
                    'status'            => '状态',
                    'title'             => '创建用户',
                    'upload-image-info' => '上传头像图片（110px x 110px），支持PNG或JPG格式',
                ],

                'datagrid' => [
                    'actions'  => '操作',
                    'active'   => '活跃',
                    'delete'   => '删除',
                    'edit'     => '编辑',
                    'email'    => '电子邮件',
                    'id'       => 'ID',
                    'inactive' => '不活跃',
                    'name'     => '姓名',
                    'role'     => '角色',
                    'status'   => '状态',
                ],

                'edit' => [
                    'title' => '编辑用户',
                ],
            ],

            'edit' => [
                'back-btn'         => '返回',
                'confirm-password' => '确认密码',
                'email'            => '电子邮件',
                'general'          => '通用',
                'name'             => '姓名',
                'password'         => '密码',
                'role'             => '角色',
                'save-btn'         => '保存用户',
                'status'           => '状态',
                'title'            => '编辑用户',
            ],

            'activate-warning'   => '您的帐户尚未激活，请联系管理员。',
            'cannot-change'      => '无法更改用户。',
            'create-success'     => '用户成功创建。',
            'delete-failed'      => '删除用户失败。',
            'delete-success'     => '用户成功删除。',
            'delete-warning'     => '您确定要执行此操作吗？',
            'incorrect-password' => '密码错误',
            'last-delete-error'  => '删除最后一个用户失败。',
            'login-error'        => '请检查您的凭据并重试。',
            'update-success'     => '用户成功更新。',
        ],

        'roles' => [
            'index' => [
                'create-btn' => '创建角色',
                'title'      => '角色',

                'datagrid' => [
                    'custom'          => '自定义',
                    'all'             => '全部',
                    'permission-type' => '权限类型',
                    'name'            => '名称',
                    'id'              => 'ID',
                    'edit'            => '编辑',
                    'delete'          => '删除',
                ],
            ],

            'create' => [
                'access-control' => '访问控制',
                'all'            => '全部',
                'back-btn'       => '返回',
                'custom'         => '自定义',
                'description'    => '描述',
                'general'        => '通用',
                'name'           => '名称',
                'permissions'    => '权限',
                'save-btn'       => '保存角色',
                'title'          => '创建角色',
            ],

            'edit' => [
                'access-control' => '访问控制',
                'all'            => '全部',
                'back-btn'       => '返回',
                'custom'         => '自定义',
                'description'    => '描述',
                'general'        => '通用',
                'name'           => '名称',
                'permissions'    => '权限',
                'save-btn'       => '保存角色',
                'title'          => '编辑角色',
            ],

            'being-used'        => '角色已在管理员用户中使用',
            'create-success'    => '角色创建成功',
            'delete-failed'     => '角色删除失败',
            'delete-success'    => '角色已成功删除',
            'last-delete-error' => '最后一个角色无法删除',
            'update-success'    => '角色已成功更新',
        ],

        'themes' => [
            'index' => [
                'create-btn' => '创建主题',
                'title'      => '主题',

                'datagrid' => [
                    'active'        => '活动',
                    'channel_name'  => '频道名称',
                    'change-status' => '更改状态',
                    'delete'        => '删除',
                    'id'            => 'ID',
                    'inactive'      => '非活动',
                    'name'          => '名称',
                    'sort-order'    => '排序顺序',
                    'status'        => '状态',
                    'theme'         => '主题',
                    'type'          => '类型',
                    'view'          => '查看',
                ],
            ],

            'create' => [
                'name'       => '名称',
                'save-btn'   => '保存主题',
                'sort-order' => '排序顺序',
                'themes'     => '主题',
                'title'      => '创建主题',

                'type' => [
                    'category-carousel' => '类别轮播',
                    'footer-links'      => '页脚链接',
                    'image-carousel'    => '图像轮播',
                    'product-carousel'  => '产品轮播',
                    'services-content'  => '服务内容',
                    'static-content'    => '静态内容',
                    'title'             => '类型',
                ],
            ],

            'edit' => [
                'active'                        => '活动',
                'add-filter-btn'                => '添加过滤器',
                'add-footer-link-btn'           => '添加页脚链接',
                'add-image-btn'                 => '添加图片',
                'add-link'                      => '添加链接',
                'asc'                           => '升序',
                'back'                          => '返回',
                'category-carousel'             => '类别轮播',
                'category-carousel-description' => '使用响应式类别轮播以引人注目的方式展示动态类别。',
                'channels'                      => '频道',
                'column'                        => '列',
                'create-filter'                 => '创建过滤器',
                'css'                           => 'CSS',
                'delete'                        => '删除',
                'desc'                          => '降序',
                'edit'                          => '编辑',
                'featured'                      => '精选',
                'filter-title'                  => '标题',
                'filters'                       => '过滤器',
                'footer-link'                   => '页脚链接',
                'footer-link-description'       => '通过页脚链接无缝浏览网站和获取信息。',
                'footer-link-form-title'        => '页脚链接',
                'footer-title'                  => '标题',
                'general'                       => '通用',
                'inactive'                      => '非活动',
                'html'                          => 'HTML',
                'image'                         => '图片',
                'image-size'                    => '图片分辨率应为（1920px x 700px）',
                'image-title'                   => '图片标题',
                'image-upload-message'          => '仅允许图片（.jpeg、.jpg、.png、.webp 等）。',
                'key'                           => '键：:key',
                'key-input'                     => '键',
                'limit'                         => '限制',
                'link'                          => '链接',
                'name'                          => '名称',
                'no'                            => '不',
                'new'                           => '新',
                'parent-id'                     => '父ID',
                'parent-id-hint'                => '您可以输入多个父ID作为逗号分隔的值（例如：12,15,34）',
                'category-id'                   => '类别ID',
                'preview'                       => '预览',
                'product-carousel'              => '产品轮播',
                'product-carousel-description'  => '使用动态和响应式产品轮播优雅地展示产品。',
                'save-btn'                      => '保存',
                'select'                        => '选择',
                'slider'                        => '滑块',
                'slider-add-btn'                => '添加滑块',
                'slider-description'            => '与滑块相关的主题自定义。',
                'slider-image'                  => '滑块图片',
                'slider-required'               => '滑块字段为必填项。',
                'sort'                          => '排序',
                'sort-order'                    => '排序顺序',
                'static-content'                => '静态内容',
                'static-content-description'    => '为您的受众提供简洁、信息丰富的静态内容，提高参与度。',
                'status'                        => '状态',
                'title'                         => '编辑主题',
                'update-slider'                 => '更新滑块',
                'url'                           => '网址',
                'value'                         => '值：:value',
                'value-input'                   => '值',
                'themes'                        => '主题',

                'services-content' => [
                    'add-btn'            => '添加服务',
                    'channels'           => '渠道',
                    'delete'             => '删除',
                    'description'        => '描述',
                    'general'            => '通用',
                    'name'               => '名称',
                    'save-btn'           => '保存',
                    'service-icon'       => '服务图标',
                    'service-icon-class' => '服务图标类',
                    'service-info'       => '与服务相关的主题定制。',
                    'services'           => '服务',
                    'sort-order'         => '排序顺序',
                    'status'             => '状态',
                    'title'              => '标题',
                    'update-service'     => '更新服务',
                ],
                'yes'                           => '是',
            ],

            'create-success' => '成功创建主题',
            'delete-success' => '成功删除主题',
            'update-success' => '成功更新主题',
        ],
    ],

    'reporting' => [
        'sales' => [
            'index' => [
                'abandoned-carts'               => '被遗弃的购物车',
                'abandoned-products'            => '被遗弃的商品',
                'abandoned-rate'                => '被遗弃率',
                'abandoned-revenue'             => '被遗弃的收入',
                'added-to-cart'                 => '已添加到购物车',
                'added-to-cart-info'            => '仅 :progress 位访客将产品添加到购物车',
                'all-channels'                  => '所有渠道',
                'average-order-value-over-time' => '随时间的平均订单价值',
                'average-sales'                 => '平均订单价值',
                'count'                         => '数量',
                'end-date'                      => '结束日期',
                'id'                            => 'ID',
                'interval'                      => '间隔',
                'name'                          => '名称',
                'orders'                        => '订单',
                'orders-over-time'              => '随时间的订单数',
                'payment-method'                => '付款方式',
                'product-views'                 => '商品浏览次数',
                'product-views-info'            => '仅 :progress 位访客查看商品',
                'purchase-funnel'               => '购买漏斗',
                'purchased'                     => '已购买',
                'purchased-info'                => '仅 :progress 位访客进行购买',
                'refunds'                       => '退款',
                'refunds-over-time'             => '随时间的退款',
                'sales-over-time'               => '随时间的销售',
                'shipping-collected'            => '运费收入',
                'shipping-collected-over-time'  => '随时间的运费收入',
                'start-date'                    => '开始日期',
                'tax-collected'                 => '税金收入',
                'tax-collected-over-time'       => '随时间的税金收入',
                'title'                         => '销售',
                'top-payment-methods'           => '热门付款方式',
                'top-shipping-methods'          => '热门配送方式',
                'top-tax-categories'            => '热门税收类别',
                'total'                         => '总计',
                'total-orders'                  => '总订单数',
                'total-sales'                   => '总销售额',
                'total-visits'                  => '总访问量',
                'total-visits-info'             => '商店的总访客数',
                'view-details'                  => '查看详情',
            ],
        ],

        'customers' => [
            'index' => [
                'all-channels'                => '所有渠道',
                'count'                       => '数量',
                'customers'                   => '客户',
                'customers-over-time'         => '随时间的客户数',
                'customers-traffic'           => '客户流量',
                'customers-with-most-orders'  => '最多订单的客户',
                'customers-with-most-reviews' => '最多评价的客户',
                'customers-with-most-sales'   => '最多销售的客户',
                'email'                       => '电子邮件',
                'end-date'                    => '结束日期',
                'id'                          => 'ID',
                'interval'                    => '间隔',
                'name'                        => '名称',
                'orders'                      => '订单',
                'reviews'                     => '评价',
                'start-date'                  => '开始日期',
                'title'                       => '客户',
                'top-customer-groups'         => '热门客户群',
                'total'                       => '总计',
                'total-customers'             => '总客户数',
                'total-visitors'              => '总访客数',
                'traffic-over-week'           => '一周内的流量',
                'unique-visitors'             => '独立访客',
                'view-details'                => '查看详情',
            ],
        ],

        'products' => [
            'index' => [
                'all-channels'                     => '所有渠道',
                'channel'                          => '频道',
                'end-date'                         => '结束日期',
                'id'                               => 'ID',
                'interval'                         => '间隔',
                'last-search-terms'                => '最近的搜索词',
                'locale'                           => '区域',
                'name'                             => '名称',
                'orders'                           => '订单',
                'price'                            => '价格',
                'products-added-over-time'         => '随时间的新增商品',
                'products-with-most-reviews'       => '最多评价的商品',
                'products-with-most-visits'        => '最多访问的商品',
                'quantities'                       => '数量',
                'quantities-sold-over-time'        => '随时间的销售数量',
                'results'                          => '结果',
                'revenue'                          => '收入',
                'reviews'                          => '评价',
                'search-term'                      => '搜索词',
                'start-date'                       => '开始日期',
                'title'                            => '商品',
                'top-search-terms'                 => '最受欢迎的搜索词',
                'top-selling-products-by-quantity' => '按数量销售最多的商品',
                'top-selling-products-by-revenue'  => '按收入销售最多的商品',
                'total'                            => '总计',
                'total-products-added-to-wishlist' => '已添加到愿望清单的商品',
                'total-sold-quantities'            => '已售商品数量',
                'uses'                             => '用途',
                'view-details'                     => '查看详情',
                'visits'                           => '访问次数',
            ],
        ],

        'view' => [
            'all-channels'  => '所有渠道',
            'back-btn'      => '返回',
            'day'           => '日',
            'end-date'      => '结束日期',
            'export-csv'    => '导出CSV',
            'export-xls'    => '导出XLS',
            'month'         => '月',
            'not-available' => '没有可用的记录。',
            'start-date'    => '开始日期',
            'year'          => '年',
        ],

        'empty' => [
            'info'  => '所选时间段内暂无数据',
            'title' => '暂无数据',
        ],
    ],

    'configuration' => [
        'index' => [
            'back-btn'                     => '返回',
            'delete'                       => '删除',
            'enable-at-least-one-payment'  => '至少启用一种支付方式。',
            'enable-at-least-one-shipping' => '至少启用一种配送方式。',
            'no-result-found'              => '未找到结果',
            'save-btn'                     => '保存配置',
            'save-message'                 => '配置保存成功',
            'search'                       => '搜索',
            'select-country'               => '选择国家',
            'select-state'                 => '选择省份',
            'title'                        => '配置',

            'general' => [
                'info'  => '设置单位选项。',
                'title' => '常规',

                'general' => [
                    'info'  => '设置单位选项并启用或禁用面包屑导航。',
                    'title' => '常规',

                    'unit-options' => [
                        'info'        => '设置单位选项。',
                        'title'       => '单位选项',
                        'title-info'  => '配置重量单位为磅（lbs）或千克（kgs）。',
                        'weight-unit' => '重量单位',
                    ],

                    'breadcrumbs' => [
                        'shop'       => '商店面包屑',
                        'title'      => '面包屑',
                        'title-info' => '在商店中启用或禁用面包屑导航。',
                    ],
                ],

                'content' => [
                    'info'  => '设置页眉优惠标题和自定义脚本。',
                    'title' => '内容',

                    'header-offer' => [
                        'title'             => '页眉优惠标题',
                        'title-info'        => '配置页眉优惠标题，包括优惠标题、重定向标题和重定向链接。',
                        'offer-title'       => '优惠标题',
                        'redirection-title' => '重定向标题',
                        'redirection-link'  => '重定向链接',
                    ],

                    'speculation-rules' => [
                        'enable-speculation' => '启用猜测规则',
                        'info'               => '配置启用或禁用自动猜测逻辑的设置。',
                        'title'              => '猜测规则',

                        'prerender' => [
                            'conservative'           => '保守',
                            'eager'                  => '积极',
                            'eagerness'              => '预渲染积极程度',
                            'eagerness-info'         => '控制猜测规则的应用激进程度。选项：积极（最大）、适中（默认）、保守（低）。',
                            'enabled'                => '启用预渲染猜测规则',
                            'ignore-url-params'      => '忽略预渲染URL参数',
                            'ignore-url-params-info' => '指定在猜测规则中忽略的URL参数。使用管道符 (|) 分隔多个参数。',
                            'ignore-urls'            => '忽略预渲染URL',
                            'ignore-urls-info'       => '输入要从猜测逻辑中排除的URL。用管道符 (|) 分隔多个URL。',
                            'info'                   => '设置猜测规则状态。',
                            'moderate'               => '适中',
                        ],

                        'prefetch' => [
                            'conservative'           => '保守',
                            'eager'                  => '积极',
                            'eagerness'              => '预取积极程度',
                            'eagerness-info'         => '控制猜测规则的应用激进程度。选项：积极（最大）、适中（默认）、保守（低）。',
                            'enabled'                => '启用预取猜测规则',
                            'ignore-url-params'      => '忽略预取URL参数',
                            'ignore-url-params-info' => '指定在猜测规则中忽略的URL参数。使用管道符 (|) 分隔多个参数。',
                            'ignore-urls'            => '忽略预取URL',
                            'ignore-urls-info'       => '输入要从猜测逻辑中排除的URL。用管道符 (|) 分隔多个URL。',
                            'info'                   => '设置猜测规则状态。',
                            'moderate'               => '适中',
                        ],
                    ],

                    'custom-scripts' => [
                        'custom-css'        => '自定义CSS',
                        'custom-javascript' => '自定义Javascript',
                        'title'             => '自定义脚本',
                        'title-info'        => '自定义脚本是为软件添加特定功能或特性的个性化代码片段，独特地增强其功能。',
                    ],
                ],

                'design' => [
                    'info'  => '为管理面板设置标志和网站图标。',
                    'title' => '设计',

                    'admin-logo' => [
                        'favicon'    => '网站图标',
                        'logo-image' => '标志图像',
                        'title'      => '管理标志',
                        'title-info' => '为您的网站前端配置标志和网站图标图像，以提升品牌形象和识别度。',
                    ],

                    'menu-category' => [
                        'default'         => '默认菜单',
                        'info'            => '此设置控制标题菜单中类别的可见性。您可以选择仅显示父类别或显示所有嵌套类别。',
                        'preview-default' => '预览默认菜单',
                        'preview-sidebar' => '预览侧边栏菜单',
                        'sidebar'         => '侧边栏菜单',
                        'title'           => '菜单类别视图',
                    ],
                ],

                'magic-ai' => [
                    'info'  => '设置魔法AI选项，并允许一些选项自动创建内容。',
                    'title' => '魔法AI',

                    'settings' => [
                        'api-key'        => 'API密钥',
                        'enabled'        => '已启用',
                        'llm-api-domain' => 'LLM API域',
                        'organization'   => '组织',
                        'title'          => '常规设置',
                        'title-info'     => '通过输入您的独家API密钥和指示相关组织来增强您使用魔法AI功能的体验。掌握OpenAI凭据并根据您的特定需求自定义设置。',
                    ],

                    'content-generation' => [
                        'category-description-prompt'      => '类别描述提示',
                        'cms-page-content-prompt'          => 'CMS页面内容提示',
                        'enabled'                          => '已启用',
                        'product-description-prompt'       => '产品描述提示',
                        'product-short-description-prompt' => '产品简短描述提示',
                        'title'                            => '内容生成',
                        'title-info'                       => '此功能将为每个所见即所得编辑器启用魔法AI，您可以使用AI管理内容。<br/><br/>启用后，转到任何编辑器生成内容。',
                    ],

                    'image-generation' => [
                        'enabled'    => '已启用',
                        'title'      => '图像生成',
                        'title-info' => '此功能将为每个图像上传启用魔法AI，您可以使用DALL-E生成图像。<br/><br/>启用后，转到任何图像上传生成图像。',
                    ],

                    'review-translation' => [
                        'deepseek-r1-8b'    => 'DeepSeek R1 (8b)',
                        'enabled'           => '启用',
                        'gemini-2-0-flash'  => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'       => 'OpenAI gpt-4 Turbo',
                        'gpt-4o'            => 'OpenAI gpt-4o',
                        'gpt-4o-mini'       => 'OpenAI gpt-4o mini',
                        'llama-groq'        => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'       => 'Llama 3.1 (8B)',
                        'llama3-2-1b'       => 'Llama 3.2 (1B)',
                        'llama3-2-3b'       => 'Llama 3.2 (3B)',
                        'llama3-8b'         => 'Llama 3 (8B)',
                        'llava-7b'          => 'Llava (7b)',
                        'mistral-7b'        => 'Mistral (7b)',
                        'model'             => '模型',
                        'orca-mini'         => 'Orca Mini',
                        'phi3-5'            => 'Phi 3.5',
                        'qwen2-5-0-5b'      => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'      => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'       => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'        => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'        => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'    => 'Starling-lm (7b)',
                        'title'             => '评论翻译',
                        'title-info'        => '为客户或访客提供将客户评论翻译成英语的选项。<br/><br/>启用后，转到评论页面，如果评论不是英语，您会找到“翻译成英语”按钮。',
                        'vicuna-13b'        => 'Vicuna (13b)',
                        'vicuna-7b'         => 'Vicuna (7b)',
                    ],

                    'checkout-message' => [
                        'deepseek-r1-8b'    => 'DeepSeek R1 (8b)',
                        'enabled'           => '启用',
                        'gemini-2-0-flash'  => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'       => 'OpenAI gpt 4 Turbo',
                        'gpt-4o'            => 'OpenAI gpt-4o',
                        'gpt-4o-mini'       => 'OpenAI gpt-4o mini',
                        'llama-groq'        => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'       => 'Llama 3.1 (8B)',
                        'llama3-2-1b'       => 'Llama 3.2 (1B)',
                        'llama3-2-3b'       => 'Llama 3.2 (3B)',
                        'llama3-8b'         => 'Llama 3 (8B)',
                        'llava-7b'          => 'Llava (7b)',
                        'mistral-7b'        => 'Mistral (7b)',
                        'model'             => '模型',
                        'orca-mini'         => 'Orca Mini',
                        'phi3-5'            => 'Phi 3.5',
                        'prompt'            => '提示',
                        'qwen2-5-0-5b'      => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'      => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'       => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'        => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'        => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'    => 'Starling-lm (7b)',
                        'title'             => '个性化结账消息',
                        'title-info'        => '为客户在感谢页面上制作个性化的结账消息，定制内容以符合个人偏好，增强整体购买后的体验。',
                        'vicuna'            => 'Vicuna',
                        'vicuna-13b'        => 'Vicuna (13b)',
                        'vicuna-7b'         => 'Vicuna (7b)',
                    ],
                ],

                'sitemap' => [
                    'info'  => '设置站点地图选项。',
                    'title' => '站点地图',

                    'settings' => [
                        'enabled' => '启用',
                        'info'    => '启用或禁用您网站的站点地图，以提高搜索引擎优化并增强用户体验。',
                        'title'   => '设置',
                    ],

                    'file-limits' => [
                        'info'             => '设置文件限制选项。',
                        'max-file-size'    => '最大文件大小',
                        'max-url-per-file' => '每个文件的最大 URL 数量',
                        'title'            => '文件限制',
                    ],
                ],
            ],

            'gdpr' => [
                'title' => 'GDPR',
                'info'  => 'GDPR 合规设置',

                'settings' => [
                    'title'   => 'GDPR 合规设置',
                    'info'    => '管理 GDPR 合规设置，包括隐私政策。根据要求启用或禁用 GDPR 功能。',
                    'enabled' => '启用 GDPR',
                ],

                'agreement' => [
                    'title'          => 'GDPR 协议',
                    'info'           => '根据 GDPR 规定管理客户同意。启用必要的数据收集和处理同意。',
                    'enable'         => '启用客户同意',
                    'checkbox-label' => '同意标签',
                    'content'        => '同意内容',
                ],

                'cookie' => [
                    'bottom-left'  => '左下角',
                    'bottom-right' => '右下角',
                    'center'       => '中心',
                    'description'  => '描述',
                    'enable'       => '启用 Cookie 通知',
                    'identifier'   => '静态块 ID',
                    'info'         => '配置 Cookie 同意设置，以通知用户数据收集和隐私政策。',
                    'position'     => 'Cookie 块位置',
                    'title'        => 'Cookie 通知设置',
                    'top-left'     => '左上角',
                    'top-right'    => '右上角',
                ],

                'cookie-consent' => [
                    'title'                  => '管理 Cookie 设置',
                    'info'                   => '选择首选的 Cookie 设置以管理数据使用。为不同类型的 Cookie 配置同意选项。',
                    'strictly-necessary'     => '严格必要',
                    'basic-interaction'      => '基本交互和功能',
                    'experience-enhancement' => '体验增强',
                    'measurement'            => '测量',
                    'targeting-advertising'  => '目标广告',
                ],
            ],

            'catalog' => [
                'info'  => '目录',
                'title' => '目录',

                'products' => [
                    'info'  => '产品查看页面，购物车查看页面，店面，评论和属性社交分享。',
                    'title' => '产品',

                    'settings' => [
                        'compare-options'     => '比较选项',
                        'image-search-option' => '图片搜索选项',
                        'title'               => '设置',
                        'title-info'          => '设置是指根据用户的偏好和要求，对系统、应用程序或设备的行为进行可配置的选择。',
                        'wishlist-options'    => '心愿单选项',
                    ],

                    'search' => [
                        'admin-mode'            => '管理员搜索模式',
                        'admin-mode-info'       => '管理员面板中的超级搜索、数据网格和其他搜索功能将基于所选的搜索引擎。',
                        'database'              => '数据库',
                        'elastic'               => 'Elastic Search',
                        'max-query-length'      => '最大查询长度',
                        'max-query-length-info' => '设置搜索查询的最大查询长度。',
                        'min-query-length'      => '最小查询长度',
                        'min-query-length-info' => '设置搜索查询的最小查询长度。',
                        'search-engine'         => '搜索引擎',
                        'storefront-mode'       => '商店前端搜索模式',
                        'storefront-mode-info'  => '商店前端的搜索功能将基于所选的搜索引擎，包括分类页面、搜索页面和其他搜索功能。',
                        'title'                 => '搜索',
                        'title-info'            => '要设置产品搜索的搜索引擎，您可以根据需求选择数据库和Elasticsearch之间的选择。如果您有大量的产品，建议使用Elasticsearch。',
                    ],

                    'guest-checkout' => [
                        'allow-guest-checkout'      => '允许访客结账',
                        'allow-guest-checkout-hint' => '提示：如果打开此选项，可以针对每个产品进行配置。',
                        'title'                     => '访客结账',
                        'title-info'                => '访客结账允许客户在不创建帐户的情况下购买产品，简化购买流程，提供更便捷和快速的交易。',
                    ],

                    'product-view-page' => [
                        'allow-no-of-related-products'  => '允许相关产品的数量',
                        'allow-no-of-up-sells-products' => '允许上销售产品的数量',
                        'title'                         => '产品查看页面配置',
                        'title-info'                    => '产品查看页面配置涉及调整产品显示页面上的布局和元素，提升用户体验和信息呈现。',
                    ],

                    'cart-view-page' => [
                        'allow-no-of-cross-sells-products' => '允许交叉销售产品的数量',
                        'title'                            => '购物车查看页面配置',
                        'title-info'                       => '购物车查看页面配置涉及在购物车页面上排列商品、详细信息和选项，提高用户交互和购买流程。',
                    ],

                    'storefront' => [
                        'buy-now-button-display' => '允许客户直接购买产品',
                        'cheapest-first'         => '最便宜的优先',
                        'comma-separated'        => '逗号分隔',
                        'default-list-mode'      => '默认列表模式',
                        'expensive-first'        => '最贵的优先',
                        'from-a-z'               => '从A到Z',
                        'from-z-a'               => '从Z到A',
                        'grid'                   => '网格',
                        'latest-first'           => '最新的优先',
                        'list'                   => '列表',
                        'oldest-first'           => '最旧的优先',
                        'products-per-page'      => '每页产品数量',
                        'sort-by'                => '排序方式',
                        'title'                  => '商店前端',
                        'title-info'             => '商店前端是在线商店的面向客户的界面，展示产品、分类和导航，提供无缝的购物体验。',
                    ],

                    'small-image' => [
                        'height'      => '高度',
                        'placeholder' => '小图占位符',
                        'title'       => '小图',
                        'title-info'  => '商店前端是在线商店的面向客户的界面，展示产品、分类和导航，提供无缝的购物体验。',
                        'width'       => '宽度',
                    ],

                    'medium-image' => [
                        'height'      => '高度',
                        'placeholder' => '中图占位符',
                        'title'       => '中图',
                        'title-info'  => '中图是指中等大小的图片，提供了详细信息和屏幕空间之间的平衡，通常用于视觉效果。',
                        'width'       => '宽度',
                    ],

                    'large-image' => [
                        'height'      => '高度',
                        'placeholder' => '大图占位符',
                        'title'       => '大图',
                        'title-info'  => '大图代表提供增强细节和视觉冲击力的高分辨率图片，通常用于展示产品或图形。',
                        'width'       => '宽度',
                    ],

                    'review' => [
                        'allow-customer-review'   => '允许客户评价',
                        'allow-guest-review'      => '允许访客评价',
                        'censoring-reviewer-name' => '审查者姓名审查',
                        'display-review-count'    => '显示评级的评论计数。',
                        'display-star-count'      => '显示评分中的星级数。',
                        'summary'                 => '概括',
                        'title'                   => '评价',
                        'title-info'              => '对某物进行评估或评价，通常涉及意见和反馈。',
                    ],

                    'attribute' => [
                        'file-upload-size'  => '允许文件上传大小（以KB为单位）',
                        'image-upload-size' => '允许图片上传大小（以KB为单位）',
                        'title'             => '属性',
                        'title-info'        => '定义对象的特性或属性，影响其行为、外观或功能。',
                    ],

                    'social-share' => [
                        'title-info'                  => '配置社交分享设置，以在Instagram、Twitter、WhatsApp、Facebook、Pinterest、LinkedIn和电子邮件中启用产品分享。',
                        'title'                       => '社交分享',
                        'share-message'               => '分享消息',
                        'share'                       => '分享',
                        'enable-social-share'         => '启用社交分享？',
                        'enable-share-whatsapp-info'  => '仅在移动设备上显示WhatsApp分享链接。',
                        'enable-share-whatsapp'       => '在WhatsApp中启用分享？',
                        'enable-share-twitter'        => '在Twitter中启用分享？',
                        'enable-share-pinterest'      => '在Pinterest中启用分享？',
                        'enable-share-linkedin'       => '在Linkedin中启用分享？',
                        'enable-share-facebook'       => '在Facebook中启用分享？',
                        'enable-share-email'          => '在电子邮件中启用分享？',
                    ],
                ],

                'rich-snippets' => [
                    'info'  => '设置产品和分类。',
                    'title' => '富媒体片段',

                    'products' => [
                        'enable'          => '启用',
                        'show-categories' => '显示分类',
                        'show-images'     => '显示图片',
                        'show-offers'     => '显示优惠',
                        'show-ratings'    => '显示评级',
                        'show-reviews'    => '显示评价',
                        'show-sku'        => '显示SKU',
                        'show-weight'     => '显示重量',
                        'title'           => '产品',
                        'title-info'      => '配置产品设置，包括SKU、重量、类别、图像、评论、评分、优惠等。',
                    ],

                    'categories' => [
                        'enable'                  => '启用',
                        'show-search-input-field' => '显示搜索输入框',
                        'title'                   => '分类',
                        'title-info'              => '“分类”是指帮助组织和分组类似产品或物品，以便更轻松地浏览和导航的组或类别。',
                    ],
                ],

                'inventory' => [
                    'title'      => '库存',
                    'title-info' => '配置库存设置以允许缺货订单并定义缺货阈值。',

                    'product-stock-options' => [
                        'allow-back-orders'       => '允许缺货订单',
                        'max-qty-allowed-in-cart' => '购物车中允许的最大数量',
                        'min-qty-allowed-in-cart' => '购物车中允许的最小数量',
                        'out-of-stock-threshold'  => '缺货阈值',
                        'title'                   => '产品库存选项',
                        'info'                    => '配置产品库存选项，允许缺货订单，设置购物车的最小和最大数量，并定义缺货门槛。',
                    ],
                ],
            ],

            'customer' => [
                'info'  => '顧客',
                'title' => '顧客',

                'address' => [
                    'info'  => '设置国家、省/直辖市、邮政编码和地址行。',
                    'title' => '地址',

                    'requirements' => [
                        'city'       => '市区',
                        'country'    => '国家',
                        'state'      => '省/直辖市',
                        'title'      => '要求',
                        'title-info' => '要求是实现某个目标所必需的条件、功能或规格。',
                        'zip'        => '邮政编码',
                    ],

                    'information' => [
                        'street-lines' => '地址行',
                        'title'        => '信息',
                        'title-info'   => '“地址行”指的是地址的各个部分，通常用逗号分隔，提供如门牌号、街道名称、市区等位置信息。',
                    ],
                ],

                'captcha' => [
                    'info'  => '设置站点密钥、密钥和状态。',
                    'title' => 'Google 验证码',

                    'credentials' => [
                        'secret-key' => '密钥',
                        'site-key'   => '站点密钥',
                        'status'     => '状态',
                        'title'      => '凭证',
                        'title-info' => '“站点地图：网站布局地图，供搜索引擎使用。密钥：用于数据加密、认证或API访问保护的安全代码。”',
                    ],

                    'validations' => [
                        'captcha'  => '发生错误，请重试。',
                        'required' => '请选择验证码。',
                    ],
                ],

                'settings' => [
                    'settings-info' => '设置愿望清单、登录重定向、新闻通讯订阅、默认组选项、电子邮件验证和社交登录。',
                    'title'         => '設定',

                    'login-as-customer' => [
                        'allow-option' => '允许以顾客身份登录',
                        'title'        => '以顾客身份登录',
                        'title-info'   => '启用“以顾客身份登录”功能。',
                    ],

                    'wishlist' => [
                        'allow-option' => '允许使用愿望清单选项',
                        'title'        => '愿望清单',
                        'title-info'   => '启用或禁用愿望清单选项。',
                    ],

                    'login-options' => [
                        'account'          => '账户',
                        'home'             => '主页',
                        'redirect-to-page' => '将顾客重定向到选定页面',
                        'title'            => '登录选项',
                        'title-info'       => '设置登录选项，确定顾客登录后的重定向页面。',
                    ],

                    'create-new-account-option' => [
                        'news-letter'      => '允许订阅新闻通讯',
                        'news-letter-info' => '在注册页面启用新闻通讯订阅选项。',
                        'title'            => '新建账户选项',
                        'title-info'       => '设置新账户的选项，包括默认顾客组的分配和注册时的新闻通讯订阅选项。',

                        'default-group' => [
                            'general'    => '一般',
                            'guest'      => '访客',
                            'title'      => '默认组',
                            'title-info' => '为新顾客分配特定的默认顾客组。',
                            'wholesale'  => '批发',
                        ],
                    ],

                    'newsletter' => [
                        'subscription' => '允许订阅通讯信息',
                        'title'        => '通讯信息',
                        'title-info'   => '“通讯信息”是通过电子邮件定期向订阅者发送的更新、优惠或内容，帮助他们保持了解和参与。',
                    ],

                    'email' => [
                        'email-verification' => '允许电子邮件验证',
                        'title'              => '电子邮件验证',
                        'title-info'         => '“电子邮件验证”通过发送确认链接来验证电子邮件地址的有效性，增强账户安全性和通信可靠性。',
                    ],

                    'social-login' => [
                        'title' => '社交登录',
                        'info'  => '"社交登录"允许用户通过他们的社交媒体账户访问网站，从而简化注册和登录过程。',

                        'google' => [
                            'enable-google' => '启用Google',

                            'client-id' => [
                                'title'      => '客户端ID',
                                'title-info' => 'Google在创建OAuth应用时提供的唯一标识符。',
                            ],

                            'client-secret' => [
                                'title'      => '客户端密钥',
                                'title-info' => '与您的Google OAuth客户端相关联的密钥。请保密。',
                            ],

                            'redirect' => [
                                'title'      => '重定向URL',
                                'title-info' => '用户在通过Google认证后被重定向的回调URL。必须与您在Google控制台中配置的URL匹配。',
                            ],
                        ],

                        'facebook' => [
                            'enable-facebook' => '启用Facebook',

                            'client-id' => [
                                'title'      => '客户端ID',
                                'title-info' => '在Facebook开发者控制台创建应用时，Facebook提供的应用ID。',
                            ],

                            'client-secret' => [
                                'title'      => '客户端密钥',
                                'title-info' => '与您的Facebook应用关联的密钥。请保密。',
                            ],

                            'redirect' => [
                                'title'      => '重定向URL',
                                'title-info' => '用户在通过Facebook认证后被重定向的回调URL。必须与您在Facebook应用设置中配置的URL匹配。',
                            ],
                        ],

                        'github' => [
                            'enable-github' => '启用GitHub',

                            'client-id' => [
                                'title'      => '客户端ID',
                                'title-info' => 'GitHub在创建OAuth应用时提供的唯一标识符。',
                            ],

                            'client-secret' => [
                                'title'      => '客户端密钥',
                                'title-info' => '与您的GitHub OAuth客户端相关联的密钥。请保密。',
                            ],

                            'redirect' => [
                                'title'      => '重定向URL',
                                'title-info' => '用户在通过GitHub认证后被重定向的回调URL。必须与您在GitHub控制台中配置的URL匹配。',
                            ],
                        ],

                        'linkedin' => [
                            'enable-linkedin' => '启用LinkedIn',

                            'client-id' => [
                                'title'      => '客户端ID',
                                'title-info' => 'LinkedIn在创建OAuth应用时提供的唯一标识符。',
                            ],

                            'client-secret' => [
                                'title'      => '客户端密钥',
                                'title-info' => '与您的LinkedIn OAuth客户端相关联的密钥。请保密。',
                            ],

                            'redirect' => [
                                'title'      => '重定向URL',
                                'title-info' => '用户在通过LinkedIn认证后被重定向的回调URL。必须与您在LinkedIn控制台中配置的URL匹配。',
                            ],
                        ],

                        'twitter' => [
                            'enable-twitter' => '启用Twitter',

                            'client-id' => [
                                'title'      => '客户端ID',
                                'title-info' => 'Twitter在创建OAuth应用时提供的唯一标识符。',
                            ],

                            'client-secret' => [
                                'title'      => '客户端密钥',
                                'title-info' => '与您的Twitter OAuth客户端相关联的密钥。请保密。',
                            ],

                            'redirect' => [
                                'title'      => '重定向URL',
                                'title-info' => '用户在通过Twitter认证后被重定向的回调URL。必须与您在Twitter控制台中配置的URL匹配。',
                            ],
                        ],
                    ],
                ],
            ],

            'email' => [
                'info'  => '电子邮件',
                'title' => '电子邮件',

                'email-settings' => [
                    'admin-email'           => '管理员邮箱',
                    'admin-email-tip'       => '此频道的管理员邮箱地址，用于接收邮件',
                    'admin-name'            => '管理员名称',
                    'admin-name-tip'        => '此名称将显示在所有管理员邮件中',
                    'admin-page-limit'      => '默认每页显示的项目数量（管理员）',
                    'contact-email'         => '联系邮箱',
                    'contact-email-tip'     => '显示在邮件底部的邮箱地址',
                    'contact-name'          => '联系名称',
                    'contact-name-tip'      => '显示在邮件底部的名称',
                    'email-sender-name'     => '邮件发送者名称',
                    'email-sender-name-tip' => '此名称将显示在客户的收件箱中',
                    'info'                  => '设置邮件发送者名称、商店邮箱地址、管理员名称和管理员邮箱地址。',
                    'shop-email-from'       => '商店邮箱地址',
                    'shop-email-from-tip'   => '用于向客户发送邮件的此频道的邮箱地址',
                    'title'                 => '电子邮件设置',
                ],

                'notifications' => [
                    'cancel-order'                                     => '取消订单后向客户发送通知',
                    'cancel-order-mail-to-admin'                       => '取消订单后向管理员发送通知邮件',
                    'customer'                                         => '注册后向客户发送账户凭证',
                    'customer-registration-confirmation-mail-to-admin' => '客户注册后向管理员发送确认邮件',
                    'info'                                             => '配置接收账户验证、订单确认、发票更新、退款、发货和订单取消的邮件。',
                    'new-inventory-source'                             => '创建发货后向库存来源发送通知邮件',
                    'new-invoice'                                      => '创建新发票后向客户发送通知邮件',
                    'new-invoice-mail-to-admin'                        => '创建新发票后向管理员发送通知邮件',
                    'new-order'                                        => '下新订单后向客户发送确认邮件',
                    'new-order-mail-to-admin'                          => '下新订单后向管理员发送确认邮件',
                    'new-refund'                                       => '创建退款后向客户发送通知邮件',
                    'new-refund-mail-to-admin'                         => '创建新退款后向管理员发送通知邮件',
                    'new-shipment'                                     => '创建发货后向客户发送通知邮件',
                    'new-shipment-mail-to-admin'                       => '创建新发货后向管理员发送通知邮件',
                    'registration'                                     => '客户注册后发送确认邮件',
                    'title'                                            => '通知',
                    'verification'                                     => '客户注册后发送验证邮件',
                ],
            ],

            'sales' => [
                'info'  => '销售',
                'title' => '销售',

                'shipping-setting' => [
                    'info'  => '配置配送设置，包括国家、省、市、地址、邮政编码、商店名称、增值税号和联系方式等。',
                    'title' => '配送设置',

                    'origin' => [
                        'bank-details'   => '银行详细信息',
                        'city'           => '城市',
                        'contact-number' => '联系电话',
                        'country'        => '国家',
                        'state'          => '省',
                        'store-name'     => '商店名称',
                        'street-address' => '街道地址',
                        'title'          => '发货来源',
                        'title-info'     => '发货来源指的是商品在运输到目的地之前的起始地点。',
                        'vat-number'     => '增值税号',
                        'zip'            => '邮政编码',
                    ],
                ],

                'shipping-methods' => [
                    'info'  => '根据需要设置配送方式，包括免费配送、固定运费等。',
                    'title' => '配送方式',

                    'free-shipping' => [
                        'description' => '描述',
                        'page-title'  => '免费配送',
                        'status'      => '状态',
                        'title'       => '标题',
                        'title-info'  => '“免费配送”是指免收运费，由卖家承担将商品配送给买家的费用。',
                    ],

                    'flat-rate-shipping' => [
                        'description' => '描述',
                        'page-title'  => '固定运费',
                        'rate'        => '费用',
                        'status'      => '状态',
                        'title'       => '标题',
                        'title-info'  => '固定运费是指无论包裹的重量、大小或距离如何，始终收取固定金额的运费。这种方式简化了运费计算，对买卖双方都有利。',
                        'type'        => [
                            'per-order' => '每订单',
                            'per-unit'  => '每单位',
                            'title'     => '类型',
                        ],
                    ],
                ],

                'payment-methods' => [
                    'accepted-currencies'            => '接受的货币',
                    'accepted-currencies-info'       => '请以逗号分隔添加货币代码。例如：USD、INR、...',
                    'business-account'               => '商业账户',
                    'cash-on-delivery'               => '货到付款',
                    'cash-on-delivery-info'          => '客户在收到商品或服务时以现金支付的付款方式。',
                    'client-id'                      => '客户端 ID',
                    'client-id-info'                 => '测试时使用“sb”。',
                    'client-secret'                  => '客户端密钥',
                    'client-secret-info'             => '在此处添加密钥。',
                    'description'                    => '描述',
                    'generate-invoice'               => '在订单后自动生成发票',
                    'generate-invoice-applicable'    => '适用于自动生成发票的情况',
                    'info'                           => '设置支付方式的信息',
                    'instructions'                   => '说明',
                    'logo'                           => '标志',
                    'logo-information'               => '图像分辨率应为 55px × 45px',
                    'mailing-address'                => '邮寄地址',
                    'money-transfer'                 => '汇款',
                    'money-transfer-info'            => '资金的转移，通常以电子方式进行。可用于交易、支付等多种目的。',
                    'page-title'                     => '支付方式',
                    'paid'                           => '已支付',
                    'paypal-smart-button'            => 'PayPal',
                    'paypal-smart-button-info'       => 'PayPal 智能按钮：一种定制化的按钮，用于简化在线支付，提供多种安全的交易方式。',
                    'paypal-standard'                => '标准 PayPal',
                    'paypal-standard-info'           => '标准 PayPal 提供基础支付选项，允许客户使用 PayPal 账户或信用/借记卡支付。',
                    'pending'                        => '待处理',
                    'pending-payment'                => '待处理付款',
                    'processing'                     => '处理中',
                    'sandbox'                        => '沙盒',
                    'set-invoice-status'             => '设置发票生成后的状态',
                    'set-order-status'               => '设置发票生成后的订单状态',
                    'sort-order'                     => '排序',
                    'status'                         => '状态',
                    'title'                          => '标题',
                ],

                'order-settings' => [
                    'info'               => '设置订单编号、最低订单量和预购选项。',
                    'title'              => '订单设置',

                    'order-number' => [
                        'generator'   => '订单编号生成器',
                        'info'        => '为每个订单分配唯一的标识符，用于跟踪和管理整个购买流程。',
                        'length'      => '订单编号长度',
                        'prefix'      => '订单编号前缀',
                        'suffix'      => '订单编号后缀',
                        'title'       => '订单编号设置',
                    ],

                    'minimum-order' => [
                        'description'             => '描述',
                        'enable'                  => '启用',
                        'include-discount-amount' => '包括折扣金额',
                        'include-tax-amount'      => '包括税额',
                        'info'                    => '设置最低要求的订单数量或金额，以便订单能够被处理或享受优惠。',
                        'minimum-order-amount'    => '最低订单金额',
                        'title'                   => '最低订单设置',
                    ],

                    'reorder' => [
                        'admin-reorder'      => '管理员重新订购',
                        'admin-reorder-info' => '启用或禁用管理员用户的重新订购功能。',
                        'info'               => '启用或禁用店铺用户的重新订购功能。',
                        'shop-reorder'       => '店铺重新订购',
                        'shop-reorder-info'  => '启用或禁用店铺用户的重新订购功能。',
                        'title'              => '允许重新订购',
                    ],

                    'stock-options' => [
                        'allow-back-orders' => '允许预购',
                        'info'              => '库存选项是投资合同，允许以特定价格买入或卖出公司的股票，影响潜在利润。',
                        'title'             => '库存选项',
                    ],
                ],

                'invoice-settings' => [
                    'info'  => '设置发票编号、支付条款、发票设计和提醒功能。',
                    'title' => '发票设置',

                    'invoice-number' => [
                        'generator'  => '发票编号生成器',
                        'info'       => '生成并分配唯一的发票编号，用于组织和跟踪。',
                        'length'     => '发票编号长度',
                        'prefix'     => '发票编号前缀',
                        'suffix'     => '发票编号后缀',
                        'title'      => '发票编号设置',
                    ],

                    'payment-terms' => [
                        'due-duration'      => '到期时间',
                        'due-duration-day'  => ':due-duration 天',
                        'due-duration-days' => ':due-duration 天',
                        'info'              => '定义买方需要支付卖方的时间和方式。',
                        'title'             => '支付条款',
                    ],

                    'pdf-print-outs' => [
                        'footer-text'      => '页脚文字',
                        'footer-text-info' => '输入将显示在PDF页脚中的文本。',
                        'info'             => '设置 PDF 打印输出，以在页眉中显示发票 ID、订单 ID，并包含发票 Logo。',
                        'invoice-id-info'  => '设置是否在发票页眉中显示发票 ID。',
                        'invoice-id-title' => '在页眉显示发票 ID',
                        'logo'             => 'Logo',
                        'logo-info'        => '图像分辨率应为 131px X 30px。',
                        'order-id-info'    => '设置是否在发票页眉中显示订单 ID。',
                        'order-id-title'   => '在页眉显示订单 ID',
                        'title'            => 'PDF 打印输出',
                    ],

                    'invoice-reminders' => [
                        'info'                       => '自动通知客户有关发票即将到期、接近付款期限或已逾期的信息。',
                        'interval-between-reminders' => '提醒间隔',
                        'maximum-limit-of-reminders' => '提醒的最大次数',
                        'title'                      => '发票提醒',
                    ],
                ],

                'taxes' => [
                    'title'      => '税费',
                    'title-info' => '税费是政府对商品、服务或交易征收的强制性费用，由卖方代为收取并上缴税务机关。',

                    'categories' => [
                        'title'      => '税费类别',
                        'title-info' => '税费类别是对不同税种（如销售税、增值税、消费税等）的分类，用于对产品或服务进行税率分类和应用。',
                        'product'    => '产品默认税费类别',
                        'shipping'   => '运费税费类别',
                        'none'       => '无',
                    ],

                    'calculation' => [
                        'title'            => '计算设置',
                        'title-info'       => '与商品或服务的价格、折扣、税费、附加费用等相关的详细信息。',
                        'based-on'         => '计算依据',
                        'shipping-address' => '收货地址',
                        'billing-address'  => '账单地址',
                        'shipping-origin'  => '发货地',
                        'product-prices'   => '产品价格',
                        'shipping-prices'  => '运费',
                        'excluding-tax'    => '未含税',
                        'including-tax'    => '含税',
                    ],

                    'default-destination-calculation' => [
                        'default-country'   => '默认国家',
                        'default-post-code' => '默认邮政编码',
                        'default-state'     => '默认省/州',
                        'title'             => '默认目的地计算',
                        'title-info'        => '根据预定义的元素或设置，自动确定标准或初始目的地。',
                    ],

                    'shopping-cart' => [
                        'title'                   => '购物车显示设置',
                        'title-info'              => '设置购物车中税费的显示方式。',
                        'display-prices'          => '显示价格',
                        'display-subtotal'        => '显示小计',
                        'display-shipping-amount' => '显示运费',
                        'excluding-tax'           => '未含税',
                        'including-tax'           => '含税',
                        'both'                    => '同时显示未含税和含税',
                    ],

                    'sales' => [
                        'title'                   => '订单、发票和退款显示设置',
                        'title-info'              => '设置订单、发票和退款中税费的显示方式。',
                        'display-prices'          => '显示价格',
                        'display-subtotal'        => '显示小计',
                        'display-shipping-amount' => '显示运费',
                        'excluding-tax'           => '未含税',
                        'including-tax'           => '含税',
                        'both'                    => '同时显示未含税和含税',
                    ],
                ],

                'checkout' => [
                    'title' => '结账',
                    'info'  => '设置访客结账功能，启用或禁用迷你购物车和购物车摘要。',

                    'shopping-cart' => [
                        'cart-page'              => '购物车页面',
                        'cart-page-info'         => '控制购物车页面的显示以改善用户购物体验。',
                        'cross-sell'             => '交叉销售商品',
                        'cross-sell-info'        => '启用交叉销售商品以增加额外的销售机会。',
                        'estimate-shipping'      => '估算运费',
                        'estimate-shipping-info' => '启用运费估算功能，提前提供运费信息。',
                        'guest-checkout'         => '允许访客结账',
                        'guest-checkout-info'    => '启用访客结账功能，实现快捷和无障碍的购买流程。',
                        'info'                   => '通过启用访客结账、购物车页面、交叉销售商品和运费估算，提升用户便利性，简化购物流程并提高销售额。',
                        'title'                  => '购物车',
                    ],

                    'my-cart' => [
                        'display-item-quantities' => '显示商品数量',
                        'display-number-in-cart'  => '显示购物车中的商品数量',
                        'info'                    => '启用“我的购物车”设置以显示商品数量概览，并显示购物车中商品总数，方便追踪。',
                        'summary'                 => '概要',
                        'title'                   => '我的购物车',
                    ],

                    'mini-cart' => [
                        'display-mini-cart'    => '显示迷你购物车',
                        'info'                 => '启用迷你购物车设置以显示迷你购物车，并显示迷你购物车优惠信息，便于快速访问购物车详情和促销活动。',
                        'mini-cart-offer-info' => '迷你购物车优惠信息',
                        'title'                => '迷你购物车',
                    ],
                ],
            ],
        ],
    ],

    'components' => [
        'layouts' => [
            'header' => [
                'account-title' => '账户',
                'app-version'   => '版本：:version',
                'logout'        => '退出',
                'my-account'    => '我的账户',
                'notifications' => '通知',
                'visit-shop'    => '访问商店',

                'mega-search' => [
                    'categories'                      => '分类',
                    'customers'                       => '客户',
                    'explore-all-categories'          => '浏览所有分类',
                    'explore-all-customers'           => '浏览所有客户',
                    'explore-all-matching-categories' => '浏览所有与“:query”匹配的分类（:count）',
                    'explore-all-matching-customers'  => '浏览所有与“:query”匹配的客户（:count）',
                    'explore-all-matching-orders'     => '浏览所有与“:query”匹配的订单（:count）',
                    'explore-all-matching-products'   => '浏览所有与“:query”匹配的产品（:count）',
                    'explore-all-orders'              => '浏览所有订单',
                    'explore-all-products'            => '浏览所有产品',
                    'orders'                          => '订单',
                    'products'                        => '产品',
                    'sku'                             => 'SKU：:sku',
                    'title'                           => '超级搜索',
                ],
            ],

            'sidebar' => [
                'attribute-families'       => '属性族',
                'attributes'               => '属性',
                'booking-product'          => '预订',
                'campaigns'                => '活动',
                'catalog'                  => '目录',
                'categories'               => '分类',
                'channels'                 => '渠道',
                'cms'                      => '内容管理系统',
                'collapse'                 => '折叠',
                'communications'           => '通信',
                'configure'                => '配置',
                'currencies'               => '货币',
                'customers'                => '客户',
                'dashboard'                => '仪表板',
                'data-transfer'            => '数据传输',
                'discount'                 => '折扣',
                'email-templates'          => '电子邮件模板',
                'events'                   => '事件',
                'exchange-rates'           => '汇率',
                'gdpr-data-requests'       => 'GDPR 数据请求',
                'groups'                   => '分组',
                'imports'                  => '导入',
                'inventory-sources'        => '库存来源',
                'invoices'                 => '发票',
                'locales'                  => '语言环境',
                'marketing'                => '营销',
                'mode'                     => '暗模式',
                'newsletter-subscriptions' => '订阅通讯',
                'orders'                   => '订单',
                'products'                 => '产品',
                'promotions'               => '促销',
                'refunds'                  => '退款',
                'reporting'                => '报告',
                'reviews'                  => '评论',
                'roles'                    => '角色',
                'sales'                    => '销售',
                'search-seo'               => '搜索与SEO',
                'search-synonyms'          => '搜索同义词',
                'search-terms'             => '搜索词',
                'settings'                 => '设置',
                'shipments'                => '发货',
                'sitemaps'                 => '站点地图',
                'tax-categories'           => '税收分类',
                'tax-rates'                => '税率',
                'taxes'                    => '税收',
                'themes'                   => '主题',
                'transactions'             => '交易',
                'url-rewrites'             => 'URL 重写',
                'users'                    => '用户',
            ],

            'powered-by' => [
                'description' => '由 :bagisto 提供支持，一个由 :webkul 社区支持的项目。',
            ],
        ],

        'datagrid' => [
            'index' => [
                'no-records-selected'              => '没有选择任何记录。',
                'must-select-a-mass-action-option' => '您必须选择批量操作的选项。',
                'must-select-a-mass-action'        => '您必须选择一项批量操作。',
            ],

            'toolbar' => [
                'length-of' => ':length 的',
                'of'        => '的',
                'per-page'  => '每页',
                'results'   => ':total 结果',
                'selected'  => ':total 已选择',

                'mass-actions' => [
                    'select-action' => '选择操作',
                    'select-option' => '选择选项',
                    'submit'        => '提交',
                ],

                'filter' => [
                    'apply-filters-btn' => '应用过滤器',
                    'back-btn'          => '返回',
                    'create-new-filter' => '创建新过滤器',
                    'custom-filters'    => '自定义过滤器',
                    'delete-error'      => '删除过滤器时出了点问题，请再试一次。',
                    'delete-success'    => '成功删除过滤器。',
                    'empty-description' => '没有可保存的选定过滤器。请选择要保存的过滤器。',
                    'empty-title'       => '添加要保存的过滤器',
                    'name'              => '名称',
                    'quick-filters'     => '快速过滤器',
                    'save-btn'          => '保存',
                    'save-filter'       => '保存过滤器',
                    'saved-success'     => '成功保存过滤器。',
                    'selected-filters'  => '已选过滤器',
                    'title'             => '过滤器',
                    'update'            => '更新',
                    'update-filter'     => '更新筛选器',
                    'updated-success'   => '过滤器已成功更新。',
                ],

                'search' => [
                    'title' => '搜索',
                ],
            ],

            'filters' => [
                'select' => '选择',
                'title'  => '过滤器',

                'dropdown' => [
                    'searchable' => [
                        'atleast-two-chars' => '请输入至少2个字符...',
                        'no-results'        => '未找到结果...',
                    ],
                ],

                'custom-filters' => [
                    'clear-all' => '清除所有',
                    'title'     => '自定义筛选',
                ],

                'boolean-options' => [
                    'false' => '假',
                    'true'  => '真',
                ],

                'date-options' => [
                    'last-month'        => '上个月',
                    'last-six-months'   => '过去6个月',
                    'last-three-months' => '过去3个月',
                    'this-month'        => '本月',
                    'this-week'         => '本周',
                    'this-year'         => '今年',
                    'today'             => '今天',
                    'yesterday'         => '昨天',
                ],
            ],

            'table' => [
                'actions'              => '操作',
                'no-records-available' => '没有可用记录。',
            ],
        ],

        'modal' => [
            'confirm' => [
                'agree-btn'    => '同意',
                'disagree-btn' => '不同意',
                'message'      => '您确定要执行此操作吗？',
                'title'        => '您确定吗？',
            ],
        ],

        'products' => [
            'search' => [
                'add-btn'       => '添加所选产品',
                'empty-info'    => '没有找到与搜索词匹配的产品。',
                'empty-title'   => '未找到任何产品',
                'product-image' => '产品图片',
                'qty'           => ':qty 可用',
                'sku'           => 'SKU - :sku',
                'title'         => '选择产品',
            ],
        ],

        'media' => [
            'images' => [
                'add-image-btn'     => '添加图片',
                'ai-add-image-btn'  => '魔法 AI',
                'ai-btn-info'       => '生成图像',
                'allowed-types'     => 'png, jpeg, jpg',
                'not-allowed-error' => '仅允许图像文件（.jpeg、.jpg、.png 等）。',

                'ai-generation' => [
                    '1024x1024'        => '1024x1024',
                    '1024x1792'        => '1024x1792',
                    '1792x1024'        => '1792x1024',
                    'apply'            => '应用',
                    'dall-e-2'         => 'Dall.E 2',
                    'dall-e-3'         => 'Dall.E 3',
                    'generate'         => '生成',
                    'generating'       => '生成中...',
                    'hd'               => '高清',
                    'model'            => '模型',
                    'number-of-images' => '图片数量',
                    'prompt'           => '提示',
                    'quality'          => '质量',
                    'regenerate'       => '重新生成',
                    'regenerating'     => '重新生成中...',
                    'size'             => '大小',
                    'standard'         => '标准',
                    'title'            => 'AI 图像生成',
                ],

                'placeholders' => [
                    'front'     => '正面',
                    'next'      => '下一个',
                    'size'      => '尺寸',
                    'use-cases' => '用途',
                    'zoom'      => '缩放',
                ],
            ],

            'videos' => [
                'add-video-btn'     => '添加视频',
                'allowed-types'     => 'mp4, webm, mkv',
                'not-allowed-error' => '仅允许视频文件（.mp4、.mov、.ogg 等）。',
            ],
        ],

        'tinymce' => [
            'ai-btn-tile' => '魔法 AI',

            'ai-generation' => [
                'apply'                  => '应用',
                'deepseek-r1-8b'         => 'DeepSeek R1 (8b)',
                'enabled'                => '启用',
                'gemini-2-0-flash'       => 'Gemini 2.0 Flash',
                'generate'               => '生成',
                'generated-content'      => '生成的内容',
                'generated-content-info' => 'AI内容可能会误导。请在应用之前审查生成的内容。',
                'generating'             => '生成中...',
                'gpt-4-turbo'            => 'OpenAI gpt-4 Turbo',
                'gpt-4o'                 => 'OpenAI gpt-4o',
                'gpt-4o-mini'            => 'OpenAI gpt-4o mini',
                'llama-groq'             => 'Llama 3.3 (Groq)',
                'llama3-1-8b'            => 'Llama 3.1 (8B)',
                'llama3-2-1b'            => 'Llama 3.2 (1B)',
                'llama3-2-3b'            => 'Llama 3.2 (3B)',
                'llama3-8b'              => 'Llama 3 (8B)',
                'llava-7b'               => 'Llava (7b)',
                'mistral-7b'             => 'Mistral (7b)',
                'model'                  => '模型',
                'orca-mini'              => 'Orca Mini',
                'phi3-5'                 => 'Phi 3.5',
                'prompt'                 => '提示',
                'qwen2-5-0-5b'           => 'Qwen 2.5 (0.5b)',
                'qwen2-5-1-5b'           => 'Qwen 2.5 (1.5b)',
                'qwen2-5-14b'            => 'Qwen 2.5 (14b)',
                'qwen2-5-3b'             => 'Qwen 2.5 (3b)',
                'qwen2-5-7b'             => 'Qwen 2.5 (7b)',
                'starling-lm-7b'         => 'Starling-lm (7b)',
                'title'                  => 'AI 辅助',
                'vicuna-13b'             => 'Vicuna (13b)',
                'vicuna-7b'              => 'Vicuna (7b)',
            ],
        ],
    ],

    'acl' => [
        'addresses'                => '地址',
        'attribute-families'       => '属性族',
        'attributes'               => '属性',
        'campaigns'                => '活动',
        'cancel'                   => '取消',
        'cart-rules'               => '购物车规则',
        'catalog-rules'            => '目录规则',
        'catalog'                  => '目录',
        'categories'               => '分类',
        'channels'                 => '渠道',
        'cms'                      => 'CMS',
        'communications'           => '通信',
        'configure'                => '配置',
        'copy'                     => '复制',
        'create'                   => '创造',
        'currencies'               => '货币',
        'customers'                => '顾客',
        'data-transfer'            => '数据传输',
        'dashboard'                => '仪表板',
        'delete'                   => '删除',
        'edit'                     => '编辑',
        'email-templates'          => '电子邮件模板',
        'events'                   => '事件',
        'exchange-rates'           => '汇率',
        'gdpr'                     => 'GDPR',
        'groups'                   => '群组',
        'import'                   => '进口',
        'imports'                  => '进口',
        'inventory-sources'        => '库存来源',
        'invoices'                 => '发票',
        'locales'                  => '区域设置',
        'marketing'                => '营销',
        'newsletter-subscriptions' => '电子报订阅',
        'note'                     => '备注',
        'orders'                   => '订单',
        'products'                 => '产品',
        'promotions'               => '促销活动',
        'refunds'                  => '退款',
        'reporting'                => '报告',
        'reviews'                  => '评论',
        'roles'                    => '角色',
        'sales'                    => '销售',
        'search-seo'               => '搜索和SEO',
        'search-synonyms'          => '搜索同义词',
        'search-terms'             => '搜索词',
        'settings'                 => '设置',
        'shipments'                => '发货',
        'sitemaps'                 => '网站地图',
        'subscribers'              => '电子报订阅者',
        'tax-categories'           => '税务类别',
        'tax-rates'                => '税率',
        'taxes'                    => '税费',
        'themes'                   => '主题',
        'transactions'             => '交易',
        'url-rewrites'             => 'URL 重写',
        'users'                    => '用户',
        'view'                     => '查看',
    ],

    'errors' => [
        'dashboard' => '仪表盘',
        'go-back'   => '返回',
        'support'   => '如果问题持续存在，请通过<a href=":link" class=":class">:email</a>联系我们以寻求帮助。',

        '404' => [
            'description' => '哎呀！您正在寻找的页面似乎在度假。似乎我们无法找到您正在搜索的内容。',
            'title'       => '404 页面未找到',
        ],

        '401' => [
            'description' => '哎呀！看起来您无权访问此页面。似乎您缺少必要的凭证。',
            'title'       => '401 未经授权',
        ],

        '403' => [
            'description' => '哎呀！此页面受限制。似乎您没有查看此内容所需的权限。',
            'title'       => '403 禁止访问',
        ],

        '500' => [
            'description' => '哎呀！出了些问题。似乎我们在加载您所寻找的页面时遇到了问题。',
            'title'       => '500 内部服务器错误',
        ],

        '503' => [
            'description' => '哎呀！看起来我们暂时停机进行维护。请稍后再查看。',
            'title'       => '503 服务不可用',
        ],
    ],

    'export' => [
        'csv'        => 'CSV',
        'download'   => '下载',
        'export'     => '导出',
        'no-records' => '没有要导出的内容',
        'xls'        => 'XLS',
        'xlsx'       => 'XLSX',
    ],

    'validations' => [
        'slug-being-used' => '此slug在类别或产品中正在使用。',
        'slug-reserved'   => '此slug已保留。',
    ],

    'footer' => [
        'copy-right' => '由 <a href="https://bagisto.com/" target="_blank">Bagisto</a> 提供支持，一个由 <a href="https://webkul.com/" target="_blank">Webkul</a> 社区支持的项目',
    ],

    'emails' => [
        'dear'   => '尊敬的 :admin_name',
        'thanks' => '如果您需要任何帮助，请联系我们：<a href=":link" style=":style">:email</a>。<br/>谢谢！',

        'admin' => [
            'forgot-password' => [
                'description'    => '您收到此电子邮件是因为我们收到了您的帐户的密码重置请求。',
                'greeting'       => '忘记密码！',
                'reset-password' => '重置密码',
                'subject'        => '重置密码电子邮件',
            ],
        ],

        'customers' => [
            'registration' => [
                'description' => '新的客户帐户已成功创建。 他们现在可以使用电子邮件地址和密码凭据登录。 登录后，他们将可以访问各种服务，包括查看过去的订单、管理愿望清单和更新帐户信息的能力。',
                'greeting'    => '我们热烈欢迎刚刚在我们这里注册的新客户：customer_name！',
                'subject'     => '新客户注册',
            ],

            'gdpr' => [
                'new-delete-request' => '新的数据删除请求',
                'new-update-request' => '新的数据更新请求',

                'new-request' => [
                    'customer-name'  => '客户名称 : ',
                    'delete-summary' => '删除请求摘要',
                    'message'        => '消息：',
                    'request-status' => '请求状态：',
                    'request-type'   => '请求类型：',
                    'update-summary' => '更新请求摘要',
                ],

                'status-update' => [
                    'subject'        => 'GDPR请求已更新',
                    'summary'        => 'GDPR请求状态已更新',
                    'request-status' => '请求状态:',
                    'request-type'   => '请求类型:',
                    'message'        => '信息:',
                ],
            ],
        ],

        'orders' => [
            'created' => [
                'greeting' => '您在 :created_at 下单了新订单 :order_id',
                'subject'  => '新订单确认',
                'summary'  => '订单摘要',
                'title'    => '订单确认！',
            ],

            'invoiced' => [
                'greeting' => '您在 :created_at 创建的订单 :order_id 的发票 #:invoice_id',
                'subject'  => '新发票确认',
                'summary'  => '发票摘要',
                'title'    => '发票确认！',
            ],

            'shipped' => [
                'greeting' => '您在 :created_at 下的订单 :order_id 已发货',
                'subject'  => '新发货确认',
                'summary'  => '发货摘要',
                'title'    => '订单已发货！',
            ],

            'inventory-source' => [
                'greeting' => '您在 :created_at 下的订单 :order_id 已发货',
                'subject'  => '新发货确认',
                'summary'  => '发货摘要',
                'title'    => '订单已发货！',
            ],

            'refunded' => [
                'greeting' => '您在 :created_at 下的订单 :order_id 已退款',
                'subject'  => '新退款确认',
                'summary'  => '退款摘要',
                'title'    => '订单已退款！',
            ],

            'canceled' => [
                'greeting' => '您在 :created_at 下的订单 :order_id 已取消',
                'subject'  => '新订单取消',
                'summary'  => '订单摘要',
                'title'    => '订单已取消！',
            ],

            'billing-address'            => '账单地址',
            'carrier'                    => '承运人',
            'contact'                    => '联系人',
            'discount'                   => '折扣',
            'excl-tax'                   => '不含税：',
            'grand-total'                => '总计',
            'name'                       => '名称',
            'payment'                    => '支付',
            'price'                      => '价格',
            'qty'                        => '数量',
            'shipping-address'           => '送货地址',
            'shipping-handling-excl-tax' => '运输和处理（不含税）',
            'shipping-handling-incl-tax' => '运输和处理（含税）',
            'shipping-handling'          => '运输和处理',
            'shipping'                   => '运输',
            'sku'                        => 'SKU',
            'subtotal-excl-tax'          => '小计（不含税）',
            'subtotal-incl-tax'          => '小计（含税）',
            'subtotal'                   => '小计',
            'tax'                        => '税费',
            'tracking-number'            => '跟踪号码：:tracking_number',
        ],
    ],
];
