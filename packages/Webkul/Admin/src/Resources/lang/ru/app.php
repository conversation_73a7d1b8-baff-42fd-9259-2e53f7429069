<?php

return [
    'users' => [
        'sessions' => [
            'email'                  => 'Адрес электронной почты',
            'forget-password-link'   => 'Забыли пароль?',
            'password'               => 'Пароль',
            'powered-by-description' => 'Работает на :bagisto, проект с открытым исходным кодом от :webkul.',
            'submit-btn'             => 'Войти',
            'title'                  => 'Войти',
        ],

        'forget-password' => [
            'create'    => [
                'email'                  => 'Зарегистрированный адрес электронной почты',
                'email-not-exist'        => 'Такой адрес электронной почты не существует',
                'page-title'             => 'Забыли пароль',
                'powered-by-description' => 'Работает на :bagisto, проект с открытым исходным кодом от :webkul.',
                'reset-link-sent'        => 'Ссылка для сброса пароля отправлена',
                'sign-in-link'           => 'Вернуться к входу?',
                'submit-btn'             => 'Сбросить',
                'title'                  => 'Восстановление пароля',
            ],
        ],

        'reset-password' => [
            'back-link-title'        => 'Вернуться к входу?',
            'confirm-password'       => 'Подтвердите пароль',
            'email'                  => 'Зарегистрированный адрес электронной почты',
            'password'               => 'Пароль',
            'powered-by-description' => 'Работает на :bagisto, проект с открытым исходным кодом от :webkul.',
            'submit-btn'             => 'Сбросить пароль',
            'title'                  => 'Сброс пароля',
        ],
    ],

    'notifications' => [
        'description-text' => 'Все уведомления в списке',
        'marked-success'   => 'Все уведомления отмечены как прочитанные',
        'no-record'        => 'Нет записей',
        'of'               => 'из',
        'per-page'         => 'На странице',
        'read-all'         => 'Пометить все как прочитанные',
        'title'            => 'Уведомления',
        'view-all'         => 'Просмотреть все',

        'order-status-messages' => [
            'all'             => 'Все',
            'canceled'        => 'Заказ отменен',
            'closed'          => 'Заказ закрыт',
            'completed'       => 'Заказ завершен',
            'pending'         => 'Заказ в ожидании',
            'pending-payment' => 'Ожидается оплата',
            'processing'      => 'Заказ в обработке',
        ],
    ],

    'account' => [
        'edit' => [
            'back-btn'          => 'Назад',
            'change-password'   => 'Изменить пароль',
            'confirm-password'  => 'Подтвердите пароль',
            'current-password'  => 'Текущий пароль',
            'email'             => 'Электронная почта',
            'general'           => 'Общее',
            'invalid-password'  => 'Текущий пароль введен неверно.',
            'name'              => 'Имя',
            'password'          => 'Пароль',
            'profile-image'     => 'Изображение профиля',
            'save-btn'          => 'Сохранить аккаунт',
            'title'             => 'Мой аккаунт',
            'update-success'    => 'Аккаунт успешно обновлен',
            'upload-image-info' => 'Загрузите изображение профиля (110px X 110px) в формате PNG или JPG',
        ],
    ],

    'dashboard' => [
        'index' => [
            'add-customer'                => 'Добавить клиента',
            'add-product'                 => 'Добавить товар',
            'all-channels'                => 'Все каналы',
            'attribute-code'              => 'Код атрибута',
            'average-sale'                => 'Средний заказ',
            'color'                       => 'Цвет',
            'customer-info'               => 'Клиенты с наибольшими продажами не найдены',
            'customer-with-most-sales'    => 'Клиент с наибольшими продажами',
            'date-duration'               => ':start - :end',
            'decreased'                   => ':progress%',
            'empty-threshold'             => 'Порог Пустоты',
            'empty-threshold-description' => 'Нет доступных товаров',
            'end-date'                    => 'Дата окончания',
            'from'                        => 'С',
            'increased'                   => ':progress%',
            'more-products'               => ':product_count+ дополнительных изображений',
            'order'                       => ':total_orders заказов',
            'order-count'                 => ':count заказов',
            'order-id'                    => '#:id',
            'overall-details'             => 'Общие сведения',
            'pay-by'                      => 'Оплата через - :method',
            'product-count'               => ':count товаров',
            'product-image'               => 'Изображение товара',
            'product-info'                => 'Добавьте связанные товары на ходу.',
            'product-number'              => 'Товар - :product_number',
            'revenue'                     => 'Доход :total',
            'sale-count'                  => ':count продаж',
            'sales'                       => 'Продажи',
            'sku'                         => 'SKU - :sku',
            'start-date'                  => 'Дата начала',
            'stock-threshold'             => 'Порог запасов',
            'store-stats'                 => 'Статистика магазина',
            'title'                       => 'Панель управления',
            'to'                          => 'По',
            'today-customers'             => 'Клиентов сегодня',
            'today-details'               => 'Сегодняшние события',
            'today-orders'                => 'Заказов сегодня',
            'today-sales'                 => 'Продаж сегодня',
            'top-performing-categories'   => 'Лучшие категории',
            'top-selling-products'        => 'Топ продаваемых товаров',
            'total-customers'             => 'Всего клиентов',
            'total-orders'                => 'Всего заказов',
            'total-sales'                 => 'Общая выручка',
            'total-stock'                 => 'Общий запас: :total_stock',
            'total-unpaid-invoices'       => 'Всего неоплаченных счетов',
            'unique-visitors'             => ':count Уникальный',
            'user-info'                   => 'Быстрый обзор текущей ситуации в вашем магазине',
            'user-name'                   => 'Привет, :user_name!',
            'visitors'                    => 'Посетитель',
        ],
    ],

    'sales' => [
        'orders' => [
            'index' => [
                'create-btn' => 'Создать заказ',
                'title'      => 'Заказы',

                'search-customer' => [
                    'create-btn'  => 'Создать клиента',
                    'empty-info'  => 'Нет доступных клиентов для данного поискового запроса.',
                    'empty-title' => 'Клиенты не найдены',
                    'search-by'   => 'Поиск по электронной почте или имени',
                    'title'       => 'Выберите клиента',
                ],

                'datagrid' => [
                    'canceled'        => 'Отменено',
                    'channel-name'    => 'Канал',
                    'closed'          => 'Закрыто',
                    'completed'       => 'Завершено',
                    'customer'        => 'Клиент',
                    'date'            => 'Дата',
                    'email'           => 'Email',
                    'fraud'           => 'Мошенничество',
                    'grand-total'     => 'Итоговая сумма',
                    'id'              => '#:id',
                    'items'           => 'Товары',
                    'location'        => 'Местоположение',
                    'order-id'        => 'Номер заказа',
                    'pay-by'          => 'Оплата через - :method',
                    'pay-via'         => 'Оплачено через',
                    'pending-payment' => 'Ожидание оплаты',
                    'pending'         => 'В ожидании',
                    'processing'      => 'Обработка',
                    'product-count'   => ':count + Еще продукты',
                    'status'          => 'Статус',
                    'success'         => 'Успешно',
                    'view'            => 'Просмотр',
                ],
            ],

            'create' => [
                'add-to-cart'             => 'Добавить в корзину',
                'back-btn'                => 'Назад',
                'check-billing-address'   => 'Отсутствует платежный адрес.',
                'check-shipping-address'  => 'Отсутствует адрес доставки.',
                'configuration'           => 'Конфигурация',
                'coupon-already-applied'  => 'Код купона уже применен.',
                'coupon-applied'          => 'Код купона успешно применен.',
                'coupon-error'            => 'Невозможно применить код купона.',
                'coupon-not-found'        => 'Код купона не найден',
                'coupon-remove'           => 'Код купона успешно удален.',
                'error'                   => 'Что-то пошло не так',
                'minimum-order-error'     => 'Минимальная сумма заказа не достигнута.',
                'order-placed-success'    => 'Заказ успешно размещен.',
                'payment-not-supported'   => 'Этот способ оплаты не поддерживается',
                'save-btn'                => 'Создать заказ',
                'specify-payment-method'  => 'Отсутствует способ оплаты.',
                'specify-shipping-method' => 'Отсутствует способ доставки.',
                'title'                   => 'Создать заказ для :name',

                'types' => [
                    'simple' => [
                        'none'         => 'Нет',
                        'total-amount' => 'Общая сумма',
                    ],

                    'configurable' => [
                        'select-options' => 'Пожалуйста, выберите опцию',
                    ],

                    'bundle' => [
                        'none'         => 'Нет',
                        'total-amount' => 'Общая сумма',
                    ],

                    'grouped' => [
                        'name' => 'Название',
                    ],

                    'downloadable' => [
                        'title' => 'Ссылки',
                    ],

                    'virtual' => [
                        'none'         => 'Нет',
                        'total-amount' => 'Общая сумма',
                    ],
                ],

                'cart' => [
                    'success-add-to-cart' => 'Товар успешно добавлен в корзину',
                    'success-remove'      => 'Товар успешно удален из корзины',
                    'success-update'      => 'Товар в корзине успешно обновлен',

                    'items' => [
                        'add-product'       => 'Добавить товар',
                        'amount-per-unit'   => ':amount за единицу x :qty Количество',
                        'delete'            => 'Удалить',
                        'empty-description' => 'В корзине нет товаров.',
                        'empty-title'       => 'Пустая корзина',
                        'excl-tax'          => 'Исключая НДС',
                        'move-to-wishlist'  => 'Переместить в список желаний',
                        'see-details'       => 'Подробнее',
                        'sku'               => 'Артикул - :sku',
                        'sub-total'         => 'Подитог - :sub_total',
                        'title'             => 'Товары в корзине',

                        'search' => [
                            'add-to-cart'   => 'Добавить в корзину',
                            'available-qty' => ':qty доступно',
                            'empty-info'    => 'Нет товаров, доступных для данного поискового запроса.',
                            'empty-title'   => 'Товары не найдены',
                            'product-image' => 'Изображение товара',
                            'qty'           => 'Количество',
                            'sku'           => 'Артикул - :sku',
                            'title'         => 'Поиск товаров',
                        ],
                    ],

                    'address' => [
                        'add-btn'          => 'Добавить адрес',
                        'add-new'          => 'Добавить новый адрес',
                        'add-new-address'  => 'Добавить новый адрес',
                        'addresses'        => 'Адреса',
                        'back'             => 'Назад',
                        'billing-address'  => 'Платежный адрес',
                        'city'             => 'Город',
                        'company-name'     => 'Название компании',
                        'confirm'          => 'Подтвердить',
                        'country'          => 'Страна',
                        'edit-btn'         => 'Редактировать адрес',
                        'email'            => 'Email',
                        'first-name'       => 'Имя',
                        'last-name'        => 'Фамилия',
                        'postcode'         => 'Индекс',
                        'proceed'          => 'Продолжить',
                        'same-as-billing'  => 'Использовать тот же адрес для доставки?',
                        'save'             => 'Сохранить',
                        'save-address'     => 'Сохранить в адресной книге',
                        'select-country'   => 'Выберите страну',
                        'select-state'     => 'Выберите регион',
                        'shipping-address' => 'Адрес доставки',
                        'state'            => 'Регион',
                        'street-address'   => 'Улица, дом',
                        'telephone'        => 'Телефон',
                        'title'            => 'Адрес',
                        'vat-id'           => 'ИНН',
                    ],

                    'payment' => [
                        'title' => 'Оплата',
                    ],

                    'shipping' => [
                        'title' => 'Доставка',
                    ],

                    'summary' => [
                        'apply-coupon'             => 'Применить купон',
                        'discount-amount'          => 'Сумма скидки',
                        'enter-your-code'          => 'Введите ваш код',
                        'grand-total'              => 'Общая сумма',
                        'place-order'              => 'Оформить заказ',
                        'processing'               => 'Обработка',
                        'shipping-amount-excl-tax' => 'Стоимость доставки (без НДС)',
                        'shipping-amount-incl-tax' => 'Стоимость доставки (с НДС)',
                        'shipping-amount'          => 'Стоимость доставки',
                        'sub-total-excl-tax'       => 'Подитог (без НДС)',
                        'sub-total-incl-tax'       => 'Подитог (с НДС)',
                        'sub-total'                => 'Подитог',
                        'tax'                      => 'НДС',
                        'title'                    => 'Резюме заказа',
                    ],
                ],

                'cart-items' => [
                    'add-to-cart'       => 'Добавить в корзину',
                    'delete'            => 'Удалить',
                    'empty-description' => 'В корзине нет товаров.',
                    'empty-title'       => 'Пустая корзина',
                    'excl-tax'          => 'Исключая НДС: ',
                    'see-details'       => 'Подробнее',
                    'sku'               => 'Артикул - :sku',
                    'title'             => 'Товары в корзине',
                ],

                'recent-order-items' => [
                    'add-to-cart'       => 'Добавить в корзину',
                    'empty-description' => 'В ваших последних заказах нет товаров.',
                    'empty-title'       => 'Пустые заказы',
                    'see-details'       => 'Подробнее',
                    'sku'               => 'Артикул - :sku',
                    'title'             => 'Товары последнего заказа',
                    'view'              => 'Просмотр',
                ],

                'wishlist-items' => [
                    'add-to-cart'       => 'Добавить в корзину',
                    'delete'            => 'Удалить',
                    'empty-description' => 'В списке желаний нет товаров.',
                    'empty-title'       => 'Пустой список желаний',
                    'see-details'       => 'Подробнее',
                    'sku'               => 'Артикул - :sku',
                    'title'             => 'Товары в списке желаний',
                ],

                'compare-items' => [
                    'add-to-cart'       => 'Добавить в корзину',
                    'delete'            => 'Удалить',
                    'empty-description' => 'В списке сравнения нет товаров.',
                    'empty-title'       => 'Пустой список сравнения',
                    'sku'               => 'Артикул - :sku',
                    'title'             => 'Товары в списке сравнения',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount за единицу x :qty количество',
                'billing-address'                => 'Платежный адрес',
                'cancel'                         => 'Отменить',
                'cancel-msg'                     => 'Вы уверены, что хотите отменить этот заказ',
                'cancel-success'                 => 'Заказ успешно отменен',
                'canceled'                       => 'Отменен',
                'channel'                        => 'Канал',
                'closed'                         => 'Закрыт',
                'comment-success'                => 'Комментарий успешно добавлен.',
                'comments'                       => 'Комментарии',
                'completed'                      => 'Завершен',
                'contact'                        => 'Контакт',
                'create-success'                 => 'Заказ успешно создан',
                'currency'                       => 'Валюта',
                'customer'                       => 'Клиент',
                'customer-group'                 => 'Группа клиентов',
                'customer-not-notified'          => ':date | Клиент <b>Не уведомлен</b>',
                'customer-notified'              => ':date | Клиент <b>Уведомлен</b>',
                'discount'                       => 'Скидка - :discount',
                'download-pdf'                   => 'Скачать PDF',
                'fraud'                          => 'Мошенничество',
                'grand-total'                    => 'Общая сумма - :grand_total',
                'invoice-id'                     => 'Счет-фактура #:invoice',
                'invoices'                       => 'Счета-фактуры',
                'item-canceled'                  => 'Отменено (:qty_canceled)',
                'item-invoice'                   => 'Выставлено счетов (:qty_invoiced)',
                'item-ordered'                   => 'Заказано (:qty_ordered)',
                'item-refunded'                  => 'Возвращено (:qty_refunded)',
                'item-shipped'                   => 'Отправлено (:qty_shipped)',
                'name'                           => 'Имя',
                'no-invoice-found'               => 'Счет-фактура не найдена',
                'no-refund-found'                => 'Возврат не найден',
                'no-shipment-found'              => 'Отправка не найдена',
                'notify-customer'                => 'Уведомить клиента',
                'order-date'                     => 'Дата заказа',
                'order-information'              => 'Информация о заказе',
                'order-status'                   => 'Статус заказа',
                'payment-and-shipping'           => 'Оплата и доставка',
                'payment-method'                 => 'Способ оплаты',
                'pending'                        => 'В ожидании',
                'pending_payment'                => 'Ожидание оплаты',
                'per-unit'                       => 'За единицу',
                'price'                          => 'Цена - :price',
                'price-excl-tax'                 => 'Цена (без учета налогов) - :price',
                'price-incl-tax'                 => 'Цена (с учетом налогов) - :price',
                'processing'                     => 'Обработка',
                'quantity'                       => 'Количество',
                'refund'                         => 'Возврат',
                'refund-id'                      => 'Возврат #:refund',
                'refunded'                       => 'Возвращено',
                'reorder'                        => 'Повторить заказ',
                'ship'                           => 'Отправить',
                'shipment'                       => 'Отправка #:shipment',
                'shipments'                      => 'Отправки',
                'shipping-address'               => 'Адрес доставки',
                'shipping-and-handling'          => 'Доставка и обработка',
                'shipping-and-handling-excl-tax' => 'Доставка и обработка (без учета налогов)',
                'shipping-and-handling-incl-tax' => 'Доставка и обработка (с учетом налогов)',
                'shipping-method'                => 'Способ доставки',
                'shipping-price'                 => 'Стоимость доставки',
                'sku'                            => 'Артикул - :sku',
                'status'                         => 'Статус',
                'sub-total'                      => 'Подитог - :sub_total',
                'sub-total-excl-tax'             => 'Подитог (без учета налогов) - :sub_total',
                'sub-total-incl-tax'             => 'Подитог (с учетом налогов) - :sub_total',
                'submit-comment'                 => 'Отправить комментарий',
                'summary-discount'               => 'Скидка',
                'summary-grand-total'            => 'Общая сумма',
                'summary-sub-total'              => 'Подитог',
                'summary-sub-total-excl-tax'     => 'Подитог (без учета налогов)',
                'summary-sub-total-incl-tax'     => 'Подитог (с учетом налогов)',
                'summary-tax'                    => 'Налоги',
                'tax'                            => 'Налог (:percent) - :tax',
                'title'                          => 'Заказ #:order_id',
                'total-due'                      => 'Итого к оплате',
                'total-paid'                     => 'Всего оплачено',
                'total-refund'                   => 'Всего возвратов',
                'view'                           => 'Просмотр',
                'write-your-comment'             => 'Напишите свой комментарий',
            ],
        ],

        'shipments' => [
            'index' => [
                'title' => 'Отгрузки',

                'datagrid' => [
                    'id'               => 'ID',
                    'inventory-source' => 'Источник инвентаря',
                    'order-date'       => 'Дата заказа',
                    'order-id'         => 'ID заказа',
                    'shipment-date'    => 'Дата отгрузки',
                    'shipment-to'      => 'Доставить в',
                    'total-qty'        => 'Общее количество',
                    'view'             => 'Просмотр',
                ],
            ],

            'create' => [
                'amount-per-unit'  => ':amount за единицу x :qty Количество',
                'cancel-error'     => 'Заказ не может быть отменен',
                'carrier-name'     => 'Наименование перевозчика',
                'create-btn'       => 'Создать отгрузку',
                'creation-error'   => 'Ошибка при создании отгрузки',
                'item-canceled'    => 'Отменено (:qty_canceled)',
                'item-invoice'     => 'Счет выставлен (:qty_invoiced)',
                'item-ordered'     => 'Заказано (:qty_ordered)',
                'item-refunded'    => 'Возвращено (:qty_refunded)',
                'item-shipped'     => 'Отправлено (:qty_shipped)',
                'order-error'      => 'Отгрузка недействительна',
                'per-unit'         => 'За единицу',
                'qty-available'    => 'Доступное количество',
                'qty-to-ship'      => 'Количество для отгрузки',
                'quantity-invalid' => 'Количество недействительно',
                'sku'              => 'SKU - :sku',
                'source'           => 'Источник',
                'success'          => 'Отгрузка успешно создана',
                'title'            => 'Создать новую отгрузку',
                'tracking-number'  => 'Номер отслеживания',
            ],

            'view' => [
                'billing-address'      => 'Платежный адрес',
                'carrier-title'        => 'Наименование перевозчика',
                'channel'              => 'Канал',
                'currency'             => 'Валюта',
                'customer'             => 'Клиент',
                'email'                => 'Email - :email',
                'inventory-source'     => 'Источник инвентаря',
                'order-date'           => 'Дата заказа',
                'order-id'             => 'ID заказа',
                'order-information'    => 'Информация о заказе',
                'order-status'         => 'Статус заказа',
                'ordered-items'        => 'Заказанные товары',
                'payment-and-shipping' => 'Оплата и доставка',
                'payment-method'       => 'Метод оплаты',
                'product-image'        => 'Изображение товара',
                'qty'                  => 'Количество - :qty',
                'shipping-address'     => 'Адрес доставки',
                'shipping-method'      => 'Метод доставки',
                'shipping-price'       => 'Стоимость доставки',
                'sku'                  => 'SKU - :sku ',
                'title'                => 'Отгрузка #:shipment_id',
                'tracking-number'      => 'Номер отслеживания',
            ],
        ],

        'refunds' => [
            'index' => [
                'title' => 'Возвраты',

                'datagrid' => [
                    'billed-to'       => 'Оплачено',
                    'id'              => 'ID',
                    'order-id'        => 'ID заказа',
                    'refund-date'     => 'Дата возврата',
                    'refunded-amount' => 'Возвращенная сумма',
                    'view'            => 'Просмотр',
                ],
            ],

            'view' => [
                'account-information'        => 'Информация об аккаунте',
                'adjustment-fee'             => 'Комиссия за корректировку',
                'adjustment-refund'          => 'Возврат корректировки',
                'base-discounted-amount'     => 'Сумма со скидкой - :base_discounted_amount',
                'billing-address'            => 'Платежный адрес',
                'currency'                   => 'Валюта',
                'sub-total-amount-excl-tax'  => 'Подытог (без учета налога) - :discounted_amount',
                'sub-total-amount-incl-tax'  => 'Подытог (с учетом налога) - :discounted_amount',
                'sub-total-amount'           => 'Подытог - :discounted_amount',
                'grand-total'                => 'Общая сумма',
                'order-channel'              => 'Канал заказа',
                'order-date'                 => 'Дата заказа',
                'order-id'                   => 'ID заказа',
                'order-information'          => 'Информация о заказе',
                'order-status'               => 'Статус заказа',
                'payment-information'        => 'Информация об оплате',
                'payment-method'             => 'Метод оплаты',
                'price-excl-tax'             => 'Цена (без учета налога) - :price',
                'price-incl-tax'             => 'Цена (с учетом налога) - :price',
                'price'                      => 'Цена - :price',
                'product-image'              => 'Изображение товара',
                'product-ordered'            => 'Заказанные товары',
                'qty'                        => 'Количество - :qty',
                'refund'                     => 'Возврат',
                'shipping-address'           => 'Адрес доставки',
                'shipping-handling-excl-tax' => 'Стоимость доставки и обработки (без учета налога)',
                'shipping-handling-incl-tax' => 'Стоимость доставки и обработки (с учетом налога)',
                'shipping-handling'          => 'Стоимость доставки и обработки',
                'shipping-method'            => 'Метод доставки',
                'shipping-price'             => 'Стоимость доставки',
                'sku'                        => 'SKU - :sku',
                'sub-total-excl-tax'         => 'Подытог (без учета налога)',
                'sub-total-incl-tax'         => 'Подытог (с учетом налога)',
                'sub-total'                  => 'Подытог',
                'tax'                        => 'Налог',
                'tax-amount'                 => 'Сумма налога - :tax_amount',
                'title'                      => 'Возврат #:refund_id',
            ],

            'create' => [
                'adjustment-fee'              => 'Комиссия за корректировку',
                'adjustment-refund'           => 'Возврат корректировки',
                'amount-per-unit'             => ':amount за единицу x :qty Количество',
                'create-success'              => 'Возврат успешно создан',
                'creation-error'              => 'Создание возврата не разрешено.',
                'discount-amount'             => 'Сумма скидки',
                'grand-total'                 => 'Общая сумма',
                'invalid-qty'                 => 'Обнаружено недействительное количество товаров для выставления счета.',
                'invalid-refund-amount-error' => 'Сумма возврата должна быть ненулевой.',
                'item-canceled'               => 'Отменено (:qty_canceled)',
                'item-invoice'                => 'Счет выставлен (:qty_invoiced)',
                'item-ordered'                => 'Заказано (:qty_ordered)',
                'item-refunded'               => 'Возвращено (:qty_refunded)',
                'item-shipped'                => 'Отправлено (:qty_shipped)',
                'per-unit'                    => 'За единицу',
                'price'                       => 'Цена',
                'qty-to-refund'               => 'Количество для возврата',
                'refund-btn'                  => 'Возврат',
                'refund-limit-error'          => 'Сумма возврата :amount не может быть выполнена.',
                'refund-shipping'             => 'Возврат стоимости доставки',
                'sku'                         => 'Артикул - :sku',
                'subtotal'                    => 'Подытог',
                'tax-amount'                  => 'Сумма налога',
                'title'                       => 'Создать возврат',
                'update-totals-btn'           => 'Обновить итоги',
            ],
        ],

        'invoices' => [
            'index' => [
                'title' => 'Счета',

                'datagrid' => [
                    'action'              => 'Действия',
                    'days-left'           => 'Осталось :count дн(я/ей)',
                    'days-overdue'        => ':count дн(я/ей) просрочено',
                    'grand-total'         => 'Общая сумма',
                    'id'                  => 'ID',
                    'invoice-date'        => 'Дата выставления счета',
                    'mass-update-success' => 'Выбранные счета успешно обновлены.',
                    'order-id'            => 'ID заказа',
                    'overdue'             => 'Просрочено',
                    'overdue-by'          => 'Просрочено на :count дн(я/ей)',
                    'paid'                => 'Оплачено',
                    'pending'             => 'В ожидании',
                    'status'              => 'Статус',
                    'update-status'       => 'Обновить статус',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount За единицу x :qty Количество',
                'channel'                        => 'Канал',
                'customer-email'                 => 'Email - :email',
                'customer'                       => 'Клиент',
                'discount'                       => 'Скидка - :discount',
                'email'                          => 'Email',
                'grand-total'                    => 'Общая сумма',
                'invoice-items'                  => 'Элементы счета',
                'invoice-sent'                   => 'Счет успешно отправлен',
                'invoice-status'                 => 'Статус счета',
                'order-date'                     => 'Дата заказа',
                'order-id'                       => 'ID заказа',
                'order-information'              => 'Информация о заказе',
                'order-status'                   => 'Статус заказа',
                'price-excl-tax'                 => 'Цена (без налога) - :price',
                'price-incl-tax'                 => 'Цена (с налогом) - :price',
                'price'                          => 'Цена - :price',
                'print'                          => 'Печать',
                'product-image'                  => 'Изображение товара',
                'qty'                            => 'Количество - :qty',
                'send-btn'                       => 'Отправить',
                'send-duplicate-invoice'         => 'Отправить дубликат счета',
                'send'                           => 'Отправить',
                'shipping-and-handling-excl-tax' => 'Стоимость доставки и обработки (без налога)',
                'shipping-and-handling-incl-tax' => 'Стоимость доставки и обработки (с налогом)',
                'shipping-and-handling'          => 'Стоимость доставки и обработки',
                'sku'                            => 'Артикул - :sku',
                'sub-total-excl-tax'             => 'Подытог (без налога) - :sub_total',
                'sub-total-incl-tax'             => 'Подытог (с налогом) - :sub_total',
                'sub-total-summary-excl-tax'     => 'Подытог (без налога)',
                'sub-total-summary-incl-tax'     => 'Подытог (с налогом)',
                'sub-total-summary'              => 'Подытог',
                'sub-total'                      => 'Подытог - :sub_total',
                'summary-discount'               => 'Скидка',
                'summary-tax'                    => 'Сумма налога',
                'tax'                            => 'Сумма налога - :tax',
                'title'                          => 'Счет #:invoice_id',
            ],

            'create' => [
                'amount-per-unit'    => ':amount За единицу x :qty Количество',
                'create-invoice'     => 'Создать Счет',
                'create-success'     => 'Счет успешно создан',
                'create-transaction' => 'Создать Транзакцию',
                'creation-error'     => 'Создание счета заказа не разрешено.',
                'invalid-qty'        => 'Мы обнаружили недопустимое количество для выставления счетов за товары.',
                'invoice'            => 'Счет',
                'new-invoice'        => 'Новый Счет',
                'product-error'      => 'Невозможно создать счет без товаров.',
                'product-image'      => 'Изображение Товара',
                'qty-to-invoiced'    => 'Количество для выставления счетов',
                'sku'                => 'Артикул - :sku',
            ],

            'invoice-pdf' => [
                'bank-details'               => 'Банковские реквизиты',
                'bill-to'                    => 'Выставить счет',
                'contact-number'             => 'Контактный номер',
                'contact'                    => 'Контакт',
                'date'                       => 'Дата счета',
                'discount'                   => 'Скидка',
                'excl-tax'                   => 'Без налога:',
                'grand-total'                => 'Общая сумма',
                'invoice-id'                 => 'ID счета',
                'invoice'                    => 'Счет',
                'order-date'                 => 'Дата заказа',
                'order-id'                   => 'ID заказа',
                'payment-method'             => 'Способ оплаты',
                'payment-terms'              => 'Условия оплаты',
                'price'                      => 'Цена',
                'product-name'               => 'Название товара',
                'qty'                        => 'Количество',
                'ship-to'                    => 'Адрес доставки',
                'shipping-handling-excl-tax' => 'Стоимость доставки и обработки (без налога)',
                'shipping-handling-incl-tax' => 'Стоимость доставки и обработки (с налогом)',
                'shipping-handling'          => 'Стоимость доставки и обработки',
                'shipping-method'            => 'Способ доставки',
                'sku'                        => 'Артикул',
                'subtotal-excl-tax'          => 'Подытог (без налога)',
                'subtotal-incl-tax'          => 'Подытог (с налогом)',
                'subtotal'                   => 'Подытог',
                'tax-amount'                 => 'Сумма налога',
                'tax'                        => 'Налог',
                'vat-number'                 => 'Номер НДС',
            ],
        ],

        'invoice-transaction' => [
            'id'               => 'ID',
            'transaction-date' => 'Дата транзакции',
            'transaction-id'   => 'ID транзакции',
            'view'             => 'Просмотр',
        ],

        'transactions' => [
            'index' => [
                'create-btn' => 'Создать транзакции',
                'title'      => 'Транзакции',

                'datagrid' => [
                    'completed'          => 'Завершено',
                    'id'                 => 'ID',
                    'invoice-id'         => 'ID счета',
                    'order-id'           => 'ID заказа',
                    'paid'               => 'Оплачено',
                    'pending'            => 'В ожидании',
                    'status'             => 'Статус',
                    'transaction-amount' => 'Сумма',
                    'transaction-date'   => 'Дата',
                    'transaction-id'     => 'ID транзакции',
                    'view'               => 'Просмотр',
                ],

                'create' => [
                    'already-paid'               => 'Уже оплачено',
                    'amount'                     => 'Сумма',
                    'create-transaction'         => 'Создать транзакцию',
                    'invoice-id'                 => 'Идентификатор счета',
                    'invoice-missing'            => 'Отсутствует счет',
                    'payment-method'             => 'Метод оплаты',
                    'save-transaction'           => 'Сохранить транзакцию',
                    'transaction-amount-exceeds' => 'Сумма транзакции превышает',
                    'transaction-amount-zero'    => 'Сумма транзакции равна нулю',
                    'transaction-saved'          => 'Транзакция успешно сохранена.',
                ],

                'view' => [
                    'amount'           => 'Сумма',
                    'created-at'       => 'Создано',
                    'invoice-id'       => 'Идентификатор счета',
                    'order-id'         => 'Идентификатор заказа',
                    'payment-details'  => 'Детали оплаты',
                    'payment-method'   => 'Метод оплаты',
                    'status'           => 'Статус',
                    'title'            => 'Детали транзакции',
                    'transaction-id'   => 'Идентификатор транзакции',
                ],
            ],
        ],

        'booking' => [
            'index' => [
                'datagrid' => [
                    'created-date' => 'Дата создания',
                    'from'         => 'С',
                    'id'           => 'ID',
                    'order-id'     => 'ID заказа',
                    'qty'          => 'Количество',
                    'to'           => 'До',
                    'view'         => 'Просмотр',
                ],

                'title'    => 'Бронирования',
            ],

            'calendar' => [
                'booking-date'     => 'Дата бронирования',
                'booking-details'  => 'Детали бронирования',
                'canceled'         => 'Отменено',
                'closed'           => 'Закрыто',
                'done'             => 'Выполнено',
                'order-id'         => 'ID заказа',
                'pending'          => 'В ожидании',
                'price'            => 'Цена',
                'status'           => 'Статус',
                'time-slot'        => 'Временной интервал:',
                'view-details'     => 'Просмотреть детали',
            ],

            'title' => 'Бронирование продукта',
        ],
    ],

    'catalog' => [
        'products' => [
            'index' => [
                'already-taken' => ':name уже занят.',
                'create-btn'    => 'Создать продукт',
                'title'         => 'Продукты',

                'create' => [
                    'back-btn'                => 'Назад',
                    'configurable-attributes' => 'Атрибуты конфигурируемого продукта',
                    'create-btn'              => 'Создать продукт',
                    'family'                  => 'Семейство',
                    'save-btn'                => 'Сохранить продукт',
                    'sku'                     => 'Артикул (SKU)',
                    'title'                   => 'Создать новый продукт',
                    'type'                    => 'Тип продукта',
                ],

                'datagrid' => [
                    'active'                 => 'Активный',
                    'attribute-family'       => 'Семейство атрибутов',
                    'attribute-family-value' => 'Семейство атрибутов - :attribute_family',
                    'category'               => 'Категория',
                    'channel'                => 'Канал',
                    'copy-of'                => 'Копия :value',
                    'copy-of-slug'           => 'копия-:value',
                    'delete'                 => 'Удалить',
                    'disable'                => 'Отключить',
                    'id'                     => 'ID',
                    'id-value'               => 'ID - :id',
                    'image'                  => 'Изображение',
                    'mass-delete-success'    => 'Выбранные продукты успешно удалены',
                    'mass-update-success'    => 'Выбранные продукты успешно обновлены',
                    'name'                   => 'Наименование',
                    'out-of-stock'           => 'Нет в наличии',
                    'price'                  => 'Цена',
                    'product-image'          => 'Изображение продукта',
                    'qty'                    => 'Количество',
                    'qty-value'              => ':qty в наличии',
                    'sku'                    => 'Артикул (SKU)',
                    'sku-value'              => 'Артикул (SKU) - :sku',
                    'status'                 => 'Статус',
                    'type'                   => 'Тип',
                    'update-status'          => 'Обновить статус',
                ],
            ],

            'edit' => [
                'preview'  => 'Просмотр',
                'remove'   => 'Удалить',
                'save-btn' => 'Сохранить продукт',
                'title'    => 'Редактировать продукт',

                'channels' => [
                    'title' => 'Каналы',
                ],

                'price' => [
                    'group' => [
                        'add-group-price'           => 'Добавить цену для группы',
                        'all-groups'                => 'Все группы',
                        'create-btn'                => 'Добавить новое',
                        'discount-group-price-info' => 'Для :qty шт. со скидкой :price',
                        'edit-btn'                  => 'Редактировать',
                        'empty-info'                => 'Специальная цена для клиентов, принадлежащих к определенной группе.',
                        'fixed-group-price-info'    => 'Для :qty шт. по фиксированной цене :price',
                        'title'                     => 'Цена для группы покупателей',

                        'create' => [
                            'all-groups'     => 'Все группы',
                            'create-title'   => 'Создать цену для группы покупателей',
                            'customer-group' => 'Группа покупателей',
                            'delete-btn'     => 'Удалить',
                            'discount'       => 'Скидка',
                            'fixed'          => 'Фиксированная',
                            'price'          => 'Цена',
                            'price-type'     => 'Тип цены',
                            'qty'            => 'Минимальное количество',
                            'save-btn'       => 'Сохранить',
                            'update-title'   => 'Обновить цену для группы покупателей',
                        ],
                    ],
                ],

                'inventories' => [
                    'pending-ordered-qty'      => 'Ожидаемое количество в заказе: :qty',
                    'pending-ordered-qty-info' => 'Ожидаемое количество в заказе будет вычтено из соответствующего источника инвентаризации после отправки. В случае отмены, ожидаемое количество будет доступно для продажи.',
                    'title'                    => 'Инвентаризация',
                ],

                'categories' => [
                    'title' => 'Категории',
                ],

                'images' => [
                    'info'  => 'Разрешение изображения должно быть примерно 560px X 609px',
                    'title' => 'Изображения',
                ],

                'videos' => [
                    'error' => 'Размер :attribute не должен превышать :max килобайт. Пожалуйста, выберите файл меньшего размера.',
                    'title' => 'Видео',
                    'info'  => 'Максимальный размер видео должен быть :size',
                ],

                'links' => [
                    'related-products' => [
                        'empty-info' => 'Добавьте связанные товары на ходу.',
                        'info'       => 'Помимо продукта, который просматривает клиент, им предлагаются связанные продукты.',
                        'title'      => 'Связанные продукты',
                    ],

                    'up-sells' => [
                        'empty-info' => 'Добавьте дополнительные продукты для продажи в пути.',
                        'info'       => 'Клиенту предлагают продукты с допродажей, которые служат альтернативой продукту, который они просматривают.',
                        'title'      => 'Продукты с допродажей',
                    ],

                    'cross-sells' => [
                        'empty-info' => 'Добавьте кросс-продажи на ходу.',
                        'info'       => 'В непосредственной близости от корзины для покупок вы найдете продукты, позиционированные как продажи в комплекте, чтобы дополнить уже добавленные товары в корзину.',
                        'title'      => 'Продукты перекрестной продажи',
                    ],

                    'add-btn'           => 'Добавить продукт',
                    'delete'            => 'Удалить',
                    'empty-info'        => 'Чтобы добавить продукты :type, выполните действие.',
                    'empty-title'       => 'Добавить продукт',
                    'image-placeholder' => 'Изображение продукта',
                    'sku'               => 'Артикул (SKU) - :sku',
                ],

                'types' => [
                    'simple' => [
                        'customizable-options' => [
                            'add-btn'           => 'Добавить опцию',
                            'empty-info'        => 'Для создания настраиваемых опций на лету.',
                            'empty-title'       => 'Добавить опцию',
                            'info'              => 'Это настроит простой продукт.',
                            'title'             => 'Настраиваемый элемент',

                            'update-create' => [
                                'is-required'               => 'Обязательно',
                                'max-characters'            => 'Максимальное количество символов',
                                'name'                      => 'Название',
                                'no'                        => 'Нет',
                                'price'                     => 'Цена',
                                'save-btn'                  => 'Сохранить',
                                'supported-file-extensions' => 'Поддерживаемые расширения файлов',
                                'title'                     => 'Опция',
                                'type'                      => 'Тип',
                                'yes'                       => 'Да',
                            ],

                            'option' => [
                                'add-btn'     => 'Добавить опцию',
                                'delete'      => 'Удалить',
                                'delete-btn'  => 'Удалить',
                                'edit-btn'    => 'Редактировать',
                                'empty-info'  => 'Для создания различных комбинаций продуктов на лету.',
                                'empty-title' => 'Добавить опцию',

                                'types' => [
                                    'text' => [
                                        'title' => 'Текст',
                                    ],

                                    'textarea' => [
                                        'title' => 'Текстовое поле',
                                    ],

                                    'checkbox' => [
                                        'title' => 'Флажок',
                                    ],

                                    'radio' => [
                                        'title' => 'Радио',
                                    ],

                                    'select' => [
                                        'title' => 'Выбрать',
                                    ],

                                    'multiselect' => [
                                        'title' => 'Множественный выбор',
                                    ],

                                    'date' => [
                                        'title' => 'Дата',
                                    ],

                                    'datetime' => [
                                        'title' => 'Дата и время',
                                    ],

                                    'time' => [
                                        'title' => 'Время',
                                    ],

                                    'file' => [
                                        'title' => 'Файл',
                                    ],
                                ],

                                'items' => [
                                    'update-create' => [
                                        'label'    => 'Метка',
                                        'price'    => 'Цена',
                                        'save-btn' => 'Сохранить',
                                        'title'    => 'Опция',
                                    ],
                                ],
                            ],

                            'validations' => [
                                'associated-product' => 'Продукт уже связан с настраиваемым, групповым или пакетным продуктом.',
                            ],
                        ],
                    ],

                    'configurable' => [
                        'add-btn'           => 'Добавить вариант',
                        'delete-btn'        => 'Удалить',
                        'edit-btn'          => 'Редактировать',
                        'empty-info'        => 'Создание различных комбинаций продуктов.',
                        'empty-title'       => 'Добавить вариант',
                        'image-placeholder' => 'Изображение продукта',
                        'info'              => 'Продукты с вариациями зависят от всех возможных комбинаций атрибутов.',
                        'qty'               => ':qty шт.',
                        'sku'               => 'Артикул (SKU) - :sku',
                        'title'             => 'Варианты',

                        'create' => [
                            'description'            => 'Описание',
                            'name'                   => 'Название',
                            'save-btn'               => 'Добавить',
                            'title'                  => 'Добавить вариант',
                            'variant-already-exists' => 'Этот вариант уже существует',
                        ],

                        'edit' => [
                            'disabled'        => 'Отключено',
                            'edit-info'       => 'Если вы хотите подробно обновить информацию о продукте, перейдите на',
                            'edit-link-title' => 'Страницу информации о продукте',
                            'enabled'         => 'Включено',
                            'images'          => 'Изображения',
                            'name'            => 'Название',
                            'price'           => 'Цена',
                            'quantities'      => 'Количество',
                            'save-btn'        => 'Сохранить',
                            'sku'             => 'Артикул (SKU)',
                            'status'          => 'Статус',
                            'title'           => 'Продукт',
                            'weight'          => 'Вес',
                        ],

                        'mass-edit' => [
                            'add-images'          => 'Добавить изображения',
                            'apply-to-all-btn'    => 'Применить ко всем',
                            'apply-to-all-name'   => 'Применить имя ко всем вариантам.',
                            'apply-to-all-sku'    => 'Применить цену ко всем SKU.',
                            'apply-to-all-status' => 'Применить статус ко всем вариантам.',
                            'apply-to-all-weight' => 'Применить вес ко всем вариантам.',
                            'edit-inventories'    => 'Редактировать инвентарь',
                            'edit-names'          => 'Редактировать названия',
                            'edit-prices'         => 'Редактировать цены',
                            'edit-sku'            => 'Редактировать SKU',
                            'edit-status'         => 'Редактировать статус',
                            'edit-weight'         => 'Редактировать вес',
                            'name'                => 'Имя',
                            'price'               => 'Цена',
                            'remove-images'       => 'Удалить изображения',
                            'remove-variants'     => 'Удалить варианты',
                            'select-action'       => 'Выбрать действие',
                            'select-variants'     => 'Выбрать варианты',
                            'status'              => 'Статус',
                            'variant-name'        => 'Название варианта',
                            'variant-sku'         => 'SKU варианта',
                            'weight'              => 'Вес',
                        ],
                    ],

                    'grouped' => [
                        'add-btn'           => 'Добавить продукт',
                        'default-qty'       => 'Количество по умолчанию',
                        'delete'            => 'Удалить',
                        'empty-info'        => 'Создание различных комбинаций продуктов.',
                        'empty-title'       => 'Добавить продукт',
                        'image-placeholder' => 'Изображение продукта',
                        'info'              => 'Групповой продукт состоит из автономных товаров, представленных в виде набора, позволяя варьировать или координировать их по сезонам или темам. Каждый продукт можно купить как индивидуально, так и в составе группы.',
                        'sku'               => 'Артикул (SKU) - :sku',
                        'title'             => 'Групповые продукты',
                    ],

                    'bundle' => [
                        'add-btn'           => 'Добавить опцию',
                        'empty-info'        => 'Создание опций для комплекта.',
                        'empty-title'       => 'Добавить опцию',
                        'image-placeholder' => 'Изображение продукта',
                        'info'              => 'Комплектный продукт - это набор нескольких товаров или услуг, продаваемых вместе по специальной цене, обеспечивая ценность и удобство для клиентов.',
                        'title'             => 'Опции комплекта',

                        'update-create' => [
                            'checkbox'    => 'Флажок',
                            'is-required' => 'Обязательно',
                            'multiselect' => 'Множественный выбор',
                            'name'        => 'Заголовок',
                            'no'          => 'Нет',
                            'radio'       => 'Переключатель',
                            'save-btn'    => 'Сохранить',
                            'select'      => 'Выбор',
                            'title'       => 'Опция',
                            'type'        => 'Тип',
                            'yes'         => 'Да',
                        ],

                        'option' => [
                            'add-btn'     => 'Добавить продукт',
                            'default-qty' => 'Количество по умолчанию',
                            'delete'      => 'Удалить',
                            'delete-btn'  => 'Удалить',
                            'edit-btn'    => 'Редактировать',
                            'empty-info'  => 'Создание различных комбинаций продуктов.',
                            'empty-title' => 'Добавить продукт',
                            'sku'         => 'Артикул (SKU) - :sku',

                            'types' => [
                                'checkbox' => [
                                    'info'  => 'Задайте продукт по умолчанию с помощью флажка',
                                    'title' => 'Флажок',
                                ],

                                'multiselect' => [
                                    'info'  => 'Задайте продукт по умолчанию с помощью кнопки \"множественный выбор\"',
                                    'title' => 'Множественный выбор',
                                ],

                                'radio' => [
                                    'info'  => 'Задайте продукт по умолчанию с помощью переключателя',
                                    'title' => 'Переключатель',
                                ],

                                'select' => [
                                    'info'  => 'Задайте продукт по умолчанию с помощью кнопки \"выбор\"',
                                    'title' => 'Выбор',
                                ],
                            ],
                        ],
                    ],

                    'booking' => [
                        'available-from' => 'Доступно с',
                        'available-to'   => 'Доступно до',
                        'location'       => 'Местоположение',
                        'qty'            => 'Количество',
                        'title'          => 'Тип бронирования',

                        'available-every-week' => [
                            'no'    => 'Нет',
                            'title' => 'Доступно каждую неделю',
                            'yes'   => 'Да',
                        ],

                        'appointment' => [
                            'break-duration' => 'Перерыв между слотами (мин)',
                            'slot-duration'  => 'Продолжительность слота (мин)',

                            'same-slot-for-all-days' => [
                                'no'    => 'Нет',
                                'title' => 'Один и тот же слот для всех дней',
                                'yes'   => 'Да',
                            ],
                        ],

                        'default' => [
                            'add'              => 'Добавить',
                            'break-duration'   => 'Перерыв между слотами (мин)',
                            'close'            => 'Закрыть',
                            'description'      => 'Информация о бронировании',
                            'description-info' => 'Длительность будет создана и отображена в соответствии со слотами. Она будет уникальной для всех слотов и отображаться на витрине магазина',
                            'edit'             => 'Редактировать',
                            'many'             => 'Много бронирований на один день',
                            'one'              => 'Одно бронирование на много дней',
                            'open'             => 'Открыть',
                            'slot-add'         => 'Добавить слоты',
                            'slot-duration'    => 'Продолжительность слота (мин)',
                            'slot-title'       => 'Продолжительность времени слотов',
                            'title'            => 'По умолчанию',
                            'unavailable'      => 'Недоступно',

                            'modal'            => [
                                'slot' => [
                                    'add-title'  => 'Добавить слоты',
                                    'close'      => 'Закрыть',
                                    'day'        => 'День',
                                    'edit-title' => 'Редактировать слоты',
                                    'friday'     => 'Пятница',
                                    'from'       => 'С',
                                    'from-day'   => 'С дня',
                                    'from-time'  => 'С времени',
                                    'monday'     => 'Понедельник',
                                    'open'       => 'Открыть',
                                    'saturday'   => 'Суббота',
                                    'save'       => 'Сохранить',
                                    'select'     => 'Выбрать',
                                    'status'     => 'Статус',
                                    'sunday'     => 'Воскресенье',
                                    'thursday'   => 'Четверг',
                                    'to'         => 'До',
                                    'to-day'     => 'До дня',
                                    'to-time'    => 'До времени',
                                    'tuesday'    => 'Вторник',
                                    'wednesday'  => 'Среда',
                                    'week'       => ':day',
                                ],
                            ],
                        ],

                        'event' => [
                            'add'                => 'Добавить билеты',
                            'delete'             => 'Удалить',
                            'description'        => 'Описание',
                            'description-info'   => 'Билеты недоступны.',
                            'edit'               => 'Редактировать',
                            'name'               => 'Имя',
                            'price'              => 'Цена',
                            'qty'                => 'Количество',
                            'special-price'      => 'Специальная цена',
                            'special-price-from' => 'Специальная цена с',
                            'special-price-to'   => 'Специальная цена до',
                            'title'              => 'Билеты',
                            'valid-from'         => 'Действительно с',
                            'valid-until'        => 'Действительно до',

                            'modal'              => [
                                'edit' => 'Редактировать билеты',
                                'save' => 'Сохранить',
                            ],
                        ],

                        'empty-info' => [
                            'tickets' => [
                                'add' => 'Добавить билеты',
                            ],

                            'slots'   => [
                                'add'         => 'Добавить слоты',
                                'description' => 'Доступные слоты с продолжительностью времени.',
                            ],
                        ],

                        'rental' => [
                            'daily'                  => 'Ежедневно',
                            'daily-hourly'           => 'Ежедневно и почасово',
                            'daily-price'            => 'Ежедневная цена',
                            'hourly'                 => 'Почасово',
                            'hourly-price'           => 'Почасовая цена',
                            'title'                  => 'Тип аренды',

                            'same-slot-for-all-days' => [
                                'no'    => 'Нет',
                                'title' => 'Один и тот же слот для всех дней',
                                'yes'   => 'Да',
                            ],
                        ],

                        'slots' => [
                            'add'              => 'Добавить слоты',
                            'description-info' => 'Длительность будет создана и отображена в соответствии со слотами. Она будет уникальной для всех слотов и отображаться на витрине магазина',
                            'save'             => 'Сохранить',
                            'title'            => 'Продолжительность времени слотов',
                            'unavailable'      => 'Недоступно',

                            'action'           => [
                                'add' => 'Добавить',
                            ],

                            'modal'            => [
                                'slot' => [
                                    'friday'     => 'Пятница',
                                    'from'       => 'С',
                                    'monday'     => 'Понедельник',
                                    'saturday'   => 'Суббота',
                                    'sunday'     => 'Воскресенье',
                                    'thursday'   => 'Четверг',
                                    'to'         => 'До',
                                    'tuesday'    => 'Вторник',
                                    'wednesday'  => 'Среда',
                                ],
                            ],
                        ],

                        'table' => [
                            'break-duration'            => 'Перерыв между слотами (мин)',
                            'guest-capacity'            => 'Вместимость гостей',
                            'guest-limit'               => 'Лимит гостей за столом',
                            'prevent-scheduling-before' => 'Запретить планирование до',
                            'slot-duration'             => 'Продолжительность слота (мин)',

                            'charged-per' => [
                                'guest'  => 'Гость',
                                'table'  => 'Стол',
                                'title'  => 'Оплата за',
                            ],

                            'same-slot-for-all-days' => [
                                'no'    => 'Нет',
                                'title' => 'Один и тот же слот для всех дней',
                                'yes'   => 'Да',
                            ],
                        ],

                        'type' => [
                            'appointment' => 'Бронирование встречи',
                            'default'     => 'Бронирование по умолчанию',
                            'event'       => 'Бронирование мероприятия',
                            'many'        => 'Много',
                            'one'         => 'Один',
                            'rental'      => 'Аренда',
                            'table'       => 'Бронирование стола',
                            'title'       => 'Тип',
                        ],
                    ],

                    'downloadable' => [
                        'links' => [
                            'add-btn'     => 'Добавить ссылку',
                            'delete-btn'  => 'Удалить',
                            'edit-btn'    => 'Редактировать',
                            'empty-info'  => 'Создание ссылки.',
                            'empty-title' => 'Добавить ссылку',
                            'file'        => 'Файл : ',
                            'info'        => 'Продукт для загрузки позволяет продавать цифровые продукты, такие как электронные книги, программные приложения, музыку, игры и другие.',
                            'sample-file' => 'Пример файла : ',
                            'sample-url'  => 'Пример URL : ',
                            'title'       => 'Ссылки для загрузки',
                            'url'         => 'URL : ',

                            'update-create' => [
                                'downloads'   => 'Загрузка разрешена',
                                'file'        => 'Файл',
                                'file-type'   => 'Тип файла',
                                'name'        => 'Заголовок',
                                'price'       => 'Цена',
                                'sample'      => 'Пример',
                                'sample-type' => 'Тип примера',
                                'save-btn'    => 'Сохранить',
                                'title'       => 'Ссылка',
                                'url'         => 'URL',
                            ],
                        ],

                        'samples' => [
                            'add-btn'     => 'Добавить пример',
                            'delete-btn'  => 'Удалить',
                            'edit-btn'    => 'Редактировать',
                            'empty-info'  => 'Создание образца.',
                            'empty-title' => 'Добавить пример',
                            'file'        => 'Файл : ',
                            'info'        => 'Продукт для загрузки позволяет продавать цифровые продукты, такие как электронные книги, программные приложения, музыку, игры и другие.',
                            'title'       => 'Образцы для загрузки',
                            'url'         => 'URL : ',

                            'update-create' => [
                                'file'        => 'Файл',
                                'file-type'   => 'Тип файла',
                                'name'        => 'Заголовок',
                                'save-btn'    => 'Сохранить',
                                'title'       => 'Ссылка',
                                'url'         => 'URL',
                            ],
                        ],
                    ],
                ],
            ],

            'create-success'          => 'Продукт успешно создан',
            'delete-success'          => 'Продукт успешно удален',
            'delete-failed'           => 'Не удалось удалить продукт',
            'product-copied'          => 'Продукт успешно скопирован',
            'saved-inventory-message' => 'Продукт успешно сохранен',
            'update-success'          => 'Продукт успешно обновлен',
        ],

        'attributes' => [
            'index' => [
                'create-btn' => 'Создать атрибут',
                'title'      => 'Атрибуты',

                'datagrid' => [
                    'boolean'             => 'Boolean',
                    'channel-based'       => 'На основе канала',
                    'checkbox'            => 'Флажок',
                    'code'                => 'Код',
                    'created-at'          => 'Создано',
                    'date'                => 'Дата',
                    'date-time'           => 'Дата Время',
                    'delete'              => 'Удалить',
                    'edit'                => 'Редактировать',
                    'false'               => 'Ложно',
                    'file'                => 'Файл',
                    'id'                  => 'ID',
                    'image'               => 'Изображение',
                    'locale-based'        => 'На основе локали',
                    'mass-delete-success' => 'Выбранный атрибут успешно удален',
                    'multiselect'         => 'Множественный выбор',
                    'name'                => 'Название',
                    'price'               => 'Цена',
                    'required'            => 'Обязательно',
                    'select'              => 'Выбрать',
                    'text'                => 'Текст',
                    'textarea'            => 'Текстовая область',
                    'true'                => 'Истинно',
                    'type'                => 'Тип',
                    'unique'              => 'Уникальный',
                ],
            ],

            'create' => [
                'add-attribute-options' => 'Добавить опции атрибута',
                'add-option'            => 'Добавить опцию',
                'add-options-info'      => 'Создать различные комбинации опций атрибута',
                'add-row'               => 'Добавить строку',
                'admin'                 => 'Админ',
                'admin-name'            => 'Название администратора',
                'back-btn'              => 'Назад',
                'boolean'               => 'Логическое значение',
                'checkbox'              => 'Флажок',
                'code'                  => 'Код атрибута',
                'color'                 => 'Цвет',
                'configuration'         => 'Конфигурация',
                'create-empty-option'   => 'Создать пустую опцию по умолчанию',
                'date'                  => 'Дата',
                'datetime'              => 'Дата и время',
                'decimal'               => 'Десятичное число',
                'default-value'         => 'Значение по умолчанию',
                'email'                 => 'Email',
                'enable-wysiwyg'        => 'Включить редактор WYSIWYG',
                'file'                  => 'Файл',
                'general'               => 'Общее',
                'image'                 => 'Изображение',
                'input-options'         => 'Опции ввода',
                'input-validation'      => 'Проверка ввода',
                'is-comparable'         => 'Атрибут сравним',
                'is-configurable'       => 'Использовать для создания конфигурируемого продукта',
                'is-filterable'         => 'Использовать в многоуровневой навигации',
                'is-required'           => 'Обязательное',
                'is-unique'             => 'Уникальный',
                'is-visible-on-front'   => 'Видим на странице товара на сайте',
                'label'                 => 'Метка',
                'multiselect'           => 'Множественный выбор',
                'no'                    => 'Нет',
                'numeric'               => 'Число',
                'option-deleted'        => 'Опция успешно удалена',
                'options'               => 'Параметры',
                'position'              => 'Позиция',
                'price'                 => 'Цена',
                'regex'                 => 'Регулярное выражение',
                'regex-info'            => 'Выражение должно быть в двойных кавычках.',
                'save-btn'              => 'Сохранить атрибут',
                'select'                => 'Выпадающий список',
                'select-type'           => 'Тип атрибута "Выбор"',
                'swatch'                => 'Swatch',
                'text'                  => 'Текст',
                'textarea'              => 'Текстовая область',
                'title'                 => 'Добавить атрибут',
                'type'                  => 'Тип атрибута',
                'url'                   => 'URL',
                'use-in-flat'           => 'Создать в таблице товаров',
                'validations'           => 'Проверки',
                'value-per-channel'     => 'Значение для канала',
                'value-per-locale'      => 'Значение для локали',
                'yes'                   => 'Да',

                'option'                => [
                    'color'    => 'Палитра цветов',
                    'dropdown' => 'Выпадающий список',
                    'image'    => 'Изображение',
                    'save-btn' => 'Сохранить опцию',
                    'text'     => 'Текстовая палитра',
                ],
            ],

            'edit' => [
                'add-attribute-options' => 'Добавить опции атрибута',
                'add-option'            => 'Добавить опцию',
                'add-options-info'      => 'Создать различные комбинации опций атрибута',
                'add-row'               => 'Добавить строку',
                'admin'                 => 'Админ',
                'admin-name'            => 'Название администратора',
                'back-btn'              => 'Назад',
                'boolean'               => 'Логическое значение',
                'checkbox'              => 'Флажок',
                'code'                  => 'Код атрибута',
                'color'                 => 'Цвет',
                'configuration'         => 'Конфигурация',
                'create-empty-option'   => 'Создать пустую опцию по умолчанию',
                'date'                  => 'Дата',
                'datetime'              => 'Дата и время',
                'decimal'               => 'Десятичное число',
                'default-value'         => 'Значение по умолчанию',
                'email'                 => 'Email',
                'enable-wysiwyg'        => 'Включить редактор WYSIWYG',
                'file'                  => 'Файл',
                'general'               => 'Общее',
                'image'                 => 'Изображение',
                'input-options'         => 'Опции ввода',
                'input-validation'      => 'Проверка ввода',
                'is-comparable'         => 'Атрибут сравним',
                'is-configurable'       => 'Использовать для создания конфигурируемого продукта',
                'is-filterable'         => 'Использовать в многоуровневой навигации',
                'is-required'           => 'Обязательное',
                'is-unique'             => 'Уникальный',
                'is-visible-on-front'   => 'Видим на странице товара на сайте',
                'label'                 => 'Метка',
                'multiselect'           => 'Множественный выбор',
                'no'                    => 'Нет',
                'numeric'               => 'Число',
                'option-deleted'        => 'Опция успешно удалена',
                'options'               => 'Параметры',
                'position'              => 'Позиция',
                'price'                 => 'Цена',
                'regex'                 => 'Регулярное выражение',
                'regex-info'            => 'Выражение должно быть в двойных кавычках.',
                'save-btn'              => 'Сохранить атрибут',
                'select'                => 'Выпадающий список',
                'select-type'           => 'Тип атрибута "Выбор"',
                'swatch'                => 'Swatch',
                'text'                  => 'Текст',
                'textarea'              => 'Текстовая область',
                'title'                 => 'Редактировать атрибут',
                'type'                  => 'Тип атрибута',
                'url'                   => 'URL',
                'use-in-flat'           => 'Создать в таблице товаров',
                'validations'           => 'Проверки',
                'value-per-channel'     => 'Значение для канала',
                'value-per-locale'      => 'Значение для локали',
                'yes'                   => 'Да',

                'option' => [
                    'color'    => 'Палитра цветов',
                    'dropdown' => 'Выпадающий список',
                    'image'    => 'Изображение',
                    'save-btn' => 'Сохранить опцию',
                    'text'     => 'Текстовая палитра',
                ],
            ],

            'create-success'    => 'Атрибут успешно создан',
            'delete-failed'     => 'Не удалось удалить атрибут',
            'delete-success'    => 'Атрибут успешно удален',
            'update-success'    => 'Атрибут успешно обновлен',
            'user-define-error' => 'Системный атрибут не может быть удален',
        ],

        'categories' => [
            'index' => [
                'add-btn' => 'Создать категорию',
                'title'   => 'Категории',

                'datagrid' => [
                    'active'         => 'Активно',
                    'delete'         => 'Удалить',
                    'delete-success' => 'Выбранные :resource были успешно удалены',
                    'edit'           => 'Редактировать',
                    'id'             => 'ID',
                    'inactive'       => 'Неактивно',
                    'name'           => 'Название',
                    'no-of-products' => 'Количество товаров',
                    'position'       => 'Позиция',
                    'status'         => 'Видимо в меню',
                    'update-status'  => 'Обновить статус',
                ],
            ],

            'create' => [
                'add-banner'               => 'Добавить баннер',
                'add-logo'                 => 'Добавить логотип',
                'back-btn'                 => 'Назад',
                'banner'                   => 'Баннер',
                'banner-size'              => 'Соотношение сторон баннера (1320px X 300px)',
                'description'              => 'Описание',
                'description-and-images'   => 'Описание и изображения',
                'description-only'         => 'Только описание',
                'display-mode'             => 'Режим отображения',
                'enter-position'           => 'Введите позицию',
                'filterable-attributes'    => 'Атрибуты для фильтрации',
                'general'                  => 'Общее',
                'logo'                     => 'Логотип',
                'logo-size'                => 'Разрешение логотипа должно быть (110px X 110px)',
                'meta-description'         => 'Мета-описание',
                'meta-keywords'            => 'Мета-ключевые слова',
                'meta-title'               => 'Мета-заголовок',
                'name'                     => 'Название',
                'parent-category'          => 'Родительская категория',
                'position'                 => 'Позиция',
                'products-and-description' => 'Товары и описание',
                'products-only'            => 'Только товары',
                'save-btn'                 => 'Сохранить категорию',
                'select-display-mode'      => 'Выберите режим отображения',
                'seo-details'              => 'SEO-данные',
                'settings'                 => 'Настройки',
                'slug'                     => 'Slug',
                'title'                    => 'Добавить новую категорию',
                'visible-in-menu'          => 'Видимо в меню',
            ],

            'edit' => [
                'add-banner'               => 'Добавить баннер',
                'add-logo'                 => 'Добавить логотип',
                'back-btn'                 => 'Назад',
                'banner'                   => 'Баннер',
                'banner-size'              => 'Соотношение сторон баннера (1320px X 300px)',
                'description'              => 'Описание',
                'description-and-images'   => 'Описание и изображения',
                'description-only'         => 'Только описание',
                'display-mode'             => 'Режим отображения',
                'enter-position'           => 'Введите позицию',
                'filterable-attributes'    => 'Атрибуты для фильтрации',
                'general'                  => 'Общее',
                'logo'                     => 'Логотип',
                'logo-size'                => 'Разрешение логотипа должно быть (110px X 110px)',
                'meta-description'         => 'Мета-описание',
                'meta-keywords'            => 'Мета-ключевые слова',
                'meta-title'               => 'Мета-заголовок',
                'name'                     => 'Название',
                'position'                 => 'Позиция*',
                'products-and-description' => 'Товары и описание',
                'products-only'            => 'Только товары',
                'save-btn'                 => 'Сохранить категорию',
                'select-display-mode'      => 'Выберите режим отображения',
                'select-parent-category'   => 'Выберите родительскую категорию*',
                'seo-details'              => 'SEO-данные',
                'settings'                 => 'Настройки',
                'slug'                     => 'Slug',
                'title'                    => 'Редактировать категорию',
                'visible-in-menu'          => 'Видимо в меню',
            ],

            'category'             => 'Категория',
            'create-success'       => 'Категория успешно создана.',
            'delete-category-root' => 'Корневая категория не может быть удалена.',
            'delete-failed'        => 'Произошла ошибка при удалении категории',
            'delete-success'       => 'Категория успешно удалена.',
            'update-success'       => 'Категория успешно обновлена.',
        ],

        'families' => [
            'index' => [
                'add'   => 'Создать семейство атрибутов',
                'title' => 'Семейства атрибутов',

                'datagrid' => [
                    'code'           => 'Код',
                    'delete'         => 'Удалить',
                    'delete-success' => 'Выбранные :resource успешно удалены',
                    'edit'           => 'Редактировать',
                    'id'             => 'ID',
                    'method-error'   => 'Ошибка! Обнаружен неверный метод, пожалуйста, проверьте конфигурацию массовых действий',
                    'name'           => 'Название',
                    'no-resource'    => 'Предоставленных ресурсов недостаточно для выполнения действия',
                    'partial-action' => 'Некоторые действия не были выполнены из-за ограничений системы на :resource',
                    'update-success' => 'Выбранные :resource успешно обновлены',
                ],
            ],

            'create' => [
                'add-group-btn'                    => 'Добавить группу',
                'add-group-title'                  => 'Добавить новую группу',
                'back-btn'                         => 'Назад',
                'code'                             => 'Код',
                'column'                           => 'Колонка',
                'delete-group-btn'                 => 'Удалить группу',
                'edit-group-info'                  => 'Дважды щелкните, чтобы изменить название группы',
                'enter-code'                       => 'Введите код',
                'enter-name'                       => 'Введите название',
                'general'                          => 'Общее',
                'group-code-already-exists'        => 'Код группы атрибутов уже существует.',
                'group-contains-system-attributes' => 'Эта группа содержит системные атрибуты. Сначала переместите системные атрибуты в другую группу и попробуйте снова.',
                'group-name-already-exists'        => 'Название группы атрибутов уже существует.',
                'groups'                           => 'Группы',
                'groups-info'                      => 'Управление группами семейства атрибутов',
                'main-column'                      => 'Главная колонка',
                'name'                             => 'Название',
                'removal-not-possible'             => 'Вы не можете удалить системные атрибуты из семейства атрибутов.',
                'right-column'                     => 'Правая колонка',
                'save-btn'                         => 'Сохранить семейство атрибутов',
                'select-group'                     => 'Пожалуйста, выберите группу атрибутов.',
                'title'                            => 'Создать семейство атрибутов',
                'unassigned-attributes'            => 'Не назначенные атрибуты',
                'unassigned-attributes-info'       => 'Перетащите эти атрибуты, чтобы добавить их в колонки или группы.',
            ],

            'edit' => [
                'add-group-btn'                    => 'Добавить группу',
                'add-group-title'                  => 'Добавить новую группу',
                'back-btn'                         => 'Назад',
                'code'                             => 'Код',
                'column'                           => 'Колонка',
                'delete-group-btn'                 => 'Удалить группу',
                'edit-group-info'                  => 'Дважды щелкните, чтобы изменить название группы',
                'enter-code'                       => 'Введите код',
                'enter-name'                       => 'Введите название',
                'general'                          => 'Общее',
                'group-code-already-exists'        => 'Код группы атрибутов уже существует.',
                'group-contains-system-attributes' => 'Эта группа содержит системные атрибуты. Сначала переместите системные атрибуты в другую группу и попробуйте снова.',
                'group-name-already-exists'        => 'Название группы атрибутов уже существует.',
                'groups'                           => 'Группы',
                'groups-info'                      => 'Управление группами семейства атрибутов',
                'main-column'                      => 'Главная колонка',
                'name'                             => 'Название',
                'removal-not-possible'             => 'Вы не можете удалить системные атрибуты из семейства атрибутов.',
                'right-column'                     => 'Правая колонка',
                'save-btn'                         => 'Сохранить семейство атрибутов',
                'select-group'                     => 'Пожалуйста, выберите группу атрибутов.',
                'title'                            => 'Редактировать семейство атрибутов',
                'unassigned-attributes'            => 'Не назначенные атрибуты',
                'unassigned-attributes-info'       => 'Перетащите эти атрибуты, чтобы добавить их в колонки или группы.',
            ],

            'attribute-family'        => 'Семейство атрибутов',
            'attribute-product-error' => 'Семейство атрибутов используется в продуктах.',
            'create-success'          => 'Семейство атрибутов успешно создано.',
            'delete-failed'           => 'Ошибка при удалении семейства атрибутов.',
            'delete-success'          => 'Семейство атрибутов успешно удалено.',
            'family'                  => 'Семейство атрибутов',
            'last-delete-error'       => 'Необходимо хотя бы одно семейство атрибутов.',
            'update-success'          => 'Семейство атрибутов успешно обновлено.',
            'user-define-error'       => 'Нельзя удалить системное семейство атрибутов',
        ],
    ],

    'customers' => [
        'customers' => [
            'index' => [
                'title'         => 'Клиенты',
                'login-message' => 'Вы вошли как :customer_name',

                'datagrid' => [
                    'active'         => 'Активный',
                    'address'        => ':address  Адрес(ов)',
                    'address-count'  => 'Количество адресов',
                    'channel'        => 'Канал',
                    'delete'         => 'Удалить',
                    'delete-success' => 'Выбранные данные успешно удалены',
                    'email'          => 'Электронная почта',
                    'gender'         => 'Пол',
                    'group'          => 'Группа',
                    'id'             => 'Идентификатор клиента',
                    'id-value'       => 'ID - :id',
                    'inactive'       => 'Неактивный',
                    'method-error'   => 'Ошибка! Обнаружен неверный метод, пожалуйста, проверьте конфигурацию массовых действий',
                    'name'           => 'Имя клиента',
                    'no-resource'    => 'Ресурс, предоставленный для действия, недостаточен',
                    'order'          => ':order Заказ(ов)',
                    'order-count'    => 'Количество заказов',
                    'order-pending'  => 'У клиента есть ожидающий заказ',
                    'partial-action' => 'Некоторые действия не были выполнены из-за ограничений системы на :resource',
                    'phone'          => 'Контактный номер',
                    'revenue'        => 'Доход',
                    'status'         => 'Статус',
                    'suspended'      => 'Приостановлен',
                    'update-status'  => 'Обновить статус',
                    'update-success' => 'Выбранные клиенты успешно обновлены',
                ],

                'create' => [
                    'contact-number'        => 'Контактный номер',
                    'create-btn'            => 'Создать клиента',
                    'create-success'        => 'Клиент успешно создан',
                    'customer-group'        => 'Группа клиентов',
                    'date-of-birth'         => 'Дата рождения',
                    'email'                 => 'Электронная почта',
                    'female'                => 'Женский',
                    'first-name'            => 'Имя',
                    'gender'                => 'Пол',
                    'last-name'             => 'Фамилия',
                    'male'                  => 'Мужской',
                    'other'                 => 'Другой',
                    'save-btn'              => 'Сохранить клиента',
                    'select-customer-group' => 'Выберите группу клиентов',
                    'select-gender'         => 'Выберите пол',
                    'title'                 => 'Создать нового клиента',
                ],
            ],

            'view' => [
                'account-delete-confirmation' => 'Вы уверены, что хотите удалить этот аккаунт?',
                'active'                      => 'Активен',
                'address-delete-confirmation' => 'Вы уверены, что хотите удалить этот адрес?',
                'back-btn'                    => 'Назад',
                'create-order'                => 'Создать заказ',
                'customer'                    => 'Клиент',
                'date-of-birth'               => 'Дата рождения - :dob',
                'default-address'             => 'Адрес по умолчанию',
                'delete-account'              => 'Удалить аккаунт',
                'delete'                      => 'Удалить',
                'email'                       => 'Эл. почта - :email',
                'empty-description'           => 'Создайте новые адреса для клиента',
                'empty-title'                 => 'Добавить адрес клиента',
                'gender'                      => 'Пол - :gender',
                'group'                       => 'Группа - :group_code',
                'inactive'                    => 'Неактивен',
                'login-as-customer'           => 'Войти как клиент',
                'note-created-success'        => 'Примечание успешно создано',
                'order-create-confirmation'   => 'Вы уверены, что хотите создать заказ для этого клиента?',
                'phone'                       => 'Телефон - :phone',
                'set-as-default'              => 'Установить по умолчанию',
                'suspended'                   => 'Приостановлен',
                'title'                       => 'Просмотр клиента',

                'address' => [
                    'count'  => 'Адреса (:count)',

                    'create' => [
                        'city'               => 'Город',
                        'company-name'       => 'Название компании',
                        'country'            => 'Страна',
                        'create-btn'         => 'Создать',
                        'create-address-btn' => 'Добавить новый адрес',
                        'default-address'    => 'Адрес по умолчанию',
                        'email'              => 'Эл. почта',
                        'first-name'         => 'Имя',
                        'last-name'          => 'Фамилия',
                        'phone'              => 'Телефон',
                        'post-code'          => 'Почтовый индекс',
                        'save-btn-title'     => 'Сохранить адрес',
                        'select-country'     => 'Выберите страну',
                        'state'              => 'Область',
                        'street-address'     => 'Улица и номер дома',
                        'title'              => 'Создать адрес',
                        'vat-id'             => 'Идентификационный номер НДС',
                    ],

                    'edit' => [
                        'city'            => 'Город',
                        'company-name'    => 'Название компании',
                        'country'         => 'Страна',
                        'default-address' => 'Адрес по умолчанию',
                        'edit-btn'        => 'Редактировать',
                        'email'           => 'Эл. почта',
                        'first-name'      => 'Имя',
                        'last-name'       => 'Фамилия',
                        'phone'           => 'Телефон',
                        'post-code'       => 'Почтовый индекс',
                        'save-btn-title'  => 'Сохранить адрес',
                        'select-country'  => 'Выберите страну',
                        'state'           => 'Область',
                        'street-address'  => 'Улица и номер дома',
                        'title'           => 'Редактировать адрес',
                        'vat-id'          => 'Идентификационный номер НДС',
                    ],

                    'address-delete-success' => 'Адрес успешно удален',
                    'create-success'         => 'Адрес успешно создан',
                    'set-default-success'    => 'Адрес по умолчанию успешно обновлен',
                    'success-mass-delete'    => 'Массовое удаление адресов успешно выполнено',
                    'update-success'         => 'Адрес успешно обновлен',
                ],

                'datagrid' => [
                    'invoices' => [
                        'increment-id'   => 'ID счета',
                        'invoice-amount' => 'Сумма счета',
                        'invoice-date'   => 'Дата счета',
                        'order-id'       => 'ID заказа',
                        'view'           => 'Просмотр',
                        'empty-invoice'  => 'Нет отзывов',
                    ],

                    'orders' => [
                        'canceled'        => 'Отменен',
                        'channel-name'    => 'Имя канала',
                        'closed'          => 'Закрыт',
                        'completed'       => 'Завершен',
                        'customer-name'   => 'Имя клиента',
                        'empty-order'     => 'Нет доступных заказов',
                        'date'            => 'Дата',
                        'email'           => 'Эл. почта',
                        'fraud'           => 'Мошенничество',
                        'grand-total'     => 'Общая сумма',
                        'location'        => 'Местоположение',
                        'order-id'        => 'ID заказа',
                        'pay-via'         => 'Оплата через',
                        'pending'         => 'В ожидании',
                        'pending-payment' => 'Ожидание оплаты',
                        'processing'      => 'Обработка',
                        'status'          => 'Статус',
                        'view'            => 'Просмотр',
                    ],

                    'reviews' => [
                        'approved'      => 'Утверждено',
                        'comment'       => 'Комментарий',
                        'created-at'    => 'Создано',
                        'disapproved'   => 'Не утверждено',
                        'empty-reviews' => 'Нет доступных счетов',
                        'id'            => 'ID',
                        'invoice-date'  => 'Дата счета',
                        'pending'       => 'В ожидании',
                        'product-id'    => 'ID продукта',
                        'product-name'  => 'Название продукта',
                        'rating'        => 'Рейтинг',
                        'status'        => 'Статус',
                        'title'         => 'Заголовок',
                    ],
                ],

                'edit' => [
                    'contact-number'        => 'Контактный номер',
                    'customer-group'        => 'Группа клиента',
                    'date-of-birth'         => 'Дата рождения',
                    'edit-btn'              => 'Редактировать',
                    'email'                 => 'Эл. почта',
                    'female'                => 'Женский',
                    'first-name'            => 'Имя',
                    'gender'                => 'Пол',
                    'last-name'             => 'Фамилия',
                    'male'                  => 'Мужской',
                    'other'                 => 'Другой',
                    'save-btn'              => 'Сохранить клиента',
                    'select-customer-group' => 'Выберите группу клиента',
                    'select-gender'         => 'Выберите пол',
                    'status'                => 'Статус',
                    'suspended'             => 'Приостановлен',
                    'title'                 => 'Редактировать клиента',
                ],

                'invoices' => [
                    'count'        => 'Счета (:count)',
                    'increment-id' => '# :increment_id',
                ],

                'notes' => [
                    'add-note'              => 'Добавить заметку',
                    'customer-not-notified' => ':date | <b>Клиент не уведомлен</b>',
                    'customer-notified'     => ':date | <b>Клиент уведомлен</b>',
                    'note'                  => 'Заметка',
                    'note-placeholder'      => 'Напишите вашу заметку здесь',
                    'notify-customer'       => 'Уведомить клиента',
                    'submit-btn-title'      => 'Отправить заметку',
                ],

                'orders' => [
                    'count'         => 'Заказы (:count)',
                    'increment-id'  => '# :increment_id',
                    'total-revenue' => 'Общий доход - :revenue',
                ],

                'reviews' => [
                    'id'    => 'ID - :id',
                    'count' => 'Отзывы (:count)',
                ],

                'cart' => [
                    'delete-success' => 'Элемент корзины успешно удален.',
                ],

                'wishlist' => [
                    'delete-success' => 'Элемент списка желаний успешно удален.',
                ],

                'compare' => [
                    'delete-success' => 'Элемент сравнения успешно удален.',
                ],
            ],

            'delete-failed'  => 'Ошибка при удалении клиента',
            'delete-success' => 'Клиент успешно удален',
            'order-pending'  => 'Заказы находятся в ожидании',
            'update-success' => 'Клиент успешно обновлен',
        ],

        'groups' => [
            'index' => [
                'title' => 'Группы',

                'create' => [
                    'code'       => 'Код',
                    'create-btn' => 'Создать Группу',
                    'name'       => 'Название',
                    'save-btn'   => 'Сохранить Группу',
                    'success'    => 'Группа успешно создана',
                    'title'      => 'Создать новую Группу',
                ],

                'edit' => [
                    'delete-failed'  => 'Ошибка удаления Группы',
                    'delete-success' => 'Группа успешно удалена',
                    'group-default'  => 'Группу по умолчанию нельзя удалить',
                    'success'        => 'Группа успешно обновлена',
                    'title'          => 'Редактировать Группу',
                ],

                'datagrid' => [
                    'code'   => 'Код',
                    'delete' => 'Удалить',
                    'edit'   => 'Редактировать',
                    'id'     => 'ID',
                    'name'   => 'Название',
                ],
            ],
        ],

        'gdpr' => [
            'index' => [
                'title' => 'Запросы GDPR',

                'datagrid' => [
                    'completed'     => 'Завершено',
                    'created-at'    => 'Создано',
                    'customer-name' => 'Имя клиента',
                    'declined'      => 'Отклонено',
                    'delete'        => 'Удалить',
                    'edit'          => 'Редактировать',
                    'id'            => 'ID',
                    'message'       => 'Сообщение',
                    'pending'       => 'В ожидании',
                    'processing'    => 'Обработка',
                    'revoked'       => 'Отозван',
                    'status'        => 'Статус',
                    'type'          => 'Тип',
                ],

                'modal' => [
                    'completed'     => 'Завершено',
                    'declined'      => 'Отклонено',
                    'message'       => 'Сообщение',
                    'pending'       => 'В ожидании',
                    'processing'    => 'Обработка',
                    'revoked'       => 'Отозван',
                    'save-btn'      => 'Сохранить',
                    'status'        => 'Статус',
                    'title'         => 'Редактировать запрос данных GDPR',
                    'type'          => 'Тип',
                ],

                'update-success'              => 'Запрос данных успешно обновлен и письмо отправлено клиенту.',
                'delete-success'              => 'Запрос данных успешно удален.',
                'attribute-reason-error'      => 'Невозможно удалить.',
                'update-success-unsent-email' => 'Запрос данных успешно обновлен, но письмо не отправлено клиенту.',
            ],
        ],

        'reviews' => [
            'index' => [
                'date'        => 'Дата',
                'description' => 'Описание',
                'id'          => 'ID',
                'name'        => 'Имя',
                'product'     => 'Товар',
                'rating'      => 'Рейтинг',
                'status'      => 'Статус',
                'title'       => 'Отзывы',

                'edit' => [
                    'approved'       => 'Одобрено',
                    'customer'       => 'Клиент',
                    'date'           => 'Дата',
                    'disapproved'    => 'Не одобрено',
                    'id'             => 'ID',
                    'images'         => 'Изображения',
                    'pending'        => 'Ожидает',
                    'product'        => 'Товар',
                    'rating'         => 'Рейтинг',
                    'review-comment' => 'Комментарий',
                    'review-title'   => 'Заголовок',
                    'save-btn'       => 'Сохранить',
                    'status'         => 'Статус',
                    'title'          => 'Редактировать Отзыв',
                    'update-success' => 'Успешно обновлено',
                ],

                'datagrid'   => [
                    'approved'            => 'Одобрено',
                    'comment'             => 'Комментарий',
                    'customer-names'      => 'Имя',
                    'date'                => 'Дата',
                    'delete'              => 'Удалить',
                    'delete-success'      => 'Отзыв успешно удален',
                    'disapproved'         => 'Не одобрено',
                    'edit'                => 'Редактировать',
                    'id'                  => 'ID',
                    'mass-delete-error'   => 'Произошла ошибка',
                    'mass-delete-success' => 'Выбранные отзывы успешно удалены',
                    'mass-update-success' => 'Выбранные отзывы успешно обновлены',
                    'pending'             => 'Ожидает',
                    'product'             => 'Товар',
                    'rating'              => 'Рейтинг',
                    'review-id'           => 'ID - :review_id',
                    'status'              => 'Статус',
                    'title'               => 'Заголовок',
                    'update-status'       => 'Обновить Статус',
                ],
            ],
        ],
    ],

    'marketing' => [
        'communications' => [
            'templates' => [
                'index' => [
                    'create-btn' => 'Создать Шаблон',
                    'title'      => 'Шаблоны Email',

                    'datagrid' => [
                        'active'   => 'Активен',
                        'draft'    => 'Черновик',
                        'id'       => 'ID',
                        'inactive' => 'Неактивен',
                        'name'     => 'Название',
                        'status'   => 'Статус',
                    ],
                ],

                'create' => [
                    'active'         => 'Активен',
                    'back-btn'       => 'Назад',
                    'content'        => 'Содержание',
                    'create-success' => 'Шаблон электронной почты успешно создан.',
                    'draft'          => 'Черновик',
                    'general'        => 'Общие',
                    'inactive'       => 'Неактивен',
                    'name'           => 'Название',
                    'save-btn'       => 'Сохранить Шаблон',
                    'select-status'  => 'Выберите Статус',
                    'status'         => 'Статус',
                    'title'          => 'Создать Шаблон',
                ],

                'edit' => [
                    'active'         => 'Активен',
                    'back-btn'       => 'Назад',
                    'content'        => 'Содержание*',
                    'draft'          => 'Черновик',
                    'general'        => 'Общие',
                    'inactive'       => 'Неактивен',
                    'name'           => 'Название',
                    'save-btn'       => 'Сохранить Шаблон',
                    'status'         => 'Статус',
                    'title'          => 'Редактировать Шаблон',
                    'update-success' => 'Успешно обновлено',
                ],

                'delete-failed'  => ':name не удалось удалить',
                'delete-success' => 'Шаблон успешно удален',
                'email-template' => 'Шаблон электронной почты',
            ],

            'campaigns' => [
                'index' => [
                    'create-btn'  => 'Создать кампанию',
                    'title'       => 'Кампании',

                    'datagrid' => [
                        'active'   => 'Активна',
                        'delete'   => 'Удалить',
                        'edit'     => 'Редактировать',
                        'id'       => 'ID',
                        'inactive' => 'Неактивна',
                        'name'     => 'Название',
                        'status'   => 'Статус',
                        'subject'  => 'Тема',
                    ],
                ],

                'create'    => [
                    'active'          => 'Активна',
                    'back-btn'        => 'Назад',
                    'channel'         => 'Канал',
                    'customer-group'  => 'Группа клиентов',
                    'email-template'  => 'Шаблон электронной почты',
                    'event'           => 'Событие',
                    'general'         => 'Общее',
                    'inactive'        => 'Неактивна',
                    'name'            => 'Название',
                    'save-btn'        => 'Сохранить кампанию',
                    'select-channel'  => 'Выберите канал',
                    'select-event'    => 'Выберите событие',
                    'select-group'    => 'Выберите группу',
                    'select-status'   => 'Выберите статус',
                    'select-template' => 'Выберите шаблон',
                    'setting'         => 'Настройка',
                    'status'          => 'Статус',
                    'subject'         => 'Тема',
                    'title'           => 'Создать кампанию',
                ],

                'edit' => [
                    'active'          => 'Активна',
                    'audience'        => 'Аудитория',
                    'back-btn'        => 'Назад',
                    'channel'         => 'Канал',
                    'customer-group'  => 'Группа клиентов',
                    'email-template'  => 'Шаблон электронной почты',
                    'event'           => 'Событие',
                    'general'         => 'Общее',
                    'inactive'        => 'Неактивна',
                    'name'            => 'Название',
                    'save-btn'        => 'Сохранить кампанию',
                    'select-event'    => 'Выберите событие',
                    'select-status'   => 'Выберите статус',
                    'select-template' => 'Выберите шаблон',
                    'status'          => 'Статус',
                    'subject'         => 'Тема',
                    'title'           => 'Редактировать кампанию',
                ],

                'create-success' => 'Кампания успешно создана.',
                'delete-failed'  => 'Не удалось удалить :name',
                'delete-success' => 'Кампания успешно удалена.',
                'email-campaign' => 'Электронная кампания',
                'update-success' => 'Кампания успешно обновлена.',
            ],

            'events' => [
                'index' => [
                    'create-btn' => 'Создать событие',
                    'event'      => 'Событие',
                    'title'      => 'События',

                    'datagrid' => [
                        'actions' => 'Действия',
                        'date'    => 'Дата',
                        'delete'  => 'Удалить',
                        'edit'    => 'Редактировать',
                        'id'      => 'ID',
                        'name'    => 'Название',
                    ],

                    'create'   => [
                        'date'           => 'Дата',
                        'delete-warning' => 'Вы уверены, что хотите выполнить это действие?',
                        'description'    => 'Описание',
                        'general'        => 'Общее',
                        'name'           => 'Название',
                        'save-btn'       => 'Сохранить событие',
                        'success'        => 'Событие успешно создано',
                        'title'          => 'Создать событие',
                    ],

                    'edit'  => [
                        'success' => 'Событие успешно обновлено',
                        'title'   => 'Редактировать событие',
                    ],
                ],

                'delete-failed'  => ':name удалить не удалось',
                'delete-success' => 'Событие успешно удалено',
                'edit-error'     => 'Невозможно редактировать событие',
            ],

            'subscribers' => [
                'index' => [
                    'title' => 'Подписчики на рассылку',

                    'datagrid' => [
                        'actions'    => 'Действия',
                        'delete'     => 'Удалить',
                        'edit'       => 'Редактировать',
                        'email'      => 'Email',
                        'false'      => 'Нет',
                        'id'         => 'ID',
                        'subscribed' => 'Подписан',
                        'true'       => 'Да',
                    ],

                    'edit' => [
                        'back-btn'      => 'Назад',
                        'email'         => 'Email',
                        'false'         => 'Нет',
                        'save-btn'      => 'Сохранить подписчика',
                        'subscribed'    => 'Подписан',
                        'success'       => 'Подписка на рассылку успешно обновлена',
                        'title'         => 'Редактировать подписчика на рассылку',
                        'true'          => 'Да',
                        'update-failed' => 'Подписка на рассылку не обновлена',
                    ],
                ],

                'delete-failed'  => 'Не удалось удалить подписчика',
                'delete-success' => 'Подписчик успешно удален',
                'delete-warning' => 'Вы уверены, что хотите выполнить это действие?',
            ],
        ],

        'promotions' => [
            'index' => [
                'cart-rule-title'    => 'Правила корзины',
                'catalog-rule-title' => 'Каталоговые правила',
            ],

            'cart-rules' => [
                'index' => [
                    'create-btn' => 'Создать правило корзины',
                    'title'      => 'Правила корзины',

                    'datagrid' => [
                        'active'      => 'Активное',
                        'copy'        => 'Копировать',
                        'copy-of'     => ':value',
                        'coupon-code' => 'Промокод',
                        'delete'      => 'Удалить',
                        'draft'       => 'Черновик',
                        'edit'        => 'Редактировать',
                        'end'         => 'Конец',
                        'id'          => 'ID',
                        'inactive'    => 'Неактивное',
                        'name'        => 'Название',
                        'priority'    => 'Приоритет',
                        'start'       => 'Начало',
                        'status'      => 'Статус',
                    ],
                ],

                'create' => [
                    'action-type'                               => 'Тип действия',
                    'actions'                                   => 'Действия',
                    'add-condition'                             => 'Добавить условие',
                    'additional'                                => 'Дополнительно',
                    'all-conditions-true'                       => 'Все условия верны',
                    'any-conditions-true'                       => 'Любое условие верно',
                    'apply-to-shipping'                         => 'Применить к доставке',
                    'attribute-family'                          => 'Семейство атрибутов',
                    'attribute-name-children-only'              => 'Имя атрибута (только дочерние)',
                    'attribute-name-parent-only'                => 'Имя атрибута (только родительские)',
                    'auto-generate-coupon'                      => 'Автоматическая генерация промокода',
                    'back-btn'                                  => 'Назад',
                    'buy-x-get-y-free'                          => 'Купи X, получи Y бесплатно',
                    'buy-x-quantity'                            => 'Купи X количество',
                    'cart-attribute'                            => 'Атрибут корзины',
                    'cart-item-attribute'                       => 'Атрибут товара в корзине',
                    'categories'                                => 'Категории',
                    'channels'                                  => 'Каналы',
                    'children-categories'                       => 'Дочерние категории',
                    'choose-condition-to-add'                   => 'Выберите условие для добавления',
                    'condition-type'                            => 'Тип условия',
                    'conditions'                                => 'Условия',
                    'contain'                                   => 'Содержит',
                    'contains'                                  => 'Содержит',
                    'coupon-code'                               => 'Промокод',
                    'coupon-type'                               => 'Тип промокода',
                    'create-success'                            => 'Правило корзины успешно создано',
                    'customer-groups'                           => 'Группы клиентов',
                    'description'                               => 'Описание',
                    'discount-amount'                           => 'Сумма скидки',
                    'does-not-contain'                          => 'Не содержит',
                    'end-of-other-rules'                        => 'Завершение других правил',
                    'equals-or-greater-than'                    => 'Равно или больше',
                    'equals-or-less-than'                       => 'Равно или меньше',
                    'fixed-amount'                              => 'Фиксированная сумма',
                    'fixed-amount-whole-cart'                   => 'Фиксированная сумма для всей корзины',
                    'free-shipping'                             => 'Бесплатная доставка',
                    'from'                                      => 'От',
                    'general'                                   => 'Общие',
                    'greater-than'                              => 'Больше',
                    'is-equal-to'                               => 'Равно',
                    'is-not-equal-to'                           => 'Не равно',
                    'less-than'                                 => 'Меньше',
                    'marketing-time'                            => 'Время маркетинга',
                    'maximum-quantity-allowed-to-be-discounted' => 'Максимальное количество для скидки',
                    'name'                                      => 'Название',
                    'no'                                        => 'Нет',
                    'no-coupon'                                 => 'Нет промокода',
                    'parent-categories'                         => 'Родительские категории',
                    'payment-method'                            => 'Способ оплаты',
                    'percentage-product-price'                  => 'Процент от цены продукта',
                    'price-in-cart'                             => 'Цена в корзине',
                    'priority'                                  => 'Приоритет',
                    'product-attribute'                         => 'Атрибут продукта',
                    'qty-in-cart'                               => 'Количество в корзине',
                    'save-btn'                                  => 'Сохранить правило корзины',
                    'settings'                                  => 'Настройки',
                    'shipping-country'                          => 'Страна доставки',
                    'shipping-method'                           => 'Способ доставки',
                    'shipping-postcode'                         => 'Почтовый индекс доставки',
                    'shipping-state'                            => 'Регион доставки',
                    'specific-coupon'                           => 'Определенный промокод',
                    'status'                                    => 'Статус',
                    'subtotal'                                  => 'Сумма заказа',
                    'title'                                     => 'Создать правило корзины',
                    'to'                                        => 'До',
                    'total-items-qty'                           => 'Общее количество товаров',
                    'total-weight'                              => 'Общий вес',
                    'uses-per-coupon'                           => 'Использований на промокод',
                    'uses-per-customer'                         => 'Использований на клиента',
                    'uses-per-customer-control-info'            => 'Будет использоваться только для зарегистрированных клиентов.',
                    'yes'                                       => 'Да',
                ],

                'edit' => [
                    'action-type'                               => 'Тип действия',
                    'actions'                                   => 'Действия',
                    'add-condition'                             => 'Добавить условие',
                    'additional'                                => 'Дополнительно',
                    'all-conditions-true'                       => 'Все условия верны',
                    'alphabetical'                              => 'Только буквы',
                    'alphanumeric'                              => 'Буквенно-цифровой',
                    'any-conditions-true'                       => 'Любое условие верно',
                    'apply-to-shipping'                         => 'Применить к доставке',
                    'attribute-family'                          => 'Семейство атрибутов',
                    'attribute-name-children-only'              => 'Имя атрибута (только дочерние)',
                    'attribute-name-parent-only'                => 'Имя атрибута (только родительские)',
                    'auto-generate-coupon'                      => 'Автоматическая генерация промокода',
                    'back-btn'                                  => 'Назад',
                    'buy-x-get-y-free'                          => 'Купи X, получи Y бесплатно',
                    'buy-x-quantity'                            => 'Купи X количество',
                    'cart-attribute'                            => 'Атрибут корзины',
                    'cart-item-attribute'                       => 'Атрибут товара в корзине',
                    'categories'                                => 'Категории',
                    'channels'                                  => 'Каналы',
                    'children-categories'                       => 'Дочерние категории',
                    'choose-condition-to-add'                   => 'Выберите условие для добавления',
                    'code-format'                               => 'Формат кода',
                    'code-prefix'                               => 'Префикс кода',
                    'code-suffix'                               => 'Суффикс кода',
                    'condition-type'                            => 'Тип условия',
                    'conditions'                                => 'Условия',
                    'contain'                                   => 'Содержит',
                    'contains'                                  => 'Содержит',
                    'coupon-code'                               => 'Промокод',
                    'coupon-length'                             => 'Длина промокода',
                    'coupon-qty'                                => 'Количество промокодов',
                    'coupon-type'                               => 'Тип промокода',
                    'customer-group'                            => 'Группа клиентов',
                    'customer-groups'                           => 'Группы клиентов',
                    'description'                               => 'Описание',
                    'discount-amount'                           => 'Сумма скидки',
                    'does-not-contain'                          => 'Не содержит',
                    'end-of-other-rules'                        => 'Завершение других правил',
                    'equals-or-greater-than'                    => 'Равно или больше',
                    'equals-or-less-than'                       => 'Равно или меньше',
                    'fixed-amount'                              => 'Фиксированная сумма',
                    'fixed-amount-whole-cart'                   => 'Фиксированная сумма для всей корзины',
                    'free-shipping'                             => 'Бесплатная доставка',
                    'from'                                      => 'От',
                    'general'                                   => 'Общие',
                    'generate'                                  => 'Генерировать',
                    'greater-than'                              => 'Больше',
                    'is-equal-to'                               => 'Равно',
                    'is-not-equal-to'                           => 'Не равно',
                    'less-than'                                 => 'Меньше',
                    'marketing-time'                            => 'Время маркетинга',
                    'maximum-quantity-allowed-to-be-discounted' => 'Максимальное количество для скидки',
                    'name'                                      => 'Название',
                    'no'                                        => 'Нет',
                    'no-coupon'                                 => 'Нет промокода',
                    'numeric'                                   => 'Только цифры',
                    'parent-categories'                         => 'Родительские категории',
                    'payment-method'                            => 'Способ оплаты',
                    'percentage-product-price'                  => 'Процент от цены продукта',
                    'price-in-cart'                             => 'Цена в корзине',
                    'priority'                                  => 'Приоритет',
                    'product-attribute'                         => 'Атрибут продукта',
                    'qty-in-cart'                               => 'Количество в корзине',
                    'save-btn'                                  => 'Сохранить правило корзины',
                    'settings'                                  => 'Настройки',
                    'shipping-country'                          => 'Страна доставки',
                    'shipping-method'                           => 'Способ доставки',
                    'shipping-postcode'                         => 'Почтовый индекс доставки',
                    'shipping-state'                            => 'Регион доставки',
                    'specific-coupon'                           => 'Определенный промокод',
                    'status'                                    => 'Статус',
                    'subtotal'                                  => 'Сумма заказа',
                    'title'                                     => 'Редактировать правило корзины',
                    'to'                                        => 'До',
                    'total-items-qty'                           => 'Общее количество товаров',
                    'total-weight'                              => 'Общий вес',
                    'update-success'                            => 'Правило корзины успешно обновлено',
                    'uses-per-coupon'                           => 'Использований на промокод',
                    'uses-per-customer'                         => 'Использований на клиента',
                    'uses-per-customer-control-info'            => 'Будет использоваться только для зарегистрированных клиентов.',
                    'yes'                                       => 'Да',
                ],

                'delete-failed'  => 'Ошибка удаления правила корзины',
                'delete-success' => 'Правило корзины успешно удалено',
            ],

            'catalog-rules' => [
                'index' => [
                    'create-btn' => 'Создать правило каталога',
                    'title'      => 'Правила каталога',

                    'datagrid' => [
                        'active'   => 'Активное',
                        'delete'   => 'Удалить',
                        'edit'     => 'Редактировать',
                        'end'      => 'Конец',
                        'id'       => 'ID',
                        'inactive' => 'Неактивное',
                        'name'     => 'Название',
                        'priority' => 'Приоритет',
                        'start'    => 'Начало',
                        'status'   => 'Статус',
                    ],
                ],

                'create' => [
                    'action-type'              => 'Тип действия',
                    'actions'                  => 'Действия',
                    'add-condition'            => 'Добавить условие',
                    'all-conditions-true'      => 'Все условия верны',
                    'any-conditions-true'      => 'Любое условие верно',
                    'attribute-family'         => 'Семейство атрибутов',
                    'back-btn'                 => 'Назад',
                    'categories'               => 'Категории',
                    'channels'                 => 'Каналы',
                    'choose-condition-to-add'  => 'Выберите условие для добавления',
                    'condition-type'           => 'Тип условия',
                    'conditions'               => 'Условия',
                    'contain'                  => 'Содержит',
                    'contains'                 => 'Содержит',
                    'customer-groups'          => 'Группы клиентов',
                    'description'              => 'Описание',
                    'discount-amount'          => 'Сумма скидки',
                    'does-not-contain'         => 'Не содержит',
                    'end-other-rules'          => 'Завершение других правил',
                    'equals-or-greater-than'   => 'Равно или больше',
                    'equals-or-less-than'      => 'Равно или меньше',
                    'fixed-amount'             => 'Фиксированная сумма',
                    'from'                     => 'От',
                    'general'                  => 'Общие',
                    'greater-than'             => 'Больше',
                    'is-equal-to'              => 'Равно',
                    'is-not-equal-to'          => 'Не равно',
                    'less-than'                => 'Меньше',
                    'marketing-time'           => 'Время маркетинга',
                    'name'                     => 'Название',
                    'no'                       => 'Нет',
                    'percentage-product-price' => 'Процент от цены продукта',
                    'priority'                 => 'Приоритет',
                    'product-attribute'        => 'Атрибут продукта',
                    'save-btn'                 => 'Сохранить правило каталога',
                    'settings'                 => 'Настройки',
                    'status'                   => 'Статус',
                    'title'                    => 'Создать правило каталога',
                    'to'                       => 'До',
                    'yes'                      => 'Да',
                ],

                'edit' => [
                    'action-type'              => 'Тип действия',
                    'actions'                  => 'Действия',
                    'add-condition'            => 'Добавить условие',
                    'all-conditions-true'      => 'Все условия верны',
                    'any-conditions-true'      => 'Любое условие верно',
                    'back-btn'                 => 'Назад',
                    'categories'               => 'Категории',
                    'channels'                 => 'Каналы',
                    'choose-condition-to-add'  => 'Выберите условие для добавления',
                    'condition-type'           => 'Тип условия',
                    'conditions'               => 'Условия',
                    'contain'                  => 'Содержит',
                    'contains'                 => 'Содержит',
                    'customer-groups'          => 'Группы клиентов',
                    'description'              => 'Описание',
                    'discount-amount'          => 'Сумма скидки',
                    'does-not-contain'         => 'Не содержит',
                    'end-other-rules'          => 'Завершение других правил',
                    'equals-or-greater-than'   => 'Равно или больше',
                    'equals-or-less-than'      => 'Равно или меньше',
                    'fixed-amount'             => 'Фиксированная сумма',
                    'from'                     => 'От',
                    'general'                  => 'Общие',
                    'greater-than'             => 'Больше',
                    'is-equal-to'              => 'Равно',
                    'is-not-equal-to'          => 'Не равно',
                    'less-than'                => 'Меньше',
                    'marketing-time'           => 'Время маркетинга',
                    'name'                     => 'Название',
                    'no'                       => 'Нет',
                    'percentage-product-price' => 'Процент от цены продукта',
                    'priority'                 => 'Приоритет',
                    'product-attribute'        => 'Атрибут продукта',
                    'save-btn'                 => 'Сохранить правило каталога',
                    'settings'                 => 'Настройки',
                    'status'                   => 'Статус',
                    'title'                    => 'Редактировать правило каталога',
                    'to'                       => 'До',
                    'yes'                      => 'Да',
                ],

                'create-success' => 'Правило каталога успешно создано',
                'delete-success' => 'Правило каталога успешно удалено',
                'update-success' => 'Правило каталога успешно обновлено',
            ],

            'cart-rules-coupons' => [
                'cart-rule-not-defined-error' => 'Правило корзины не может быть удалено',
                'delete-success'              => 'Купон правила корзины успешно удален',
                'mass-delete-success'         => 'Выбранные элементы успешно удалены',
                'success'                     => ':name успешно создано',

                'datagrid' => [
                    'coupon-code'     => 'Код купона',
                    'created-date'    => 'Дата создания',
                    'delete'          => 'Удалить',
                    'expiration-date' => 'Дата истечения срока действия',
                    'id'              => 'ID',
                    'times-used'      => 'Количество использований',
                ],
            ],
        ],

        'search-seo' => [
            'search-terms' => [
                'index' => [
                    'create-btn' => 'Создать новый поисковый термин',
                    'title'      => 'Термины поиска',

                    'datagrid' => [
                        'actions'             => 'Действия',
                        'channel'             => 'Канал',
                        'delete'              => 'Удалить',
                        'edit'                => 'Редактировать',
                        'id'                  => 'ID',
                        'locale'              => 'Региональные настройки',
                        'mass-delete-success' => 'Выбранные поисковые термины успешно удалены',
                        'redirect-url'        => 'Адрес перенаправления',
                        'results'             => 'Результаты',
                        'search-query'        => 'Поисковый запрос',
                        'uses'                => 'Использование',
                    ],

                    'create' => [
                        'channel'        => 'Канал',
                        'delete-warning' => 'Вы уверены, что хотите выполнить эту операцию?',
                        'locale'         => 'Региональные настройки',
                        'redirect-url'   => 'Адрес перенаправления',
                        'results'        => 'Результаты',
                        'save-btn'       => 'Сохранить поисковый термин',
                        'search-query'   => 'Поисковый запрос',
                        'success'        => 'Поисковый термин успешно создан',
                        'title'          => 'Создать новый поисковый термин',
                        'uses'           => 'Использование',
                    ],

                    'edit' => [
                        'delete-success' => 'Поисковый термин успешно удален',
                        'success'        => 'Поисковый термин успешно обновлен',
                        'title'          => 'Редактировать поисковый термин',
                    ],
                ],
            ],

            'search-synonyms' => [
                'index' => [
                    'create-btn' => 'Создать синоним поиска',
                    'title'      => 'Синонимы поиска',

                    'datagrid' => [
                        'actions'             => 'Действия',
                        'delete'              => 'Удалить',
                        'edit'                => 'Редактировать',
                        'id'                  => 'ID',
                        'mass-delete-success' => 'Выбранные синонимы поиска успешно удалены',
                        'name'                => 'Название',
                        'terms'               => 'Термины',
                    ],

                    'create' => [
                        'delete-warning' => 'Вы уверены, что хотите выполнить это действие?',
                        'name'           => 'Название',
                        'save-btn'       => 'Сохранить синоним поиска',
                        'success'        => 'Синоним поиска успешно создан',
                        'terms'          => 'Термины',
                        'terms-info'     => 'Введите синонимы как список, разделенный запятыми, например, "обувь, обувь." Это расширяет поиск, чтобы включить все термины.',
                        'title'          => 'Создать синоним поиска',
                    ],

                    'edit' => [
                        'delete-success' => 'Синоним поиска успешно удален',
                        'success'        => 'Синоним поиска успешно обновлен',
                        'title'          => 'Редактировать синоним поиска',
                    ],
                ],
            ],

            'sitemaps' => [
                'index' => [
                    'create-btn' => 'Создать карту сайта',
                    'sitemap'    => 'Карта сайта',
                    'title'      => 'Карты сайта',

                    'datagrid' => [
                        'actions'         => 'Действия',
                        'delete'          => 'Удалить',
                        'edit'            => 'Редактировать',
                        'file-name'       => 'Имя файла',
                        'id'              => 'ID',
                        'link-for-google' => 'Ссылка для Google',
                        'path'            => 'Путь',
                    ],

                    'create' => [
                        'delete-warning' => 'Вы уверены, что хотите выполнить это действие?',
                        'file-name'      => 'Имя файла',
                        'file-name-info' => 'Пример: sitemap.xml',
                        'path'           => 'Путь',
                        'path-info'      => 'Пример: "/sitemap/" или "/" для базового пути',
                        'save-btn'       => 'Сохранить карту сайта',
                        'success'        => 'Карта сайта успешно создана',
                        'title'          => 'Создать карту сайта',
                    ],

                    'edit' => [
                        'delete-success' => 'Карта сайта успешно удалена',
                        'success'        => 'Карта сайта успешно обновлена',
                        'title'          => 'Редактировать карту сайта',
                    ],
                ],

                'edit' => [
                    'back-btn'       => 'Назад',
                    'file-name'      => 'Имя файла',
                    'file-name-info' => 'Пример: sitemap.xml',
                    'general'        => 'Общие',
                    'path'           => 'Путь',
                    'path-info'      => 'Пример: "/sitemap/" или "/" для базового пути',
                    'save-btn'       => 'Сохранить карту сайта',
                ],

                'delete-failed' => ':name удаление не удалось',
            ],

            'url-rewrites' => [
                'index' => [
                    'create-btn' => 'Создать перенаправление URL',
                    'title'      => 'Перенаправления URL',

                    'datagrid' => [
                        'actions'             => 'Действия',
                        'category'            => 'Категория',
                        'cms-page'            => 'Страница CMS',
                        'delete'              => 'Удалить',
                        'edit'                => 'Редактировать',
                        'for'                 => 'Для',
                        'id'                  => 'ID',
                        'locale'              => 'Локаль',
                        'mass-delete-success' => 'Выбранные перенаправления URL были успешно удалены.',
                        'permanent-redirect'  => 'Постоянное (301)',
                        'product'             => 'Продукт',
                        'redirect-type'       => 'Тип перенаправления',
                        'request-path'        => 'Путь запроса',
                        'target-path'         => 'Целевой путь',
                        'temporary-redirect'  => 'Временное (302)',
                    ],

                    'create' => [
                        'category'           => 'Категория',
                        'cms-page'           => 'Страница CMS',
                        'delete-warning'     => 'Вы уверены, что хотите выполнить это действие?',
                        'for'                => 'Для',
                        'locale'             => 'Локаль',
                        'permanent-redirect' => 'Постоянное (301)',
                        'product'            => 'Продукт',
                        'redirect-type'      => 'Тип перенаправления',
                        'request-path'       => 'Путь запроса',
                        'save-btn'           => 'Сохранить перенаправление URL',
                        'success'            => 'Перенаправление URL успешно создано.',
                        'target-path'        => 'Целевой путь',
                        'temporary-redirect' => 'Временное (302)',
                        'title'              => 'Создать перенаправление URL',
                    ],

                    'edit' => [
                        'delete-success' => 'Перенаправление URL успешно удалено.',
                        'success'        => 'Перенаправление URL успешно обновлено.',
                        'title'          => 'Редактировать перенаправление URL',
                    ],
                ],
            ],
        ],
    ],

    'cms' => [
        'index' => [
            'already-taken' => 'Это имя :name уже занято.',
            'create-btn'    => 'Создать страницу',
            'channel'       => 'Канал',
            'language'      => 'Язык',
            'title'         => 'Страницы',

            'datagrid' => [
                'channel'             => 'Канал',
                'delete'              => 'Удалить',
                'edit'                => 'Редактировать',
                'id'                  => 'ID',
                'mass-delete-success' => 'Выбранные данные успешно удалены',
                'page-title'          => 'Заголовок страницы',
                'url-key'             => 'URL-ключ',
                'view'                => 'Просмотр',
            ],
        ],

        'create' => [
            'channels'         => 'Каналы',
            'content'          => 'Содержание',
            'description'      => 'Описание',
            'general'          => 'Общие',
            'meta-description' => 'Мета-описание',
            'meta-keywords'    => 'Мета-ключевые слова',
            'meta-title'       => 'Мета-заголовок',
            'page-title'       => 'Заголовок страницы',
            'save-btn'         => 'Сохранить страницу',
            'seo'              => 'SEO',
            'title'            => 'Создать страницу',
            'url-key'          => 'URL-ключ',
        ],

        'edit' => [
            'back-btn'         => 'Назад',
            'channels'         => 'Каналы',
            'content'          => 'Содержание',
            'description'      => 'Описание',
            'general'          => 'Общие',
            'meta-description' => 'Мета-описание',
            'meta-keywords'    => 'Мета-ключевые слова',
            'meta-title'       => 'Мета-заголовок',
            'page-title'       => 'Заголовок страницы',
            'preview-btn'      => 'Просмотр страницы',
            'save-btn'         => 'Сохранить страницу',
            'seo'              => 'SEO',
            'title'            => 'Редактировать страницу',
            'url-key'          => 'URL-ключ',
        ],

        'create-success' => 'Страница CMS успешно создана.',
        'delete-success' => 'Страница CMS успешно удалена.',
        'no-resource'    => 'Ресурс не существует.',
        'update-success' => 'Страница CMS успешно обновлена.',
    ],

    'settings' => [
        'locales' => [
            'index' => [
                'create-btn' => 'Создать локаль',
                'locale'     => 'Локаль',
                'logo-size'  => 'Разрешение изображения должно быть 24px x 16px',
                'title'      => 'Языки',

                'datagrid' => [
                    'actions'   => 'Действия',
                    'code'      => 'Код',
                    'delete'    => 'Удалить',
                    'direction' => 'Направление',
                    'edit'      => 'Редактировать',
                    'id'        => 'ID',
                    'ltr'       => 'LTR',
                    'name'      => 'Название',
                    'rtl'       => 'RTL',
                ],

                'create' => [
                    'code'             => 'Код',
                    'direction'        => 'Направление',
                    'locale-logo'      => 'Логотип локали',
                    'name'             => 'Название',
                    'save-btn'         => 'Сохранить локаль',
                    'select-direction' => 'Выбрать направление',
                    'title'            => 'Создать локаль',
                ],

                'edit' => [
                    'title' => 'Редактировать локали',
                ],

                'create-success'    => 'Локаль успешно создана.',
                'delete-failed'     => 'Ошибка при удалении локали',
                'delete-success'    => 'Локаль успешно удалена.',
                'delete-warning'    => 'Вы уверены, что хотите выполнить это действие?',
                'last-delete-error' => 'Необходимо хотя бы одну локаль.',
                'update-success'    => 'Локаль успешно обновлена.',
            ],
        ],

        'currencies' => [
            'index' => [
                'create-btn' => 'Создать валюту',
                'currency'   => 'Валюта',
                'title'      => 'Валюты',

                'datagrid' => [
                    'actions'        => 'Действия',
                    'code'           => 'Код',
                    'delete'         => 'Удалить',
                    'edit'           => 'Редактировать',
                    'id'             => 'ID',
                    'method-error'   => 'Ошибка! Обнаружен неправильный метод, проверьте конфигурацию массового действия',
                    'name'           => 'Название',
                    'no-resource'    => 'Ресурс, предоставленный для действия, недостаточен',
                    'partial-action' => 'Некоторые действия не были выполнены из-за ограничений системы на :resource',
                    'update-success' => 'Выбранные :resource успешно обновлены',
                ],

                'create' => [
                    'code'                   => 'Код',
                    'create-btn'             => 'Создать валюту',
                    'currency-position'      => 'Положение валюты',
                    'decimal'                => 'Десятичная часть',
                    'decimal-separator'      => 'Десятичный разделитель',
                    'decimal-separator-note' => 'Поле :attribute может принимать только операторы запятая (,) и точка (.)',
                    'delete-warning'         => 'Вы уверены, что хотите выполнить это действие?',
                    'general'                => 'Общие',
                    'group-separator'        => 'Разделитель групп',
                    'group-separator-note'   => 'Поле :attribute может содержать только символы запятая (,), точка (.), апостроф (\') и пробел ( ).',
                    'name'                   => 'Название',
                    'save-btn'               => 'Сохранить валюту',
                    'symbol'                 => 'Символ',
                    'title'                  => 'Создать новую валюту',
                ],

                'edit' => [
                    'title' => 'Редактировать валюту',
                ],

                'create-success'    => 'Валюта успешно создана.',
                'delete-failed'     => 'Ошибка при удалении валюты',
                'delete-success'    => 'Валюта успешно удалена.',
                'last-delete-error' => 'По крайней мере, одна валюта обязательна.',
                'update-success'    => 'Валюта успешно обновлена.',
            ],
        ],

        'data-transfer'     => [
            'imports' => [
                'create' => [
                    'action'              => 'Действие',
                    'allowed-errors'      => 'Разрешенные ошибки',
                    'back-btn'            => 'Назад',
                    'create-update'       => 'Создать/Обновить',
                    'delete'              => 'Удалить',
                    'download-sample'     => 'Скачать Пример',
                    'field-separator'     => 'Разделитель Полей',
                    'file'                => 'Файл',
                    'file-info'           => 'Используйте относительный путь к /project-root/storage/app/import, например, product-images, import-images.',
                    'file-info-example'   => 'Например, в случае product-images, файлы должны быть помещены в папку /project-root/storage/app/import/product-images.',
                    'general'             => 'Общие',
                    'images-directory'    => 'Путь к Папке Изображений',
                    'process-in-queue'    => 'Обработка в Очереди',
                    'results'             => 'Результаты',
                    'save-btn'            => 'Сохранить Импорт',
                    'settings'            => 'Настройки',
                    'skip-errors'         => 'Пропустить Ошибки',
                    'stop-on-errors'      => 'Остановить при Ошибках',
                    'title'               => 'Создать Импорт',
                    'type'                => 'Тип',
                    'validation-strategy' => 'Стратегия Валидации',
                ],

                'edit' => [
                    'action'              => 'Действие',
                    'allowed-errors'      => 'Разрешенные ошибки',
                    'back-btn'            => 'Назад',
                    'create-update'       => 'Создать/Обновить',
                    'current-file'        => 'Текущий загруженный файл',
                    'delete'              => 'Удалить',
                    'download-sample'     => 'Скачать Пример',
                    'field-separator'     => 'Разделитель Полей',
                    'file'                => 'Файл',
                    'file-info'           => 'Используйте относительный путь к /project-root/storage/app/import, например, product-images, import-images.',
                    'file-info-example'   => 'Например, в случае product-images, файлы должны быть помещены в папку /project-root/storage/app/import/product-images.',
                    'general'             => 'Общие',
                    'images-directory'    => 'Путь к Папке Изображений',
                    'process-in-queue'    => 'Обработка в Очереди',
                    'results'             => 'Результаты',
                    'save-btn'            => 'Сохранить Импорт',
                    'settings'            => 'Настройки',
                    'skip-errors'         => 'Пропустить Ошибки',
                    'stop-on-errors'      => 'Остановить при Ошибках',
                    'title'               => 'Редактировать Импорт',
                    'type'                => 'Тип',
                    'validation-strategy' => 'Стратегия Валидации',
                ],

                'index' => [
                    'button-title' => 'Создать Импорт',
                    'title'        => 'Импорты',

                    'datagrid' => [
                        'actions'       => 'Действия',
                        'completed-at'  => 'Завершено В',
                        'created'       => 'Создано',
                        'delete'        => 'Удалить',
                        'deleted'       => 'Удалено',
                        'edit'          => 'Редактировать',
                        'error-file'    => 'Файл Ошибок',
                        'id'            => 'ИД',
                        'started-at'    => 'Начато В',
                        'state'         => 'Состояние',
                        'summary'       => 'Сводка',
                        'updated'       => 'Обновлено',
                        'uploaded-file' => 'Загруженный Файл',
                    ],
                ],

                'import' => [
                    'back-btn'                => 'Назад',
                    'completed-batches'       => 'Общее Количество Выполненных Партий:',
                    'download-error-report'   => 'Скачать Полный Отчет',
                    'edit-btn'                => 'Редактировать',
                    'imported-info'           => 'Поздравляем! Ваш импорт прошел успешно.',
                    'importing-info'          => 'Импорт в процессе',
                    'indexing-info'           => 'Индексация Ресурсов (Цены, Инвентаризация и Elasticsearch) В процессе',
                    'linking-info'            => 'Связывание Ресурсов В процессе',
                    'progress'                => 'Прогресс:',
                    'title'                   => 'Импорт',
                    'total-batches'           => 'Общее Количество Партий:',
                    'total-created'           => 'Общее Количество Созданных Записей:',
                    'total-deleted'           => 'Общее Количество Удаленных Записей:',
                    'total-errors'            => 'Общее Количество Ошибок:',
                    'total-invalid-rows'      => 'Общее Количество Недопустимых Строк:',
                    'total-rows-processed'    => 'Общее Количество Обработанных Строк:',
                    'total-updated'           => 'Общее Количество Обновленных Записей:',
                    'validate-info'           => 'Нажмите на Проверить Данные, чтобы проверить ваш импорт.',
                    'validate'                => 'Проверить',
                    'validating-info'         => 'Данные начали чтение и Валидацию',
                    'validation-failed-info'  => 'Ваш импорт невалиден. Пожалуйста, исправьте следующие ошибки и попробуйте снова.',
                    'validation-success-info' => 'Ваш импорт валиден. Нажмите на Импорт, чтобы начать процесс импорта.',
                ],

                'create-success'    => 'Импорт создан успешно.',
                'delete-failed'     => 'Не удалось удалить импорт неожиданно.',
                'delete-success'    => 'Импорт удален успешно.',
                'not-valid'         => 'Импорт невалиден',
                'nothing-to-import' => 'Нет ресурсов для импорта.',
                'setup-queue-error' => 'Пожалуйста, измените драйвер очереди на "база данных" или "redis", чтобы начать процесс импорта.',
                'update-success'    => 'Импорт обновлен успешно.',
            ],
        ],

        'exchange-rates' => [
            'index' => [
                'create-btn'    => 'Создать обменный курс',
                'exchange-rate' => 'Обменный курс',
                'title'         => 'Обменные курсы',
                'update-rates'  => 'Обновить обменный курс',

                'create' => [
                    'delete-warning'         => 'Вы уверены, что хотите выполнить это действие?',
                    'rate'                   => 'Курс',
                    'save-btn'               => 'Сохранить обменный курс',
                    'select-target-currency' => 'Выберите целевую валюту',
                    'source-currency'        => 'Исходная валюта',
                    'target-currency'        => 'Целевая валюта',
                    'title'                  => 'Создать обменный курс',
                ],

                'edit' => [
                    'title' => 'Редактировать обменные курсы',
                ],

                'datagrid' => [
                    'actions'       => 'Действия',
                    'currency-name' => 'Название валюты',
                    'delete'        => 'Удалить',
                    'edit'          => 'Редактировать',
                    'exchange-rate' => 'Обменный курс',
                    'id'            => 'ID',
                ],

                'create-success' => 'Обменный курс успешно создан',
                'delete-error'   => 'Ошибка при удалении обменного курса',
                'delete-success' => 'Обменный курс успешно удален',
                'update-success' => 'Обменный курс успешно обновлен',
            ],
        ],

        'inventory-sources' => [
            'index' => [
                'create-btn' => 'Создать источник инвентаризации',
                'title'      => 'Источники инвентаря',

                'datagrid' => [
                    'active'   => 'Активен',
                    'code'     => 'Код',
                    'delete'   => 'Удалить',
                    'edit'     => 'Редактировать',
                    'id'       => 'ID',
                    'inactive' => 'Неактивен',
                    'name'     => 'Название',
                    'priority' => 'Приоритет',
                    'status'   => 'Статус',
                ],
            ],

            'create' => [
                'add-title'      => 'Добавить источник инвентаря',
                'address'        => 'Адрес источника',
                'back-btn'       => 'Назад',
                'city'           => 'Город',
                'code'           => 'Код',
                'contact-email'  => 'Email',
                'contact-fax'    => 'Факс',
                'contact-info'   => 'Контактная информация',
                'contact-name'   => 'Имя',
                'contact-number' => 'Номер телефона',
                'country'        => 'Страна',
                'description'    => 'Описание',
                'general'        => 'Общие',
                'latitude'       => 'Широта',
                'longitude'      => 'Долгота',
                'name'           => 'Название',
                'postcode'       => 'Почтовый индекс',
                'priority'       => 'Приоритет',
                'save-btn'       => 'Сохранить источник инвентаря',
                'select-country' => 'Выберите страну',
                'select-state'   => 'Выберите область',
                'settings'       => 'Настройки',
                'state'          => 'Область',
                'status'         => 'Статус',
                'street'         => 'Улица',
                'title'          => 'Источники инвентаря',
            ],

            'edit' => [
                'back-btn'       => 'Назад',
                'city'           => 'Город',
                'code'           => 'Код',
                'contact-email'  => 'Email',
                'contact-fax'    => 'Факс',
                'contact-info'   => 'Контактная информация',
                'contact-name'   => 'Имя',
                'contact-number' => 'Номер телефона',
                'country'        => 'Страна',
                'description'    => 'Описание',
                'general'        => 'Общие',
                'latitude'       => 'Широта',
                'longitude'      => 'Долгота',
                'name'           => 'Название',
                'postcode'       => 'Почтовый индекс',
                'priority'       => 'Приоритет',
                'save-btn'       => 'Сохранить источник инвентаря',
                'select-country' => 'Выберите страну',
                'select-state'   => 'Выберите область',
                'settings'       => 'Настройки',
                'source-address' => 'Адрес источника',
                'state'          => 'Область',
                'status'         => 'Статус',
                'street'         => 'Улица',
                'title'          => 'Редактировать источники инвентаря',
            ],

            'create-success'    => 'Источник инвентаря успешно создан',
            'delete-failed'     => 'Ошибка при удалении источников инвентаря',
            'delete-success'    => 'Источники инвентаря успешно удалены',
            'last-delete-error' => 'По крайней мере, один источник инвентаря обязателен.',
            'update-success'    => 'Источники инвентаря успешно обновлены',
        ],

        'taxes' => [
            'categories' => [
                'index' => [
                    'delete-warning' => 'Вы уверены, что хотите удалить?',
                    'tax-category'   => 'Налоговая категория',
                    'title'          => 'Налоговые категории',

                    'datagrid' => [
                        'actions' => 'Действия',
                        'code'    => 'Код',
                        'delete'  => 'Удалить',
                        'edit'    => 'Редактировать',
                        'id'      => 'ID',
                        'name'    => 'Название',
                    ],

                    'create' => [
                        'add-tax-rates' => 'Добавить налоговые ставки',
                        'code'          => 'Код',
                        'description'   => 'Описание',
                        'empty-text'    => 'Налоговые ставки недоступны, создайте новые налоговые ставки.',
                        'general'       => 'Налоговая категория',
                        'name'          => 'Название',
                        'save-btn'      => 'Сохранить налоговую категорию',
                        'select'        => 'Выбрать',
                        'tax-rates'     => 'Налоговые ставки',
                        'title'         => 'Создать налоговую категорию',
                    ],

                    'edit' => [
                        'title' => 'Редактировать налоговые категории',
                    ],

                    'can-not-delete' => 'Категории, назначенные налоговым ставкам, не могут быть удалены.',
                    'create-success' => 'Новая налоговая категория создана',
                    'delete-failed'  => 'Ошибка при удалении налоговой категории',
                    'delete-success' => 'Налоговая категория успешно удалена',
                    'update-success' => 'Налоговая категория успешно обновлена',
                ],
            ],

            'rates'   => [
                'index' => [
                    'button-title' => 'Создать налоговую ставку',
                    'tax-rate'     => 'Налоговая ставка',
                    'title'        => 'Налоговые ставки',

                    'datagrid' => [
                        'country'    => 'Страна',
                        'delete'     => 'Удалить',
                        'edit'       => 'Редактировать',
                        'id'         => 'ID',
                        'identifier' => 'Идентификатор',
                        'state'      => 'Штат',
                        'tax-rate'   => 'Налоговая ставка',
                        'zip-code'   => 'Почтовый индекс',
                        'zip-from'   => 'Почтовый индекс с',
                        'zip-to'     => 'Почтовый индекс по',
                    ],
                ],

                'create' => [
                    'back-btn'       => 'Назад',
                    'country'        => 'Страна',
                    'general'        => 'Общее',
                    'identifier'     => 'Идентификатор',
                    'is-zip'         => 'Включить диапазон почтовых индексов',
                    'save-btn'       => 'Сохранить налоговую ставку',
                    'select-country' => 'Выберите страну',
                    'select-state'   => 'Выберите штат',
                    'settings'       => 'Настройки',
                    'state'          => 'Штат',
                    'tax-rate'       => 'Ставка',
                    'title'          => 'Создать налоговую ставку',
                    'zip-code'       => 'Почтовый индекс',
                    'zip-from'       => 'Почтовый индекс с',
                    'zip-to'         => 'Почтовый индекс по',
                ],

                'edit' => [
                    'back-btn'       => 'Назад',
                    'country'        => 'Страна',
                    'identifier'     => 'Идентификатор',
                    'save-btn'       => 'Сохранить налоговую ставку',
                    'select-country' => 'Выберите страну',
                    'select-state'   => 'Выберите штат',
                    'settings'       => 'Настройки',
                    'state'          => 'Штат',
                    'tax-rate'       => 'Ставка',
                    'title'          => 'Редактировать налоговую ставку',
                    'zip-code'       => 'Почтовый индекс',
                    'zip-from'       => 'Почтовый индекс с',
                    'zip-to'         => 'Почтовый индекс по',
                ],

                'create-success' => 'Налоговая ставка успешно создана.',
                'delete-failed'  => 'Ошибка при удалении налоговой ставки',
                'delete-success' => 'Налоговая ставка успешно удалена',
                'update-success' => 'Настройка налоговой ставки успешно обновлена',
            ],
        ],

        'channels' => [
            'index' => [
                'create-btn'        => 'Создать канал',
                'delete-failed'     => 'Канал удаление не удалось',
                'delete-success'    => 'Канал успешно удален.',
                'last-delete-error' => 'Последнее удаление канала не удалось.',
                'title'             => 'Каналы',

                'datagrid' => [
                    'code'      => 'Код',
                    'delete'    => 'Удалить',
                    'edit'      => 'Редактировать',
                    'host-name' => 'Имя хоста',
                    'id'        => 'ID',
                    'name'      => 'Название',
                ],
            ],

            'create' => [
                'allowed-ips'             => 'Разрешенные IP-адреса',
                'cancel'                  => 'Назад',
                'code'                    => 'Код',
                'create-success'          => 'Канал успешно создан.',
                'currencies'              => 'Валюты',
                'currencies-and-locales'  => 'Валюты и локали',
                'default-currency'        => 'Валюта по умолчанию',
                'default-locale'          => 'Локаль по умолчанию',
                'description'             => 'Описание',
                'design'                  => 'Дизайн',
                'favicon'                 => 'Фавикон',
                'favicon-size'            => 'Разрешение изображения должно быть как 16px X 16px',
                'general'                 => 'Общее',
                'hostname'                => 'Имя хоста',
                'hostname-placeholder'    => 'https://www.example.com (Не добавляйте слеш в конце.)',
                'inventory-sources'       => 'Источники инвентаря',
                'last-delete-error'       => 'Требуется хотя бы один канал.',
                'locales'                 => 'Локали',
                'logo'                    => 'Логотип',
                'logo-size'               => 'Разрешение изображения должно быть как 192px X 50px',
                'maintenance-mode-text'   => 'Сообщение',
                'name'                    => 'Название',
                'root-category'           => 'Корневая категория',
                'save-btn'                => 'Сохранить канал',
                'select-default-currency' => 'Выберите валюту по умолчанию',
                'select-default-locale'   => 'Выберите язык по умолчанию',
                'select-root-category'    => 'Выберите корневую категорию',
                'select-theme'            => 'Выберите тему',
                'seo'                     => 'SEO домашней страницы',
                'seo-description'         => 'Мета-описание',
                'seo-keywords'            => 'Мета-ключевые слова',
                'seo-title'               => 'Мета-заголовок',
                'settings'                => 'Настройки',
                'status'                  => 'Статус',
                'theme'                   => 'Тема',
                'title'                   => 'Создать канал',
            ],

            'edit' => [
                'allowed-ips'            => 'Разрешенные IP-адреса',
                'back-btn'               => 'Назад',
                'code'                   => 'Код',
                'currencies'             => 'Валюты',
                'currencies-and-locales' => 'Валюты и локали',
                'default-currency'       => 'Валюта по умолчанию',
                'default-locale'         => 'Локаль по умолчанию',
                'description'            => 'Описание',
                'design'                 => 'Дизайн',
                'favicon'                => 'Фавикон',
                'favicon-size'           => 'Разрешение изображения должно быть как 16px X 16px',
                'general'                => 'Общее',
                'hostname'               => 'Имя хоста',
                'hostname-placeholder'   => 'https://www.example.com (Не добавляйте слеш в конце.)',
                'inventory-sources'      => 'Источники инвентаря',
                'last-delete-error'      => 'Требуется хотя бы один канал.',
                'locales'                => 'Локали',
                'logo'                   => 'Логотип',
                'logo-size'              => 'Разрешение изображения должно быть как 192px X 50px',
                'maintenance-mode'       => 'Режим обслуживания',
                'maintenance-mode-text'  => 'Сообщение',
                'name'                   => 'Название',
                'root-category'          => 'Корневая категория',
                'save-btn'               => 'Сохранить канал',
                'seo'                    => 'SEO домашней страницы',
                'seo-description'        => 'Мета-описание',
                'seo-keywords'           => 'Мета-ключевые слова',
                'seo-title'              => 'Мета-заголовок',
                'status'                 => 'Статус',
                'theme'                  => 'Тема',
                'title'                  => 'Редактировать канал',
                'update-success'         => 'Канал успешно обновлен.',
            ],
        ],

        'users' => [
            'index' => [
                'admin' => 'Админ',
                'title' => 'Пользователи',
                'user'  => 'Пользователь',

                'create' => [
                    'confirm-password'  => 'Подтвердите пароль',
                    'email'             => 'Электронная почта',
                    'name'              => 'Имя',
                    'password'          => 'Пароль',
                    'role'              => 'Роль',
                    'save-btn'          => 'Сохранить пользователя',
                    'status'            => 'Статус',
                    'title'             => 'Создать пользователя',
                    'upload-image-info' => 'Загрузите изображение профиля (110px X 110px) в формате PNG или JPG',
                ],

                'datagrid' => [
                    'actions'  => 'Действия',
                    'active'   => 'Активный',
                    'delete'   => 'Удалить',
                    'edit'     => 'Редактировать',
                    'email'    => 'Электронная почта',
                    'id'       => 'ID',
                    'inactive' => 'Неактивный',
                    'name'     => 'Имя',
                    'role'     => 'Роль',
                    'status'   => 'Статус',
                ],

                'edit' => [
                    'title' => 'Редактировать пользователя',
                ],
            ],

            'edit' => [
                'back-btn'         => 'Назад',
                'confirm-password' => 'Подтвердите пароль',
                'email'            => 'Электронная почта',
                'general'          => 'Общее',
                'name'             => 'Имя',
                'password'         => 'Пароль',
                'role'             => 'Роль',
                'save-btn'         => 'Сохранить пользователя',
                'status'           => 'Статус',
                'title'            => 'Редактировать пользователя',
            ],

            'activate-warning'   => 'Ваш аккаунт еще не активирован, пожалуйста, свяжитесь с администратором.',
            'cannot-change'      => 'Нельзя изменить пользователя.',
            'create-success'     => 'Пользователь успешно создан.',
            'delete-failed'      => 'Не удалось удалить пользователя.',
            'delete-success'     => 'Пользователь успешно удален.',
            'delete-warning'     => 'Вы уверены, что хотите выполнить это действие?',
            'incorrect-password' => 'Неверный пароль',
            'last-delete-error'  => 'Не удалось удалить последнего пользователя.',
            'login-error'        => 'Пожалуйста, проверьте ваши учетные данные и попробуйте еще раз.',
            'update-success'     => 'Пользователь успешно обновлен.',
        ],

        'roles' => [
            'index' => [
                'create-btn' => 'Создать роль',
                'title'      => 'Роли',

                'datagrid' => [
                    'custom'          => 'Пользовательские',
                    'all'             => 'Все',
                    'permission-type' => 'Тип разрешения',
                    'name'            => 'Название',
                    'id'              => 'ID',
                    'edit'            => 'Редактировать',
                    'delete'          => 'Удалить',
                ],
            ],

            'create' => [
                'access-control' => 'Контроль доступа',
                'all'            => 'Все',
                'back-btn'       => 'Назад',
                'custom'         => 'Пользовательские',
                'description'    => 'Описание',
                'general'        => 'Общее',
                'name'           => 'Название',
                'permissions'    => 'Разрешения',
                'save-btn'       => 'Сохранить роль',
                'title'          => 'Создать роль',
            ],

            'edit' => [
                'access-control' => 'Контроль доступа',
                'all'            => 'Все',
                'back-btn'       => 'Назад',
                'custom'         => 'Пользовательские',
                'description'    => 'Описание',
                'general'        => 'Общее',
                'name'           => 'Название',
                'permissions'    => 'Разрешения',
                'save-btn'       => 'Сохранить роль',
                'title'          => 'Редактировать роль',
            ],

            'being-used'        => 'Роль уже используется у администратора',
            'create-success'    => 'Роли успешно созданы',
            'delete-failed'     => 'Не удалось удалить роль',
            'delete-success'    => 'Роль успешно удалена',
            'last-delete-error' => 'Последнюю роль нельзя удалить',
            'update-success'    => 'Роль успешно обновлена',
        ],

        'themes' => [
            'index' => [
                'create-btn' => 'Создать Тему',
                'title'      => 'Темы',

                'datagrid' => [
                    'active'        => 'Активно',
                    'channel_name'  => 'Название Канала',
                    'change-status' => 'Изменить статус',
                    'delete'        => 'Удалить',
                    'id'            => 'Идентификатор',
                    'inactive'      => 'Неактивно',
                    'name'          => 'Название',
                    'sort-order'    => 'Порядок Сортировки',
                    'status'        => 'Статус',
                    'theme'         => 'Тема',
                    'type'          => 'Тип',
                    'view'          => 'Просмотр',
                ],
            ],

            'create' => [
                'name'       => 'Название',
                'save-btn'   => 'Сохранить тему',
                'sort-order' => 'Порядок Сортировки',
                'themes'     => 'Темы',
                'title'      => 'Создать Тему',

                'type' => [
                    'category-carousel' => 'Карусель Категорий',
                    'footer-links'      => 'Ссылки в Подвале',
                    'image-carousel'    => 'Карусель изображений',
                    'product-carousel'  => 'Карусель Продуктов',
                    'services-content'  => 'Содержание услуг',
                    'static-content'    => 'Статическое Содержание',
                    'title'             => 'Тип',
                ],
            ],

            'edit' => [
                'active'                        => 'Активно',
                'add-filter-btn'                => 'Добавить Фильтр',
                'add-footer-link-btn'           => 'Добавить Ссылку в Подвале',
                'add-image-btn'                 => 'Добавить Изображение',
                'add-link'                      => 'Добавить Ссылку',
                'asc'                           => 'по возрастанию',
                'back'                          => 'Назад',
                'category-carousel'             => 'Карусель Категорий',
                'category-carousel-description' => 'Показ динамических категорий привлекательным образом с использованием адаптивной карусели категорий.',
                'channels'                      => 'Каналы',
                'column'                        => 'Колонка',
                'create-filter'                 => 'Создать Фильтр',
                'css'                           => 'CSS',
                'delete'                        => 'Удалить',
                'desc'                          => 'по убыванию',
                'edit'                          => 'Редактировать',
                'featured'                      => 'Избранное',
                'filter-title'                  => 'Заголовок',
                'filters'                       => 'Фильтры',
                'footer-link'                   => 'Ссылки в Подвале',
                'footer-link-description'       => 'Переход по ссылкам в подвале для легкого исследования веб-сайта и получения информации.',
                'footer-link-form-title'        => 'Ссылка в Подвале',
                'footer-title'                  => 'Заголовок',
                'general'                       => 'Общее',
                'html'                          => 'HTML',
                'image'                         => 'Изображение',
                'image-size'                    => 'Разрешение изображения должно быть (1920px X 700px)',
                'image-title'                   => 'Заголовок изображения',
                'image-upload-message'          => 'Разрешены только изображения (.jpeg, .jpg, .png, .webp, ..).',
                'inactive'                      => 'Неактивно',
                'key'                           => 'Ключ: :key',
                'key-input'                     => 'Ключ',
                'limit'                         => 'Лимит',
                'link'                          => 'Ссылка',
                'name'                          => 'Название',
                'new'                           => 'Новый',
                'no'                            => 'Нет',
                'parent-id'                     => 'Родительский ID',
                'parent-id-hint'                => 'Вы можете ввести несколько родительских ID в качестве разделенных запятыми значений (например: 12,15,34)',
                'category-id'                   => 'ID категории',
                'preview'                       => 'Просмотр',
                'product-carousel'              => 'Карусель Продуктов',
                'product-carousel-description'  => 'Презентация продуктов элегантным образом с использованием динамической и адаптивной карусели продуктов.',
                'save-btn'                      => 'Сохранить',
                'select'                        => 'Выбрать',
                'slider'                        => 'Слайдер',
                'slider-add-btn'                => 'Добавить Слайдер',
                'slider-description'            => 'Настройка темы, связанная со слайдером.',
                'slider-image'                  => 'Изображение Слайдера',
                'slider-required'               => 'Поле слайдера обязательно для заполнения.',
                'sort'                          => 'Сортировать',
                'sort-order'                    => 'Порядок Сортировки',
                'static-content'                => 'Статическое Содержание',
                'static-content-description'    => 'Улучшите взаимодействие с кратким и информативным статическим содержанием для вашей аудитории.',
                'status'                        => 'Статус',
                'themes'                        => 'Темы',
                'title'                         => 'Редактировать Тему',
                'update-slider'                 => 'Обновить Слайдер',
                'url'                           => 'URL',
                'value'                         => 'Значение: :value',
                'value-input'                   => 'Значение',

                'services-content' => [
                    'add-btn'            => 'Добавить услуги',
                    'channels'           => 'Каналы',
                    'delete'             => 'Удалить',
                    'description'        => 'Описание',
                    'general'            => 'Общее',
                    'name'               => 'Имя',
                    'save-btn'           => 'Сохранить',
                    'service-icon'       => 'Значок услуги',
                    'service-icon-class' => 'Класс значка услуги',
                    'service-info'       => 'Настройка темы, связанной с услугами.',
                    'services'           => 'Услуги',
                    'sort-order'         => 'Порядок сортировки',
                    'status'             => 'Статус',
                    'title'              => 'Заголовок',
                    'update-service'     => 'Обновить услуги',
                ],
                'yes'                           => 'Да',
            ],

            'create-success' => 'Тема успешно создана',
            'delete-success' => 'Тема успешно удалена',
            'update-success' => 'Тема успешно обновлена',
        ],
    ],

    'reporting' => [
        'sales' => [
            'index' => [
                'abandoned-carts'               => 'Заброшенные корзины',
                'abandoned-products'            => 'Заброшенные продукты',
                'abandoned-rate'                => 'Коэффициент заброшенности',
                'abandoned-revenue'             => 'Заброшенная выручка',
                'added-to-cart'                 => 'Добавлено в корзину',
                'added-to-cart-info'            => 'Только :progress посетителей добавили продукты в корзину',
                'all-channels'                  => 'Все каналы',
                'average-order-value-over-time' => 'Средний объем заказа со временем',
                'average-sales'                 => 'Средний объем продаж',
                'count'                         => 'Количество',
                'end-date'                      => 'Дата окончания',
                'id'                            => 'Идентификатор',
                'interval'                      => 'Интервал',
                'name'                          => 'Название',
                'orders'                        => 'Заказы',
                'orders-over-time'              => 'Заказы со временем',
                'payment-method'                => 'Способ оплаты',
                'product-views'                 => 'Просмотры продуктов',
                'product-views-info'            => 'Только :progress посетителей просматривали продукты',
                'purchase-funnel'               => 'Воронка покупки',
                'purchased'                     => 'Куплено',
                'purchased-info'                => 'Только :progress посетителей совершили покупку',
                'refunds'                       => 'Возвраты',
                'refunds-over-time'             => 'Возвраты со временем',
                'sales-over-time'               => 'Продажи со временем',
                'shipping-collected'            => 'Собрано за доставку',
                'shipping-collected-over-time'  => 'Сбор за доставку со временем',
                'start-date'                    => 'Дата начала',
                'tax-collected'                 => 'Собрано налогов',
                'tax-collected-over-time'       => 'Сбор налогов со временем',
                'title'                         => 'Продажи',
                'top-payment-methods'           => 'Лучшие методы оплаты',
                'top-shipping-methods'          => 'Лучшие методы доставки',
                'top-tax-categories'            => 'Лучшие категории налогов',
                'total'                         => 'Итого',
                'total-orders'                  => 'Всего заказов',
                'total-sales'                   => 'Всего продаж',
                'total-visits'                  => 'Всего посещений',
                'total-visits-info'             => 'Всего посетителей в магазине',
                'view-details'                  => 'Посмотреть детали',
            ],
        ],

        'customers' => [
            'index' => [
                'all-channels'                => 'Все каналы',
                'count'                       => 'Количество',
                'customers'                   => 'Клиенты',
                'customers-over-time'         => 'Клиенты со временем',
                'customers-traffic'           => 'Трафик клиентов',
                'customers-with-most-orders'  => 'Клиенты с наибольшим количеством заказов',
                'customers-with-most-reviews' => 'Клиенты с наибольшим количеством отзывов',
                'customers-with-most-sales'   => 'Клиенты с наибольшим количеством продаж',
                'email'                       => 'Электронная почта',
                'end-date'                    => 'Дата окончания',
                'id'                          => 'Идентификатор',
                'interval'                    => 'Интервал',
                'name'                        => 'Название',
                'orders'                      => 'Заказы',
                'reviews'                     => 'Отзывы',
                'start-date'                  => 'Дата начала',
                'title'                       => 'Клиенты',
                'top-customer-groups'         => 'Лучшие группы клиентов',
                'total'                       => 'Итого',
                'total-customers'             => 'Всего клиентов',
                'total-visitors'              => 'Всего посетителей',
                'traffic-over-week'           => 'Трафик на протяжении недели',
                'unique-visitors'             => 'Уникальные посетители',
                'view-details'                => 'Посмотреть детали',
            ],
        ],

        'products' => [
            'index' => [
                'all-channels'                     => 'Все каналы',
                'channel'                          => 'Канал',
                'end-date'                         => 'Дата окончания',
                'id'                               => 'Идентификатор',
                'interval'                         => 'Интервал',
                'last-search-terms'                => 'Последние поисковые запросы',
                'locale'                           => 'Язык',
                'name'                             => 'Название',
                'orders'                           => 'Заказы',
                'price'                            => 'Цена',
                'products-added-over-time'         => 'Продукты, добавленные со временем',
                'products-with-most-reviews'       => 'Продукты с наибольшим количеством отзывов',
                'products-with-most-visits'        => 'Продукты с наибольшим количеством посещений',
                'quantities'                       => 'Количество',
                'quantities-sold-over-time'        => 'Проданные количества со временем',
                'results'                          => 'Результаты',
                'revenue'                          => 'Доход',
                'reviews'                          => 'Отзывы',
                'search-term'                      => 'Поисковый запрос',
                'start-date'                       => 'Дата начала',
                'title'                            => 'Продукты',
                'top-search-terms'                 => 'Самые популярные поисковые запросы',
                'top-selling-products-by-quantity' => 'Самые продаваемые продукты по количеству',
                'top-selling-products-by-revenue'  => 'Самые продаваемые продукты по доходу',
                'total'                            => 'Итого',
                'total-products-added-to-wishlist' => 'Продукты, добавленные в список желаний',
                'total-sold-quantities'            => 'Всего проданных количеств',
                'uses'                             => 'Использование',
                'view-details'                     => 'Посмотреть детали',
                'visits'                           => 'Посетители',
            ],
        ],

        'view' => [
            'all-channels'  => 'All Channels',
            'back-btn'      => 'Назад',
            'day'           => 'День',
            'end-date'      => 'Дата окончания',
            'export-csv'    => 'Экспорт в CSV',
            'export-xls'    => 'Экспорт в XLS',
            'month'         => 'Месяц',
            'not-available' => 'Нет доступных записей.',
            'start-date'    => 'Дата начала',
            'year'          => 'Год',
        ],

        'empty' => [
            'info'  => 'Нет данных для выбранного интервала',
            'title' => 'Данные недоступны',
        ],
    ],

    'configuration' => [
        'index' => [
            'back-btn'                     => 'Назад',
            'delete'                       => 'Удалить',
            'enable-at-least-one-payment'  => 'Включите хотя бы один способ оплаты.',
            'enable-at-least-one-shipping' => 'Включите хотя бы один способ доставки.',
            'no-result-found'              => 'Результат не найден',
            'save-btn'                     => 'Сохранить настройки',
            'save-message'                 => 'Настройки успешно сохранены',
            'search'                       => 'Поиск',
            'select-country'               => 'Выберите страну',
            'select-state'                 => 'Выберите регион',
            'title'                        => 'Настройки',

            'general' => [
                'info'  => 'Настройте параметры единиц измерения.',
                'title' => 'Общие настройки',

                'general' => [
                    'info'  => 'Установите параметры единиц измерения и включите или отключите хлебные крошки.',
                    'title' => 'General',

                    'unit-options' => [
                        'info'        => 'Set units options.',
                        'title'       => 'Unit Options',
                        'title-info'  => 'Настройте вес в фунтах (lbs) или килограммах (kgs).',
                        'weight-unit' => 'Weight Unit',
                    ],

                    'breadcrumbs' => [
                        'shop'       => 'Shop Breadcrumbs',
                        'title'      => 'Breadcrumbs',
                        'title-info' => 'Enable or disable breadcrumbs navigation in the shop.',
                    ],
                ],

                'content' => [
                    'info'  => 'Установите заголовок предложения в шапке и пользовательские скрипты.',
                    'title' => 'Содержание',

                    'header-offer' => [
                        'title'             => 'Заголовок предложения в шапке',
                        'title-info'        => 'Настройте заголовок предложения в шапке с заголовком предложения, заголовком перенаправления и ссылкой перенаправления.',
                        'offer-title'       => 'Заголовок предложения',
                        'redirection-title' => 'Заголовок перенаправления',
                        'redirection-link'  => 'Ссылка перенаправления',
                    ],

                    'speculation-rules' => [
                        'enable-speculation' => 'Включить правила спекуляции',
                        'info'               => 'Настройте параметры для включения или отключения автоматической логики спекуляции.',
                        'title'              => 'Правила спекуляции',

                        'prerender' => [
                            'conservative'           => 'Консервативный',
                            'eager'                  => 'Активный',
                            'eagerness'              => 'Уровень активности prerender',
                            'eagerness-info'         => 'Контролирует, насколько агрессивно применяются правила спекуляции. Опции: активный (максимум), умеренный (по умолчанию), консервативный (низкий).',
                            'enabled'                => 'Включить правила спекуляции prerender',
                            'ignore-url-params'      => 'Игнорировать параметры URL prerender',
                            'ignore-url-params-info' => 'Укажите параметры URL для игнорирования в правилах спекуляции. Используйте символ | для разделения нескольких параметров.',
                            'ignore-urls'            => 'Игнорировать URL prerender',
                            'ignore-urls-info'       => 'Введите URL-адреса, исключаемые из логики спекуляции. Разделяйте несколько URL символом |.',
                            'info'                   => 'Установить статус правил спекуляции.',
                            'moderate'               => 'Умеренный',
                        ],

                        'prefetch' => [
                            'conservative'           => 'Консервативный',
                            'eager'                  => 'Активный',
                            'eagerness'              => 'Уровень активности prefetch',
                            'eagerness-info'         => 'Контролирует, насколько агрессивно применяются правила спекуляции. Опции: активный (максимум), умеренный (по умолчанию), консервативный (низкий).',
                            'enabled'                => 'Включить правила спекуляции prefetch',
                            'ignore-url-params'      => 'Игнорировать параметры URL prefetch',
                            'ignore-url-params-info' => 'Укажите параметры URL для игнорирования в правилах спекуляции. Используйте символ | для разделения нескольких параметров.',
                            'ignore-urls'            => 'Игнорировать URL prefetch',
                            'ignore-urls-info'       => 'Введите URL-адреса, исключаемые из логики спекуляции. Разделяйте несколько URL символом |.',
                            'info'                   => 'Установить статус правил спекуляции.',
                            'moderate'               => 'Умеренный',
                        ],
                    ],

                    'custom-scripts' => [
                        'custom-css'        => 'Пользовательский CSS',
                        'custom-javascript' => 'Пользовательский JavaScript',
                        'title'             => 'Пользовательские скрипты',
                        'title-info'        => 'Пользовательские скрипты - это индивидуальные фрагменты кода, созданные для добавления определенных функций или возможностей в программное обеспечение, уникально расширяющие его возможности.',
                    ],
                ],

                'design' => [
                    'info'  => 'Установите логотип и значок фавикона для панели администратора.',
                    'title' => 'Дизайн',

                    'admin-logo' => [
                        'favicon'    => 'Фавикон',
                        'logo-image' => 'Изображение логотипа',
                        'title'      => 'Логотип администратора',
                        'title-info' => 'Настройте изображения логотипа и фавикона для фронтенда вашего веб-сайта для лучшего брендинга и узнаваемости.',
                    ],

                    'menu-category' => [
                        'default'         => 'Меню по умолчанию',
                        'info'            => 'Этот параметр управляет отображением категорий в меню заголовка. Вы можете выбрать отображение только родительских категорий или всех вложенных категорий.',
                        'preview-default' => 'Предпросмотр меню по умолчанию',
                        'preview-sidebar' => 'Предпросмотр бокового меню',
                        'sidebar'         => 'Боковое меню',
                        'title'           => 'Просмотр категории меню',
                    ],
                ],

                'magic-ai' => [
                    'info'  => 'Установите параметры Magic AI и разрешите некоторые опции для автоматизации создания контента.',
                    'title' => 'Magic AI',

                    'settings' => [
                        'api-key'        => 'API-ключ',
                        'enabled'        => 'Включено',
                        'llm-api-domain' => 'Домен LLM API',
                        'organization'   => 'Организация',
                        'title'          => 'Общие настройки',
                        'title-info'     => 'Улучшите свой опыт работы с функцией Magic AI, введя свой эксклюзивный API-ключ и указав соответствующую организацию для безупречной интеграции. Получите полный контроль над учетными данными OpenAI и настройте параметры в соответствии с вашими конкретными потребностями.',
                    ],

                    'content-generation' => [
                        'category-description-prompt'      => 'Подсказка для описания категории',
                        'cms-page-content-prompt'          => 'Подсказка для содержимого страницы CMS',
                        'enabled'                          => 'Включено',
                        'product-description-prompt'       => 'Подсказка для описания товара',
                        'product-short-description-prompt' => 'Подсказка для краткого описания товара',
                        'title'                            => 'Генерация контента',
                        'title-info'                       => 'Эта функция позволяет использовать Magic AI для каждого редактора WYSIWYG, где вы хотите управлять контентом с помощью искусственного интеллекта.<br/><br/>При включении перейдите в любой редактор для генерации контента.',
                    ],

                    'image-generation' => [
                        'enabled'    => 'Включено',
                        'title'      => 'Генерация изображений',
                        'title-info' => 'Эта функция позволяет использовать Magic AI для каждой загрузки изображений, где вы хотите генерировать изображения с помощью DALL-E.<br/><br/>При включении перейдите к любой загрузке изображений для генерации изображения.',
                    ],

                    'review-translation' => [
                        'deepseek-r1-8b'    => 'DeepSeek R1 (8b)',
                        'enabled'           => 'Включено',
                        'gemini-2-0-flash'  => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'       => 'OpenAI gpt-4 Turbo',
                        'gpt-4o'            => 'OpenAI gpt-4o',
                        'gpt-4o-mini'       => 'OpenAI gpt-4o mini',
                        'llama-groq'        => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'       => 'Llama 3.1 (8B)',
                        'llama3-2-1b'       => 'Llama 3.2 (1B)',
                        'llama3-2-3b'       => 'Llama 3.2 (3B)',
                        'llama3-8b'         => 'Llama 3 (8B)',
                        'llava-7b'          => 'Llava (7b)',
                        'mistral-7b'        => 'Mistral (7b)',
                        'model'             => 'Модель',
                        'orca-mini'         => 'Orca Mini',
                        'phi3-5'            => 'Phi 3.5',
                        'qwen2-5-0-5b'      => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'      => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'       => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'        => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'        => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'    => 'Starling-lm (7b)',
                        'title'             => 'Перевод отзывов',
                        'title-info'        => 'Предоставьте клиенту или посетителю возможность перевести отзыв клиента на английский язык.<br/><br/>Когда включено, перейдите к отзыву, и вы найдете кнопку «Перевести на английский», если отзыв на другом языке, кроме английского.',
                        'vicuna-13b'        => 'Vicuna (13b)',
                        'vicuna-7b'         => 'Vicuna (7b)',
                    ],

                    'checkout-message' => [
                        'deepseek-r1-8b'    => 'DeepSeek R1 (8b)',
                        'enabled'           => 'Включено',
                        'gemini-2-0-flash'  => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'       => 'OpenAI gpt 4 Turbo',
                        'gpt-4o'            => 'OpenAI gpt-4o',
                        'gpt-4o-mini'       => 'OpenAI gpt-4o mini',
                        'llama-groq'        => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'       => 'Llama 3.1 (8B)',
                        'llama3-2-1b'       => 'Llama 3.2 (1B)',
                        'llama3-2-3b'       => 'Llama 3.2 (3B)',
                        'llama3-8b'         => 'Llama 3 (8B)',
                        'llava-7b'          => 'Llava (7b)',
                        'mistral-7b'        => 'Mistral (7b)',
                        'model'             => 'Модель',
                        'orca-mini'         => 'Orca Mini',
                        'phi3-5'            => 'Phi 3.5',
                        'prompt'            => 'Подсказка',
                        'qwen2-5-0-5b'      => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'      => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'       => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'        => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'        => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'    => 'Starling-lm (7b)',
                        'title'             => 'Персонализированное сообщение при оформлении заказа',
                        'title-info'        => 'Создайте персонализированное сообщение при оформлении заказа для клиентов на странице благодарности, адаптируя контент к индивидуальным предпочтениям и улучшая общий опыт после покупки.',
                        'vicuna'            => 'Vicuna',
                        'vicuna-13b'        => 'Vicuna (13b)',
                        'vicuna-7b'         => 'Vicuna (7b)',
                    ],
                ],

                'sitemap' => [
                    'info'  => 'Настройка параметров карты сайта.',
                    'title' => 'Карта сайта',

                    'settings' => [
                        'enabled' => 'Включено',
                        'info'    => 'Включите или отключите карту сайта для вашего веб-сайта, чтобы улучшить поисковую оптимизацию и повысить удобство использования.',
                        'title'   => 'Настройки',
                    ],

                    'file-limits' => [
                        'info'             => 'Настройка параметров ограничения файлов.',
                        'max-file-size'    => 'Максимальный размер файла',
                        'max-url-per-file' => 'Максимальное количество URL на файл',
                        'title'            => 'Ограничения файлов',
                    ],
                ],
            ],

            'gdpr' => [
                'title' => 'GDPR',
                'info'  => 'Настройки соответствия GDPR',

                'settings' => [
                    'title'   => 'Настройки соответствия GDPR',
                    'info'    => 'Управляйте настройками соответствия GDPR, включая политику конфиденциальности. Включайте или отключайте функции GDPR по мере необходимости.',
                    'enabled' => 'Включить GDPR',
                ],

                'agreement' => [
                    'title'          => 'Согласие GDPR',
                    'info'           => 'Управляйте согласием клиентов в соответствии с правилами GDPR. Включите обязательное согласие на сбор и обработку данных.',
                    'enable'         => 'Включить согласие клиента',
                    'checkbox-label' => 'Метка флажка согласия',
                    'content'        => 'Содержание согласия',
                ],

                'cookie' => [
                    'bottom-left'  => 'Нижний левый',
                    'bottom-right' => 'Нижний правый',
                    'center'       => 'Центр',
                    'description'  => 'Описание',
                    'enable'       => 'Включить уведомление о файлах cookie',
                    'identifier'   => 'ID статического блока',
                    'info'         => 'Настройте параметры согласия на использование файлов cookie, чтобы информировать пользователей о сборе данных и соблюдении политики конфиденциальности.',
                    'position'     => 'Позиция блока файлов cookie',
                    'title'        => 'Настройки уведомления о файлах cookie',
                    'top-left'     => 'Верхний левый',
                    'top-right'    => 'Верхний правый',
                ],

                'cookie-consent' => [
                    'title'                  => 'Управление настройками файлов cookie',
                    'info'                   => 'Выберите предпочтительные настройки файлов cookie для управления использованием данных. Настройте согласия для различных типов файлов cookie.',
                    'strictly-necessary'     => 'Строго необходимые',
                    'basic-interaction'      => 'Основное взаимодействие и функциональность',
                    'experience-enhancement' => 'Улучшение опыта',
                    'measurement'            => 'Измерение',
                    'targeting-advertising'  => 'Таргетинг и реклама',
                ],
            ],

            'catalog' => [
                'info'  => 'Каталог',
                'title' => 'Каталог',

                'products' => [
                    'info'  => 'Страница просмотра товара, страница просмотра корзины, витрина магазина, отзывы и социальный обмен атрибутами.',
                    'title' => 'Товары',

                    'settings' => [
                        'compare-options'     => 'Опции сравнения',
                        'image-search-option' => 'Опция поиска изображений',
                        'title'               => 'Настройки',
                        'title-info'          => 'Настройки относятся к настраиваемым параметрам, которые управляют поведением системы, приложения или устройства, адаптированным к предпочтениям и требованиям пользователя.',
                        'wishlist-options'    => 'Опции списка желаний',
                    ],

                    'search' => [
                        'admin-mode'            => 'Режим поиска администратора',
                        'admin-mode-info'       => 'Мега-поиск, таблица данных и другие функции поиска в панели администратора будут основаны на выбранном поисковом движке.',
                        'database'              => 'База данных',
                        'elastic'               => 'Elastic Search',
                        'max-query-length'      => 'Максимальная длина запроса',
                        'max-query-length-info' => 'Установите максимальную длину запроса для поисковых запросов.',
                        'min-query-length'      => 'Минимальная длина запроса',
                        'min-query-length-info' => 'Установите минимальную длину запроса для поисковых запросов.',
                        'search-engine'         => 'Поисковый движок',
                        'storefront-mode'       => 'Режим поиска витрины',
                        'storefront-mode-info'  => 'Функциональность поиска на витрине будет основана на выбранном поисковом движке, включая страницу категорий, страницу поиска и другие функции поиска.',
                        'title'                 => 'Поиск',
                        'title-info'            => 'Для настройки поискового движка для поиска товаров вы можете выбрать между базой данных и Elasticsearch в зависимости от ваших требований. Если у вас большое количество товаров, рекомендуется использовать Elasticsearch.',
                    ],

                    'guest-checkout' => [
                        'allow-guest-checkout'      => 'Разрешить оформление заказа гостем',
                        'allow-guest-checkout-hint' => 'Подсказка: Если включено, эта опция может быть настроена для каждого товара отдельно.',
                        'title'                     => 'Оформление заказа гостем',
                        'title-info'                => 'Оформление заказа гостем позволяет покупателям покупать товары без создания учетной записи, упрощая процесс покупки для удобства и более быстрых транзакций.',
                    ],

                    'product-view-page' => [
                        'allow-no-of-related-products'  => 'Разрешенное количество связанных товаров',
                        'allow-no-of-up-sells-products' => 'Разрешенное количество товаров для продажи',
                        'title'                         => 'Настройка страницы просмотра товара',
                        'title-info'                    => 'Настройка страницы просмотра товара включает настройку макета и элементов на странице отображения товара, улучшая пользовательский опыт и представление информации.',
                    ],

                    'cart-view-page' => [
                        'allow-no-of-cross-sells-products' => 'Разрешенное количество товаров для перекрестных продаж',
                        'title'                            => 'Настройка страницы просмотра корзины',
                        'title-info'                       => 'Настройка страницы просмотра корзины включает расположение товаров, деталей и опций на странице корзины покупок, улучшая взаимодействие пользователя и процесс покупки.',
                    ],

                    'storefront' => [
                        'buy-now-button-display' => 'Разрешить покупателям непосредственно покупать товары',
                        'cheapest-first'         => 'Сначала дешевые',
                        'comma-separated'        => 'Разделенные запятой',
                        'default-list-mode'      => 'Режим списка по умолчанию',
                        'expensive-first'        => 'Сначала дорогие',
                        'from-a-z'               => 'От А до Я',
                        'from-z-a'               => 'От Я до А',
                        'grid'                   => 'Сетка',
                        'latest-first'           => 'Сначала новые',
                        'list'                   => 'Список',
                        'oldest-first'           => 'Сначала старые',
                        'products-per-page'      => 'Товаров на странице',
                        'sort-by'                => 'Сортировать по',
                        'title'                  => 'Витрина',
                        'title-info'             => 'Витрина - это интерфейс для клиентов онлайн-магазина, демонстрирующий товары, категории и навигацию для безупречного опыта покупок.',
                    ],

                    'small-image' => [
                        'height'      => 'Высота',
                        'placeholder' => 'Заполнитель для маленького изображения',
                        'title'       => 'Маленькое изображение',
                        'title-info'  => 'Маленькое изображение представляет собой изображение с умеренным размером, которое обеспечивает баланс между деталями и экранным пространством, обычно используется для визуальных элементов.',
                        'width'       => 'Ширина',
                    ],

                    'medium-image' => [
                        'height'      => 'Высота',
                        'placeholder' => 'Заполнитель для среднего изображения',
                        'title'       => 'Среднее изображение',
                        'title-info'  => 'Среднее изображение относится к изображению с умеренным размером, которое обеспечивает баланс между деталями и экранным пространством, обычно используется для визуальных элементов.',
                        'width'       => 'Ширина',
                    ],

                    'large-image' => [
                        'height'      => 'Высота',
                        'placeholder' => 'Заполнитель для большого изображения',
                        'title'       => 'Большое изображение',
                        'title-info'  => 'Большое изображение представляет собой изображение с высоким разрешением, обеспечивающее улучшенные детали и визуальный эффект, часто используется для демонстрации товаров или графики.',
                        'width'       => 'Ширина',
                    ],

                    'review' => [
                        'allow-customer-review'   => 'Разрешить отзывы клиентов',
                        'allow-guest-review'      => 'Разрешить отзывы гостей',
                        'censoring-reviewer-name' => 'Цензура имени рецензента',
                        'display-review-count'    => 'Отображение количества отзывов для оценок.',
                        'display-star-count'      => 'Отображение количества звезд в рейтингах.',
                        'summary'                 => 'Краткое содержание',
                        'title'                   => 'Отзывы',
                        'title-info'              => 'Оценка или оценка чего-либо, часто включающая мнения и обратную связь.',
                    ],

                    'attribute' => [
                        'file-upload-size'  => 'Разрешенный размер загружаемого файла (в Кб)',
                        'image-upload-size' => 'Разрешенный размер загружаемого изображения (в Кб)',
                        'title'             => 'Атрибут',
                        'title-info'        => 'Характеристика или свойство, определяющее объект и влияющее на его поведение, внешний вид или функцию.',
                    ],

                    'social-share' => [
                        'title-info'                  => 'Настройте параметры социального обмена, чтобы разрешить обмен продуктами в Instagram, Twitter, WhatsApp, Facebook, Pinterest, LinkedIn и по электронной почте.',
                        'title'                       => 'Поделиться в социальных сетях',
                        'share-message'               => 'Сообщение для публикации',
                        'share'                       => 'Поделиться',
                        'enable-social-share'         => 'Включить поделиться в социальных сетях?',
                        'enable-share-whatsapp-info'  => 'Ссылка для обмена в WhatsApp будет отображаться только на мобильных устройствах.',
                        'enable-share-whatsapp'       => 'Включить поделиться в WhatsApp?',
                        'enable-share-twitter'        => 'Включить поделиться в Twitter?',
                        'enable-share-pinterest'      => 'Включить поделиться в Pinterest?',
                        'enable-share-linkedin'       => 'Включить поделиться в Linkedin?',
                        'enable-share-facebook'       => 'Включить поделиться в Facebook?',
                        'enable-share-email'          => 'Включить поделиться по электронной почте?',
                    ],
                ],

                'rich-snippets' => [
                    'info'  => 'Настройте продукты и категории.',
                    'title' => 'Rich Snippets',

                    'products' => [
                        'enable'          => 'Включить',
                        'show-categories' => 'Показывать категории',
                        'show-images'     => 'Показывать изображения',
                        'show-offers'     => 'Показывать предложения',
                        'show-ratings'    => 'Показывать рейтинги',
                        'show-reviews'    => 'Показывать отзывы',
                        'show-sku'        => 'Показывать артикул',
                        'show-weight'     => 'Показывать вес',
                        'title'           => 'Продукты',
                        'title-info'      => 'Настройка параметров продукта, включая SKU, вес, категории, изображения, отзывы, рейтинги, предложения и т. д.',
                    ],

                    'categories' => [
                        'enable'                  => 'Включить',
                        'show-search-input-field' => 'Показывать поле поиска',
                        'title'                   => 'Категории',
                        'title-info'              => '«Категории» относятся к группам или классификациям, которые помогают организовывать и группировать похожие товары или предметы для более удобного просмотра и навигации.',
                    ],
                ],

                'inventory' => [
                    'title'      => 'Инвентаризация',
                    'title-info' => 'Настройте параметры инвентаризации для разрешения предзаказов и определения порога нехватки товара.',

                    'product-stock-options' => [
                        'allow-back-orders'       => 'Разрешить предзаказы',
                        'max-qty-allowed-in-cart' => 'Максимальное количество товаров в корзине',
                        'min-qty-allowed-in-cart' => 'Минимальное количество товаров в корзине',
                        'out-of-stock-threshold'  => 'Порог нехватки товара',
                        'title'                   => 'Опции запаса товара',
                        'info'                    => 'Настройте параметры запасов продукции для возможности отложенных заказов, установите минимальные и максимальные количество в корзине, и определите пороги отсутствия товара.',
                    ],
                ],
            ],

            'customer' => [
                'info'  => 'Клиент',
                'title' => 'Клиент',

                'address' => [
                    'info'  => 'Установите страну, регион, почтовый индекс и строки в адресе улицы.',
                    'title' => 'Адрес',

                    'requirements' => [
                        'city'       => 'Город',
                        'country'    => 'Страна',
                        'state'      => 'Регион',
                        'title'      => 'Требования',
                        'title-info' => 'Требования - это условия, характеристики или спецификации, необходимые для успешного выполнения, достижения или выполнения чего-либо.',
                        'zip'        => 'Почтовый индекс',
                    ],

                    'information' => [
                        'street-lines' => 'Строки в адресе улицы',
                        'title'        => 'Информация',
                        'title-info'   => '«Строки в адресе улицы» относятся к отдельным сегментам адреса, часто разделенным запятыми, предоставляющим информацию о местоположении, такую ​​как номер дома, улица, город и т. д.',
                    ],
                ],

                'captcha' => [
                    'info'  => 'Установите ключ сайта, секретный ключ и статус.',
                    'title' => 'Google Капча',

                    'credentials' => [
                        'secret-key' => 'Секретный ключ',
                        'site-key'   => 'Ключ сайта',
                        'status'     => 'Статус',
                        'title'      => 'Учетные данные',
                        'title-info' => '"Sitemap: Карта сайта для поисковых систем. Секретный ключ: Безопасный код для шифрования данных, аутентификации или защиты доступа к API."',
                    ],

                    'validations' => [
                        'captcha'  => 'Что-то пошло не так! Пожалуйста, попробуйте еще раз.',
                        'required' => 'Пожалуйста, выберите CAPTCHA',
                    ],
                ],

                'settings' => [
                    'settings-info' => 'Настройте список желаний, перенаправление при входе, подписки на рассылку, опцию группы по умолчанию, проверку электронной почты и социальный вход.',
                    'title'         => 'Настройки',

                    'login-as-customer' => [
                        'allow-option' => 'Разрешить вход от имени клиента',
                        'title'        => 'Вход от имени клиента',
                        'title-info'   => 'Включите функциональность "Вход от имени клиента".',
                    ],

                    'wishlist' => [
                        'allow-option' => 'Разрешить опцию "Список желаний"',
                        'title'        => 'Список желаний',
                        'title-info'   => 'Включите или отключите опцию "Список желаний".',
                    ],

                    'login-options' => [
                        'account'          => 'Аккаунт',
                        'home'             => 'Главная',
                        'redirect-to-page' => 'Перенаправить клиента на выбранную страницу',
                        'title'            => 'Опции входа',
                        'title-info'       => 'Настройте опции входа, чтобы определить страницу перенаправления для клиентов после входа.',
                    ],

                    'create-new-account-option' => [
                        'news-letter'      => 'Разрешить подписку на рассылку',
                        'news-letter-info' => 'Включите опцию подписки на рассылку на странице регистрации.',
                        'title'            => 'Опции создания нового аккаунта',
                        'title-info'       => 'Установите параметры для новых аккаунтов, включая назначение группы клиентов по умолчанию и включение опции подписки на рассылку при регистрации.',

                        'default-group' => [
                            'general'    => 'Общая',
                            'guest'      => 'Гость',
                            'title'      => 'Группа по умолчанию',
                            'title-info' => 'Назначьте определенную группу клиентов в качестве группы по умолчанию для новых клиентов.',
                            'wholesale'  => 'Оптовая продажа',
                        ],
                    ],

                    'newsletter' => [
                        'subscription' => 'Разрешить подписку на рассылку',
                        'title'        => 'Подписка на рассылку',
                        'title-info'   => '"Информация о рассылке" содержит обновления, предложения или регулярно распространяемый контент через электронную почту для подписчиков, информируя их и поддерживая их вовлеченность.',
                    ],

                    'email' => [
                        'email-verification' => 'Разрешить проверку электронной почты',
                        'title'              => 'Проверка электронной почты',
                        'title-info'         => '"Проверка электронной почты" подтверждает подлинность адреса электронной почты, часто путем отправки ссылки для подтверждения, улучшая безопасность учетной записи и надежность коммуникации.',
                    ],

                    'social-login' => [
                        'title' => 'Социальный вход',
                        'info'  => '"Социальный вход" позволяет пользователям получить доступ к сайту с помощью их аккаунтов в социальных сетях, упрощая процессы регистрации и входа.',

                        'google' => [
                            'enable-google' => 'Включить Google',

                            'client-id' => [
                                'title'      => 'ID клиента',
                                'title-info' => 'Уникальный идентификатор, предоставленный Google при создании вашего OAuth-приложения.',
                            ],

                            'client-secret' => [
                                'title'      => 'Секрет клиента',
                                'title-info' => 'Секретный ключ, связанный с вашим клиентом OAuth Google. Храните его в безопасности.',
                            ],

                            'redirect' => [
                                'title'      => 'URL перенаправления',
                                'title-info' => 'URL-адрес для обратного вызова, на который пользователи будут перенаправлены после аутентификации через Google. Он должен соответствовать URL, настроенному в вашей консоли Google.',
                            ],
                        ],

                        'facebook' => [
                            'enable-facebook' => 'Включить Facebook',

                            'client-id' => [
                                'title'      => 'ID клиента',
                                'title-info' => 'ID приложения, предоставленный Facebook при создании приложения в консоли разработчика Facebook.',
                            ],

                            'client-secret' => [
                                'title'      => 'Секрет клиента',
                                'title-info' => 'Секретный ключ, связанный с вашим приложением Facebook. Храните его в безопасности и в приватности.',
                            ],

                            'redirect' => [
                                'title'      => 'URL перенаправления',
                                'title-info' => 'URL-адрес для обратного вызова, на который пользователи будут перенаправлены после аутентификации через Facebook. Он должен соответствовать URL, настроенному в настройках вашего приложения на Facebook.',
                            ],
                        ],

                        'github' => [
                            'enable-github' => 'Включить GitHub',

                            'client-id' => [
                                'title'      => 'ID клиента',
                                'title-info' => 'Уникальный идентификатор, предоставленный GitHub при создании вашего OAuth-приложения.',
                            ],

                            'client-secret' => [
                                'title'      => 'Секрет клиента',
                                'title-info' => 'Секретный ключ, связанный с вашим клиентом OAuth GitHub. Храните его в безопасности.',
                            ],

                            'redirect' => [
                                'title'      => 'URL перенаправления',
                                'title-info' => 'URL-адрес для обратного вызова, на который пользователи будут перенаправлены после аутентификации через GitHub. Он должен соответствовать URL, настроенному в вашей консоли GitHub.',
                            ],
                        ],

                        'linkedin' => [
                            'enable-linkedin' => 'Включить LinkedIn',

                            'client-id' => [
                                'title'      => 'ID клиента',
                                'title-info' => 'Уникальный идентификатор, предоставленный LinkedIn при создании вашего OAuth-приложения.',
                            ],

                            'client-secret' => [
                                'title'      => 'Секрет клиента',
                                'title-info' => 'Секретный ключ, связанный с вашим клиентом OAuth LinkedIn. Храните его в безопасности.',
                            ],

                            'redirect' => [
                                'title'      => 'URL перенаправления',
                                'title-info' => 'URL-адрес для обратного вызова, на который пользователи будут перенаправлены после аутентификации через LinkedIn. Он должен соответствовать URL, настроенному в вашей консоли LinkedIn.',
                            ],
                        ],

                        'twitter' => [
                            'enable-twitter' => 'Включить Twitter',

                            'client-id' => [
                                'title'      => 'ID клиента',
                                'title-info' => 'Уникальный идентификатор, предоставленный Twitter при создании вашего OAuth-приложения.',
                            ],

                            'client-secret' => [
                                'title'      => 'Секрет клиента',
                                'title-info' => 'Секретный ключ, связанный с вашим клиентом OAuth Twitter. Храните его в безопасности.',
                            ],

                            'redirect' => [
                                'title'      => 'URL перенаправления',
                                'title-info' => 'URL-адрес для обратного вызова, на который пользователи будут перенаправлены после аутентификации через Twitter. Он должен соответствовать URL, настроенному в вашей консоли Twitter.',
                            ],
                        ],
                    ],
                ],
            ],

            'email' => [
                'info'  => 'Электронная почта',
                'title' => 'Электронная почта',

                'email-settings' => [
                    'admin-email'           => 'Email администратора',
                    'admin-email-tip'       => 'Адрес электронной почты администратора для получения писем',
                    'admin-name'            => 'Имя администратора',
                    'admin-name-tip'        => 'Это имя будет отображаться во всех письмах администратора',
                    'admin-page-limit'      => 'Количество элементов на странице (администратор)',
                    'contact-email'         => 'Email для контакта',
                    'contact-email-tip'     => 'Адрес электронной почты, который будет отображаться внизу ваших писем',
                    'contact-name'          => 'Имя для контакта',
                    'contact-name-tip'      => 'Это имя будет отображаться внизу ваших писем',
                    'email-sender-name'     => 'Имя отправителя писем',
                    'email-sender-name-tip' => 'Это имя будет отображаться во входящих письмах клиентов',
                    'info'                  => 'Установите имя отправителя писем, адрес электронной почты магазина, имя администратора и адрес электронной почты администратора.',
                    'shop-email-from'       => 'Адрес электронной почты магазина',
                    'shop-email-from-tip'   => 'Адрес электронной почты этого канала для отправки писем вашим клиентам',
                    'title'                 => 'Настройки электронной почты',
                ],

                'notifications' => [
                    'cancel-order'                                     => 'Отправить уведомление клиенту после отмены заказа',
                    'cancel-order-mail-to-admin'                       => 'Отправить уведомление администратору после отмены заказа',
                    'customer'                                         => 'Отправить учетные данные клиента после регистрации',
                    'customer-registration-confirmation-mail-to-admin' => 'Отправить подтверждение администратору после регистрации клиента',
                    'info'                                             => 'Настройте получение писем для подтверждения учетной записи, подтверждения заказов, обновлений по счетам, возвратам, отправкам и отменам заказов.',
                    'new-inventory-source'                             => 'Отправить уведомление источнику инвентаря после создания отправки',
                    'new-invoice'                                      => 'Отправить уведомление клиенту после создания нового счета',
                    'new-invoice-mail-to-admin'                        => 'Отправить уведомление администратору после создания нового счета',
                    'new-order'                                        => 'Отправить подтверждение клиенту после размещения нового заказа',
                    'new-order-mail-to-admin'                          => 'Отправить подтверждение администратору после размещения нового заказа',
                    'new-refund'                                       => 'Отправить уведомление клиенту после создания возврата',
                    'new-refund-mail-to-admin'                         => 'Отправить уведомление администратору после создания нового возврата',
                    'new-shipment'                                     => 'Отправить уведомление клиенту после создания отправки',
                    'new-shipment-mail-to-admin'                       => 'Отправить уведомление администратору после создания новой отправки',
                    'registration'                                     => 'Отправить подтверждение после регистрации клиента',
                    'title'                                            => 'Уведомления',
                    'verification'                                     => 'Отправить письмо для подтверждения после регистрации клиента',
                ],
            ],

            'sales' => [
                'info'  => 'Продажи',
                'title' => 'Продажи',

                'shipping-setting' => [
                    'info'  => 'Настройте параметры доставки, включая страну, штат, город, адрес улицы, почтовый индекс, название магазина, номер НДС, контактный номер и банковские реквизиты.',
                    'title' => 'Настройки доставки',

                    'origin' => [
                        'bank-details'   => 'Банковские реквизиты',
                        'city'           => 'Город',
                        'contact-number' => 'Контактный номер',
                        'country'        => 'Страна',
                        'state'          => 'Штат',
                        'store-name'     => 'Название магазина',
                        'street-address' => 'Адрес улицы',
                        'title'          => 'Место отправления',
                        'title-info'     => 'Место отправления относится к месту, где товары или продукты происходят до транспортировки в пункт назначения.',
                        'vat-number'     => 'Номер НДС',
                        'zip'            => 'Почтовый индекс',
                    ],
                ],

                'shipping-methods' => [
                    'info'  => 'Настройте методы доставки, включая бесплатную доставку, фиксированную ставку и дополнительные опции при необходимости.',
                    'title' => 'Методы доставки',

                    'free-shipping' => [
                        'description' => 'Описание',
                        'page-title'  => 'Бесплатная доставка',
                        'status'      => 'Статус',
                        'title'       => 'Название',
                        'title-info'  => '«Бесплатная доставка» - это метод доставки, при котором стоимость доставки отменяется, и продавец покрывает расходы на доставку товаров покупателю.',
                    ],

                    'flat-rate-shipping' => [
                        'description' => 'Описание',
                        'page-title'  => 'Фиксированная ставка доставки',
                        'rate'        => 'Ставка',
                        'status'      => 'Статус',
                        'title'       => 'Название',
                        'title-info'  => 'Фиксированная ставка доставки - это метод доставки, при котором взимается фиксированная плата за доставку, независимо от веса, размера или расстояния пакета. Это упрощает расчеты за доставку и может быть выгодным как для покупателей, так и для продавцов.',
                        'type'        => [
                            'per-order' => 'За заказ',
                            'per-unit'  => 'За единицу',
                            'title'     => 'Тип',
                        ],
                    ],
                ],

                'payment-methods' => [
                    'accepted-currencies'            => 'Принимаемые валюты',
                    'accepted-currencies-info'       => 'Добавьте коды валют через запятую, например, USD, INR, ...',
                    'business-account'               => 'Бизнес-аккаунт',
                    'cash-on-delivery'               => 'Оплата наличными при доставке',
                    'cash-on-delivery-info'          => 'Способ оплаты, при котором клиенты оплачивают наличными при получении товаров или услуг на своем пороге.',
                    'client-id'                      => 'Идентификатор клиента',
                    'client-id-info'                 => 'Используйте "sb" для тестирования.',
                    'client-secret'                  => 'Секретный ключ клиента',
                    'client-secret-info'             => 'Добавьте здесь свой секретный ключ',
                    'description'                    => 'Описание',
                    'generate-invoice'               => 'Автоматически генерировать счет после размещения заказа',
                    'generate-invoice-applicable'    => 'Применимо, если включена автоматическая генерация счета',
                    'info'                           => 'Установите информацию о методах оплаты',
                    'instructions'                   => 'Инструкции',
                    'logo'                           => 'Логотип',
                    'logo-information'               => 'Разрешение изображения должно быть 55px X 45px',
                    'mailing-address'                => 'Отправить чек на',
                    'money-transfer'                 => 'Денежный перевод',
                    'money-transfer-info'            => 'Перевод средств от одного лица или счета на другой, часто электронным путем, для различных целей, таких как транзакции или переводы денежных средств.',
                    'page-title'                     => 'Методы оплаты',
                    'paid'                           => 'Оплачено',
                    'paypal-smart-button'            => 'PayPal',
                    'paypal-smart-button-info'       => 'PayPal Smart Button: упрощает онлайн-платежи с настраиваемыми кнопками для безопасных многофункциональных транзакций на веб-сайтах и в приложениях.',
                    'paypal-standard'                => 'PayPal Standard',
                    'paypal-standard-info'           => 'PayPal Standard - это основной вариант оплаты PayPal для интернет-магазинов, позволяющий клиентам платить с помощью своих учетных записей PayPal или кредитных / дебетовых карт.',
                    'pending'                        => 'В ожидании',
                    'pending-payment'                => 'Ожидающий платеж',
                    'processing'                     => 'Обработка',
                    'sandbox'                        => 'Песочница',
                    'set-invoice-status'             => 'Установить статус счета после создания счета на',
                    'set-order-status'               => 'Установить статус заказа после создания счета на',
                    'sort-order'                     => 'Порядок сортировки',
                    'status'                         => 'Статус',
                    'title'                          => 'Название',
                ],

                'order-settings' => [
                    'info'               => 'Установите номера заказов, минимальные заказы и заказы на запас.',
                    'title'              => 'Настройки заказа',

                    'order-number' => [
                        'generator'   => 'Генератор номеров заказов',
                        'info'        => 'Уникальный идентификатор, присвоенный конкретному заказу клиента, облегчающий отслеживание, связь и ссылку на протяжении процесса покупки.',
                        'length'      => 'Длина номера заказа',
                        'prefix'      => 'Префикс номера заказа',
                        'suffix'      => 'Суффикс номера заказа',
                        'title'       => 'Настройки номера заказа',
                    ],

                    'minimum-order' => [
                        'description'             => 'Описание',
                        'enable'                  => 'Включить',
                        'include-discount-amount' => 'Включить сумму скидки',
                        'include-tax-amount'      => 'Включить налог в сумму',
                        'info'                    => 'Настроенные критерии, определяющие минимальное требуемое количество или стоимость заказа для его обработки или получения преимуществ.',
                        'minimum-order-amount'    => 'Минимальная сумма заказа',
                        'title'                   => 'Настройки минимального заказа',
                    ],

                    'reorder' => [
                        'admin-reorder'      => 'Повторный заказ администратора',
                        'admin-reorder-info' => 'Включить или отключить функцию повторного заказа для администраторов.',
                        'info'               => 'Включить или отключить функцию повторного заказа для администраторов.',
                        'shop-reorder'       => 'Повторный заказ магазина',
                        'shop-reorder-info'  => 'Включить или отключить функцию повторного заказа для пользователей магазина.',
                        'title'              => 'Разрешить повторный заказ',
                    ],

                    'stock-options' => [
                        'allow-back-orders' => 'Разрешить заказы на запас',
                        'info'              => 'Опции акций - это инвестиционные контракты, предоставляющие право на покупку или продажу акций компании по предварительно установленной цене, влияющие на потенциальную прибыль.',
                        'title'             => 'Опции акций',
                    ],
                ],

                'invoice-settings' => [
                    'info'  => 'Установите номер счета, условия оплаты, дизайн счета и напоминания о счетах.',
                    'title' => 'Настройки счета',

                    'invoice-number' => [
                        'generator'  => 'Генератор номеров счетов',
                        'info'       => 'Конфигурация правил или параметров для генерации и присвоения уникальных идентификационных номеров счетов для организационных и отслеживающих целей.',
                        'length'     => 'Длина номера счета',
                        'prefix'     => 'Префикс номера счета',
                        'suffix'     => 'Суффикс номера счета',
                        'title'      => 'Настройки номера счета',
                    ],

                    'payment-terms' => [
                        'due-duration'      => 'Срок оплаты',
                        'due-duration-day'  => ':due-duration день',
                        'due-duration-days' => ':due-duration дней',
                        'info'              => 'Согласованные условия, определяющие, когда и как должен быть произведен платеж за товары или услуги покупателем продавцу.',
                        'title'             => 'Условия оплаты',
                    ],

                    'pdf-print-outs' => [
                        'footer-text'      => 'Текст нижнего колонтитула',
                        'footer-text-info' => 'Введите текст, который появится в нижнем колонтитуле PDF.',
                        'info'             => 'Настройте печать PDF для отображения идентификатора счета, идентификатора заказа в заголовке и включения логотипа счета.',
                        'invoice-id-info'  => 'Настройте отображение идентификатора счета в заголовке счета.',
                        'invoice-id-title' => 'Отображать идентификатор счета в заголовке',
                        'logo'             => 'Логотип',
                        'logo-info'        => 'Разрешение изображения должно быть 131px x 30px.',
                        'order-id-info'    => 'Настройте отображение идентификатора заказа в заголовке счета.',
                        'order-id-title'   => 'Отображать идентификатор заказа в заголовке',
                        'title'            => 'Печать PDF',
                    ],

                    'invoice-reminders' => [
                        'info'                       => 'Автоматические уведомления или сообщения, отправляемые клиентам для напоминания о предстоящих или просроченных платежах по счетам.',
                        'interval-between-reminders' => 'Интервал между напоминаниями',
                        'maximum-limit-of-reminders' => 'Максимальное количество напоминаний',
                        'title'                      => 'Напоминания о счетах',
                    ],
                ],

                'taxes' => [
                    'title'      => 'Налоги',
                    'title-info' => 'Налоги - это обязательные сборы, взимаемые правительствами с товаров, услуг или операций, собираемые продавцами и перечисляемые властям.',

                    'categories' => [
                        'title'      => 'Категории налогов',
                        'title-info' => 'Категории налогов - это классификации различных типов налогов, таких как налог с продаж, налог на добавленную стоимость или акцизный налог, используемые для классификации и применения ставок налога к товарам или услугам.',
                        'product'    => 'Категория налога по умолчанию для товаров',
                        'shipping'   => 'Категория налога для доставки',
                        'none'       => 'Нет',
                    ],

                    'calculation' => [
                        'title'            => 'Настройки расчета',
                        'title-info'       => 'Информация о стоимости товаров или услуг, включая базовую цену, скидки, налоги и дополнительные сборы.',
                        'based-on'         => 'Расчет на основе',
                        'shipping-address' => 'Адрес доставки',
                        'billing-address'  => 'Адрес выставления счета',
                        'shipping-origin'  => 'Место отправления',
                        'product-prices'   => 'Цены на товары',
                        'shipping-prices'  => 'Цены на доставку',
                        'excluding-tax'    => 'Без учета налога',
                        'including-tax'    => 'С учетом налога',
                    ],

                    'default-destination-calculation' => [
                        'default-country'   => 'Страна по умолчанию',
                        'default-post-code' => 'Почтовый индекс по умолчанию',
                        'default-state'     => 'Регион по умолчанию',
                        'title'             => 'Расчет места назначения по умолчанию',
                        'title-info'        => 'Автоматическое определение стандартного или начального места назначения на основе заранее определенных факторов или настроек.',
                    ],

                    'shopping-cart' => [
                        'title'                   => 'Настройки отображения корзины',
                        'title-info'              => 'Установите отображение налогов в корзине',
                        'display-prices'          => 'Отображать цены',
                        'display-subtotal'        => 'Отображать промежуточный итог',
                        'display-shipping-amount' => 'Отображать стоимость доставки',
                        'excluding-tax'           => 'Без учета налога',
                        'including-tax'           => 'С учетом налога',
                        'both'                    => 'И без учета налога, и с учетом',
                    ],

                    'sales' => [
                        'title'                   => 'Настройки отображения заказов, счетов, возвратов',
                        'title-info'              => 'Установите отображение налогов в заказах, счетах и возвратах',
                        'display-prices'          => 'Отображать цены',
                        'display-subtotal'        => 'Отображать промежуточный итог',
                        'display-shipping-amount' => 'Отображать стоимость доставки',
                        'excluding-tax'           => 'Без учета налога',
                        'including-tax'           => 'С учетом налога',
                        'both'                    => 'И без учета налога, и с учетом',
                    ],
                ],

                'checkout' => [
                    'title' => 'Оформление заказа',
                    'info'  => 'Настроить оформление заказа для гостей, включить или отключить мини-корзину, сводка корзины.',

                    'shopping-cart' => [
                        'cart-page'              => 'Страница корзины',
                        'cart-page-info'         => 'Управление видимостью страницы корзины для улучшения опыта покупателя.',
                        'cross-sell'             => 'Допродажа товаров',
                        'cross-sell-info'        => 'Включить допродажу товаров для увеличения возможностей дополнительных продаж.',
                        'estimate-shipping'      => 'Расчет стоимости доставки',
                        'estimate-shipping-info' => 'Включить расчет стоимости доставки, чтобы предоставить предварительные расходы на доставку.',
                        'guest-checkout'         => 'Разрешить оформление заказа гостем',
                        'guest-checkout-info'    => 'Включить оформление заказа гостем для более быстрого и удобного процесса покупки.',
                        'info'                   => 'Включите оформление заказа гостем, страницу корзины, допродажу товаров и расчет стоимости доставки, чтобы улучшить удобство для пользователей и оптимизировать процесс покупок для увеличения продаж.',
                        'title'                  => 'Корзина',
                    ],

                    'my-cart' => [
                        'display-item-quantities' => 'Отображать количество товаров',
                        'display-number-in-cart'  => 'Отображать количество товаров в корзине',
                        'info'                    => 'Включите настройки для Моей корзины, чтобы показывать сводку количества товаров и отображать общее количество товаров в корзине для удобного отслеживания.',
                        'summary'                 => 'Сводка',
                        'title'                   => 'Моя корзина',
                    ],

                    'mini-cart' => [
                        'display-mini-cart'    => 'Отображать мини-корзину',
                        'info'                 => 'Включите настройки мини-корзины, чтобы отображать мини-корзину и показывать информацию о предложениях в мини-корзине для быстрого доступа к деталям корзины и акциям.',
                        'mini-cart-offer-info' => 'Информация о предложениях в мини-корзине',
                        'title'                => 'Мини-корзина',
                    ],
                ],
            ],
        ],
    ],

    'components' => [
        'layouts' => [
            'header' => [
                'account-title' => 'Аккаунт',
                'app-version'   => 'Версия : :version',
                'logout'        => 'Выход',
                'my-account'    => 'Мой аккаунт',
                'notifications' => 'Уведомления',
                'visit-shop'    => 'Перейти в магазин',

                'mega-search' => [
                    'categories'                      => 'Категории',
                    'customers'                       => 'Клиенты',
                    'explore-all-categories'          => 'Просмотреть все категории',
                    'explore-all-customers'           => 'Просмотреть всех клиентов',
                    'explore-all-matching-categories' => 'Просмотреть все категории, соответствующие запросу \":query\" (:count)',
                    'explore-all-matching-customers'  => 'Просмотреть всех клиентов, соответствующих запросу \":query\" (:count)',
                    'explore-all-matching-orders'     => 'Просмотреть все заказы, соответствующие запросу \":query\" (:count)',
                    'explore-all-matching-products'   => 'Просмотреть все товары, соответствующие запросу \":query\" (:count)',
                    'explore-all-orders'              => 'Просмотреть все заказы',
                    'explore-all-products'            => 'Просмотреть все товары',
                    'orders'                          => 'Заказы',
                    'products'                        => 'Товары',
                    'sku'                             => 'Артикул: :sku',
                    'title'                           => 'Мега-поиск',
                ],
            ],

            'sidebar' => [
                'attribute-families'       => 'Семейства атрибутов',
                'attributes'               => 'Атрибуты',
                'booking-product'          => 'Бронирования',
                'campaigns'                => 'Кампании',
                'catalog'                  => 'Каталог',
                'categories'               => 'Категории',
                'channels'                 => 'Каналы',
                'cms'                      => 'CMS',
                'collapse'                 => 'Свернуть',
                'communications'           => 'Коммуникации',
                'configure'                => 'Настроить',
                'currencies'               => 'Валюты',
                'customers'                => 'Клиенты',
                'dashboard'                => 'Панель управления',
                'data-transfer'            => 'Обмен данными',
                'discount'                 => 'Скидка',
                'email-templates'          => 'Шаблоны электронных писем',
                'events'                   => 'События',
                'exchange-rates'           => 'Обменные курсы',
                'gdpr-data-requests'       => 'Запросы данных GDPR',
                'groups'                   => 'Группы',
                'imports'                  => 'Импорт',
                'inventory-sources'        => 'Источники инвентаризации',
                'invoices'                 => 'Счета',
                'locales'                  => 'Локали',
                'marketing'                => 'Маркетинг',
                'mode'                     => 'Темный режим',
                'newsletter-subscriptions' => 'Подписки на новостную рассылку',
                'orders'                   => 'Заказы',
                'products'                 => 'Продукты',
                'promotions'               => 'Акции',
                'refunds'                  => 'Возвраты',
                'reporting'                => 'Отчетность',
                'reviews'                  => 'Отзывы',
                'roles'                    => 'Роли',
                'sales'                    => 'Продажи',
                'search-seo'               => 'Поиск и SEO',
                'search-synonyms'          => 'Синонимы поиска',
                'search-terms'             => 'Поисковые запросы',
                'settings'                 => 'Настройки',
                'shipments'                => 'Отправки',
                'sitemaps'                 => 'Карты сайта',
                'tax-categories'           => 'Категории налогов',
                'tax-rates'                => 'Налоговые ставки',
                'taxes'                    => 'Налоги',
                'themes'                   => 'Темы',
                'transactions'             => 'Транзакции',
                'url-rewrites'             => 'Перезапись URL',
                'users'                    => 'Пользователи',
            ],

            'powered-by' => [
                'description' => 'Работает на :bagisto, проект с открытым исходным кодом от :webkul.',
            ],
        ],

        'datagrid' => [
            'index' => [
                'no-records-selected'              => 'Не выбрано ни одной записи.',
                'must-select-a-mass-action-option' => 'Вы должны выбрать опцию массового действия.',
                'must-select-a-mass-action'        => 'Вы должны выбрать массовое действие.',
            ],

            'toolbar' => [
                'length-of' => ':length из',
                'of'        => 'из',
                'per-page'  => 'На страницу',
                'results'   => ':total Результаты',
                'selected'  => ':total Выбрано',

                'mass-actions' => [
                    'select-action' => 'Выбрать действие',
                    'select-option' => 'Выбрать опцию',
                    'submit'        => 'Отправить',
                ],

                'filter' => [
                    'apply-filters-btn' => 'Применить фильтры',
                    'back-btn'          => 'Назад',
                    'create-new-filter' => 'Создать новый фильтр',
                    'custom-filters'    => 'Пользовательские фильтры',
                    'delete-error'      => 'Произошла ошибка при удалении фильтра, пожалуйста, попробуйте снова.',
                    'delete-success'    => 'Фильтр успешно удален.',
                    'empty-description' => 'Нет выбранных фильтров для сохранения. Пожалуйста, выберите фильтры для сохранения.',
                    'empty-title'       => 'Добавить фильтры для сохранения',
                    'name'              => 'Название',
                    'quick-filters'     => 'Быстрые фильтры',
                    'save-btn'          => 'Сохранить',
                    'save-filter'       => 'Сохранить фильтр',
                    'saved-success'     => 'Фильтр успешно сохранен.',
                    'selected-filters'  => 'Выбранные фильтры',
                    'title'             => 'Фильтр',
                    'update'            => 'Обновить',
                    'update-filter'     => 'Обновить фильтр',
                    'updated-success'   => 'Фильтр успешно обновлён.',
                ],

                'search' => [
                    'title' => 'Поиск',
                ],
            ],

            'filters' => [
                'select' => 'Выбрать',
                'title'  => 'Фильтры',

                'dropdown' => [
                    'searchable' => [
                        'atleast-two-chars' => 'Введите как минимум 2 символа...',
                        'no-results'        => 'Результатов не найдено...',
                    ],
                ],

                'boolean-options' => [
                    'false' => 'Ложь',
                    'true'  => 'Истина',
                ],

                'custom-filters' => [
                    'clear-all' => 'Очистить все',
                    'title'     => 'Пользовательские фильтры',
                ],

                'date-options' => [
                    'last-month'        => 'В прошлом месяце',
                    'last-six-months'   => 'За последние 6 месяцев',
                    'last-three-months' => 'За последние 3 месяца',
                    'this-month'        => 'В этом месяце',
                    'this-week'         => 'На этой неделе',
                    'this-year'         => 'В этом году',
                    'today'             => 'Сегодня',
                    'yesterday'         => 'Вчера',
                ],
            ],

            'table' => [
                'actions'              => 'Действия',
                'no-records-available' => 'Нет доступных записей.',
            ],
        ],

        'modal' => [
            'confirm' => [
                'agree-btn'    => 'Согласен',
                'disagree-btn' => 'Не согласен',
                'message'      => 'Вы уверены, что хотите выполнить это действие?',
                'title'        => 'Вы уверены?',
            ],
        ],

        'products' => [
            'search' => [
                'add-btn'       => 'Добавить выбранный продукт',
                'empty-info'    => 'Нет продуктов по данному запросу.',
                'empty-title'   => 'Продукты не найдены',
                'product-image' => 'Изображение продукта',
                'qty'           => ':qty доступно',
                'sku'           => 'SKU - :sku',
                'title'         => 'Выберите продукты',
            ],
        ],

        'media' => [
            'images' => [
                'add-image-btn'     => 'Добавить изображение',
                'ai-add-image-btn'  => 'Магия ИИ',
                'ai-btn-info'       => 'Создать изображение',
                'allowed-types'     => 'png, jpeg, jpg',
                'not-allowed-error' => 'Разрешены только файлы изображений (.jpeg, .jpg, .png и др.)',

                'ai-generation' => [
                    '1024x1024'        => '1024x1024',
                    '1024x1792'        => '1024x1792',
                    '1792x1024'        => '1792x1024',
                    'apply'            => 'Применить',
                    'dall-e-2'         => 'Далл.Э 2',
                    'dall-e-3'         => 'Далл.Э 3',
                    'generate'         => 'Создать',
                    'generating'       => 'Создание...',
                    'hd'               => 'HD',
                    'model'            => 'Модель',
                    'number-of-images' => 'Количество изображений',
                    'prompt'           => 'Подсказка',
                    'quality'          => 'Качество',
                    'regenerate'       => 'Регенерировать',
                    'regenerating'     => 'Регенерация...',
                    'size'             => 'Размер',
                    'standard'         => 'Стандарт',
                    'title'            => 'Генерация изображений с использованием ИИ',
                ],

                'placeholders' => [
                    'front'     => 'Спереди',
                    'next'      => 'Следующий',
                    'size'      => 'Размер',
                    'use-cases' => 'Использование',
                    'zoom'      => 'Увеличить',
                ],
            ],

            'videos' => [
                'add-video-btn'     => 'Добавить видео',
                'allowed-types'     => 'mp4, webm, mkv',
                'not-allowed-error' => 'Разрешены только файлы видео (.mp4, .mov, .ogg и др.)',
            ],
        ],

        'tinymce' => [
            'ai-btn-tile' => 'Магия ИИ',

            'ai-generation' => [
                'apply'                  => 'Применить',
                'deepseek-r1-8b'         => 'DeepSeek R1 (8b)',
                'enabled'                => 'Включено',
                'gemini-2-0-flash'       => 'Gemini 2.0 Flash',
                'generate'               => 'Создать',
                'generated-content'      => 'Созданный контент',
                'generated-content-info' => 'Контент, созданный ИИ, может быть вводящим в заблуждение. Пожалуйста, проверьте созданный контент перед его применением.',
                'generating'             => 'Создание...',
                'gpt-4-turbo'            => 'OpenAI gpt-4 Turbo',
                'gpt-4o'                 => 'OpenAI gpt-4o',
                'gpt-4o-mini'            => 'OpenAI gpt-4o mini',
                'llama-groq'             => 'Llama 3.3 (Groq)',
                'llama3-1-8b'            => 'Llama 3.1 (8B)',
                'llama3-2-1b'            => 'Llama 3.2 (1B)',
                'llama3-2-3b'            => 'Llama 3.2 (3B)',
                'llama3-8b'              => 'Llama 3 (8B)',
                'llava-7b'               => 'Llava (7b)',
                'mistral-7b'             => 'Mistral (7b)',
                'model'                  => 'Модель',
                'orca-mini'              => 'Orca Mini',
                'phi3-5'                 => 'Phi 3.5',
                'prompt'                 => 'Подсказка',
                'qwen2-5-0-5b'           => 'Qwen 2.5 (0.5b)',
                'qwen2-5-1-5b'           => 'Qwen 2.5 (1.5b)',
                'qwen2-5-14b'            => 'Qwen 2.5 (14b)',
                'qwen2-5-3b'             => 'Qwen 2.5 (3b)',
                'qwen2-5-7b'             => 'Qwen 2.5 (7b)',
                'starling-lm-7b'         => 'Starling-lm (7b)',
                'title'                  => 'Помощь ИИ',
                'vicuna-13b'             => 'Vicuna (13b)',
                'vicuna-7b'              => 'Vicuna (7b)',
            ],
        ],
    ],

    'acl' => [
        'addresses'                => 'Адреса',
        'attribute-families'       => 'Семейства атрибутов',
        'attributes'               => 'Атрибуты',
        'campaigns'                => 'Кампании',
        'cancel'                   => 'Отмена',
        'cart-rules'               => 'Правила корзины',
        'catalog-rules'            => 'Правила каталога',
        'catalog'                  => 'Каталог',
        'categories'               => 'Категории',
        'channels'                 => 'Каналы',
        'cms'                      => 'CMS',
        'communications'           => 'Коммуникации',
        'configure'                => 'Настроить',
        'copy'                     => 'Копировать',
        'create'                   => 'Создавать',
        'currencies'               => 'Валюты',
        'customers'                => 'Клиенты',
        'dashboard'                => 'Панель управления',
        'data-transfer'            => 'Обмен данными',
        'delete'                   => 'Удалить',
        'edit'                     => 'Изменить',
        'email-templates'          => 'Шаблоны электронных писем',
        'events'                   => 'События',
        'exchange-rates'           => 'Курсы обмена',
        'gdpr'                     => 'GDPR',
        'groups'                   => 'Группы',
        'import'                   => 'Импортировать',
        'imports'                  => 'Импорт',
        'inventory-sources'        => 'Источники инвентаря',
        'invoices'                 => 'Счета',
        'locales'                  => 'Локали',
        'marketing'                => 'Маркетинг',
        'newsletter-subscriptions' => 'Подписки на рассылку',
        'note'                     => 'Примечание',
        'orders'                   => 'Заказы',
        'products'                 => 'Продукты',
        'promotions'               => 'Акции',
        'refunds'                  => 'Возвраты',
        'reporting'                => 'Отчетность',
        'reviews'                  => 'Отзывы',
        'roles'                    => 'Роли',
        'sales'                    => 'Продажи',
        'search-seo'               => 'Поиск и SEO',
        'search-synonyms'          => 'Синонимы поиска',
        'search-terms'             => 'Поисковые запросы',
        'settings'                 => 'Настройки',
        'shipments'                => 'Отгрузки',
        'sitemaps'                 => 'Карты сайта',
        'subscribers'              => 'Подписчики на рассылку',
        'tax-categories'           => 'Категории налогов',
        'tax-rates'                => 'Ставки налогов',
        'taxes'                    => 'Налоги',
        'themes'                   => 'Темы',
        'transactions'             => 'Транзакции',
        'url-rewrites'             => 'Перезапись URL',
        'users'                    => 'Пользователи',
        'view'                     => 'Просмотр',
    ],

    'errors' => [
        'dashboard' => 'Панель управления',
        'go-back'   => 'Вернуться назад',
        'support'   => 'Если проблема не устранена, свяжитесь с нами по адресу <a href=":link" class=":class">:email</a> для получения помощи.',

        '404' => [
            'description' => 'Упс! Страница, которую вы ищете, находится в отпуске. Похоже, мы не смогли найти то, что вы искали.',
            'title'       => 'Ошибка 404 - Страница не найдена',
        ],

        '401' => [
            'description' => 'Упс! Похоже, у вас нет разрешения на доступ к этой странице. Вероятно, вам не хватает необходимых учетных данных.',
            'title'       => 'Ошибка 401 - Несанкционированный доступ',
        ],

        '403' => [
            'description' => 'Упс! Эта страница ограничена. Похоже, у вас нет необходимых разрешений для просмотра этого контента.',
            'title'       => 'Ошибка 403 - Доступ запрещен',
        ],

        '500' => [
            'description' => 'Упс! Что-то пошло не так. Похоже, у нас возникли проблемы с загрузкой страницы, которую вы ищете.',
            'title'       => 'Ошибка 500 - Внутренняя ошибка сервера',
        ],

        '503' => [
            'description' => 'Упс! Похоже, мы временно недоступны из-за технического обслуживания. Пожалуйста, вернитесь через некоторое время.',
            'title'       => 'Ошибка 503 - Служба недоступна',
        ],
    ],

    'export' => [
        'csv'        => 'CSV',
        'download'   => 'Скачать',
        'export'     => 'Экспорт',
        'no-records' => 'Нет данных для экспорта',
        'xls'        => 'XLS',
        'xlsx'       => 'XLSX',
    ],

    'validations' => [
        'slug-being-used' => 'Этот слаг используется в категориях или продуктах.',
        'slug-reserved'   => 'Этот слаг зарезервирован.',
    ],

    'footer' => [
        'copy-right' => 'Разработано на платформе <a href="https://bagisto.com/" target="_blank">Bagisto</a>, Проект сообщества <a href="https://webkul.com/" target="_blank">Webkul</a>',
    ],

    'emails' => [
        'dear'   => 'Уважаемый :admin_name',
        'thanks' => 'Если вам нужна какая-либо помощь, пожалуйста, свяжитесь с нами по адресу <a href=":link" style=":style">:email</a>.<br/>Спасибо!',

        'admin' => [
            'forgot-password' => [
                'description'    => 'Вы получаете это письмо, потому что мы получили запрос на сброс пароля для вашей учетной записи.',
                'greeting'       => 'Забыли пароль!',
                'reset-password' => 'Сбросить пароль',
                'subject'        => 'Письмо сброса пароля',
            ],
        ],

        'customers' => [
            'registration' => [
                'description' => 'Новая учетная запись клиента успешно создана. Теперь они могут войти в систему, используя свой адрес электронной почты и пароль. После входа в систему они получат доступ к различным услугам, включая возможность просматривать прошлые заказы, управлять списками желаний и обновлять информацию своей учетной записи.',
                'greeting'    => 'Мы тепло приветствуем нового клиента :customer_name, который только что зарегистрировался у нас!',
                'subject'     => 'Регистрация нового клиента',
            ],

            'gdpr' => [
                'new-delete-request' => 'Новый запрос на удаление данных',
                'new-update-request' => 'Новый запрос на обновление данных',

                'new-request' => [
                    'customer-name'  => 'Имя клиента : ',
                    'delete-summary' => 'Резюме запроса на удаление',
                    'message'        => 'Сообщение : ',
                    'request-status' => 'Статус запроса : ',
                    'request-type'   => 'Тип запроса : ',
                    'update-summary' => 'Резюме запроса на обновление',
                ],

                'status-update' => [
                    'subject'        => 'Запрос GDPR обновлён',
                    'summary'        => 'Статус запроса GDPR обновлён',
                    'request-status' => 'Статус запроса:',
                    'request-type'   => 'Тип запроса:',
                    'message'        => 'Сообщение:',
                ],
            ],
        ],

        'orders' => [
            'created' => [
                'greeting' => 'У вас новый заказ :order_id, размещенный :created_at',
                'subject'  => 'Подтверждение нового заказа',
                'summary'  => 'Сводка заказа',
                'title'    => 'Подтверждение заказа!',
            ],

            'invoiced' => [
                'greeting' => 'Ваш счет #:invoice_id по заказу :order_id создан :created_at',
                'subject'  => 'Подтверждение нового счета',
                'summary'  => 'Сводка счета',
                'title'    => 'Подтверждение счета!',
            ],

            'shipped' => [
                'greeting' => 'Вы отправили заказ :order_id, размещенный :created_at',
                'subject'  => 'Подтверждение отправки заказа',
                'summary'  => 'Сводка отправки',
                'title'    => 'Заказ отправлен!',
            ],

            'inventory-source' => [
                'greeting' => 'Вы отправили заказ :order_id, размещенный :created_at',
                'subject'  => 'Подтверждение отправки заказа',
                'summary'  => 'Сводка отправки',
                'title'    => 'Заказ отправлен!',
            ],

            'refunded' => [
                'greeting' => 'Вы вернули заказ :order_id, размещенный :created_at',
                'subject'  => 'Подтверждение возврата заказа',
                'summary'  => 'Сводка возврата',
                'title'    => 'Заказ возвращен!',
            ],

            'canceled' => [
                'greeting' => 'Вы отменили заказ :order_id, размещенный :created_at',
                'subject'  => 'Заказ отменен',
                'summary'  => 'Сводка заказа',
                'title'    => 'Заказ отменен!',
            ],

            'billing-address'            => 'Платежный адрес',
            'carrier'                    => 'Перевозчик',
            'contact'                    => 'Контакт',
            'discount'                   => 'Скидка',
            'excl-tax'                   => 'Без налога: ',
            'grand-total'                => 'Общая сумма',
            'name'                       => 'Имя',
            'payment'                    => 'Оплата',
            'price'                      => 'Цена',
            'qty'                        => 'Кол-во',
            'shipping-address'           => 'Адрес доставки',
            'shipping-handling-excl-tax' => 'Доставка и обработка (без налога)',
            'shipping-handling-incl-tax' => 'Доставка и обработка (с налогом)',
            'shipping-handling'          => 'Доставка и обработка',
            'shipping'                   => 'Доставка',
            'sku'                        => 'Артикул',
            'subtotal-excl-tax'          => 'Подитог (без налога)',
            'subtotal-incl-tax'          => 'Подитог (с налогом)',
            'subtotal'                   => 'Подитог',
            'tax'                        => 'Налог',
            'tracking-number'            => 'Номер отслеживания: :tracking_number',
        ],
    ],
];
