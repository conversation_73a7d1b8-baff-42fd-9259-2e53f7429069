<?php

return [
    'users' => [
        'sessions' => [
            'email'                  => 'E-posta Adresi',
            'forget-password-link'   => 'Şifremi Unuttum ?',
            'password'               => 'Şifre',
            'powered-by-description' => ':webkul tarafından geliştirilen açık kaynaklı bir proje olan :bagisto tarafından desteklenmektedir.',
            'submit-btn'             => 'Giriş Yap',
            'title'                  => 'Giri<PERSON> Yap',
        ],

        'forget-password' => [
            'create' => [
                'email'                  => 'Kayıtlı E-posta',
                'email-not-exist'        => 'E-posta Bulunamadı',
                'page-title'             => 'Şifremi Unuttum',
                'powered-by-description' => ':webkul tarafından geliştirilen açık kaynaklı bir proje olan :bagisto tarafından desteklenmektedir.',
                'reset-link-sent'        => '<PERSON><PERSON>re Sıfırlama Bağlantısı Gönderildi',
                'sign-in-link'           => '<PERSON><PERSON><PERSON>’a <PERSON>eri <PERSON>ö<PERSON> ?',
                'submit-btn'             => 'Sıfırla',
                'title'                  => '<PERSON><PERSON><PERSON>',
            ],
        ],

        'reset-password' => [
            'back-link-title'        => 'Giriş Yap’a Geri Dön ?',
            'confirm-password'       => 'Şifreyi Onayla',
            'email'                  => 'Kayıtlı E-posta',
            'password'               => 'Şifre',
            'powered-by-description' => ':webkul tarafından geliştirilen açık kaynaklı bir proje olan :bagisto tarafından desteklenmektedir.',
            'submit-btn'             => 'Şifre Sıfırla',
            'title'                  => 'Şifre Sıfırlama',
        ],
    ],

    'notifications' => [
        'description-text' => 'Tüm Bildirimleri Listele',
        'marked-success'   => 'Tüm bildirimler okundu olarak işaretlendi',
        'no-record'        => 'Kayıt Bulunamadı',
        'of'               => 'of',
        'per-page'         => 'Sayfa Başına',
        'read-all'         => 'Tümünü Okundu Olarak İşaretle',
        'title'            => 'Bildirimler',
        'view-all'         => 'Tümünü Görüntüle',

        'order-status-messages' => [
            'all'             => 'Tümü',
            'canceled'        => 'Sipariş İptal Edildi',
            'closed'          => 'Sipariş Kapatıldı',
            'completed'       => 'Sipariş Tamamlandı',
            'pending'         => 'Sipariş Bekliyor',
            'pending-payment' => 'Bekleyen Ödeme',
            'processing'      => 'Sipariş İşleniyor',
        ],
    ],

    'account' => [
        'edit' => [
            'back-btn'          => 'Geri',
            'change-password'   => 'Şifre Değiştir',
            'confirm-password'  => 'Şifreyi Onayla',
            'current-password'  => 'Mevcut Şifre',
            'email'             => 'E-posta',
            'general'           => 'Genel',
            'invalid-password'  => 'Girdiğiniz mevcut şifre yanlış.',
            'name'              => 'Ad',
            'password'          => 'Şifre',
            'profile-image'     => 'Profil Resmi',
            'save-btn'          => 'Hesabı Kaydet',
            'title'             => 'Hesabım',
            'update-success'    => 'Hesap başarıyla güncellendi',
            'upload-image-info' => 'PNG veya JPG Formatında 110x110 boyutlarında bir profil resmi yükleyin',
        ],
    ],

    'dashboard' => [
        'index' => [
            'add-customer'                => 'Müşteri Ekle',
            'add-product'                 => 'Ürün Ekle',
            'all-channels'                => 'Tüm Kanallar',
            'attribute-code'              => 'Özellik Kodu',
            'average-sale'                => 'Ortalama Sipariş Tutarı',
            'color'                       => 'Renk',
            'customer-info'               => 'En Çok Satış Yapan Müşteri Bulunamadı',
            'customer-with-most-sales'    => 'En Çok Satış Yapan Müşteri',
            'date-duration'               => ':start - :end',
            'decreased'                   => ':progress%',
            'empty-threshold'             => 'Boş Eşik',
            'empty-threshold-description' => 'Uygun ürün bulunmuyor',
            'end-date'                    => 'Bitiş Tarihi',
            'from'                        => 'Başlangıç',
            'increased'                   => ':progress%',
            'more-products'               => ':product_count+ Daha Fazla Ürün',
            'order'                       => ':total_orders Sipariş',
            'order-count'                 => ':count Sipariş',
            'order-id'                    => '#:id',
            'overall-details'             => 'Genel Detaylar',
            'pay-by'                      => ':method ile Ödeme Yapıldı',
            'product-count'               => ':count Ürün',
            'product-image'               => 'Ürün Resmi',
            'product-info'                => 'Yoldayken ilgili ürünleri ekleyin.',
            'product-number'              => 'Ürün - :product_number',
            'revenue'                     => 'Gelir: :total',
            'sale-count'                  => ':count Satış',
            'sales'                       => 'Satışlar',
            'sku'                         => 'Ürün Kodu - :sku',
            'start-date'                  => 'Başlangıç Tarihi',
            'stock-threshold'             => 'Stok Eşiği',
            'store-stats'                 => 'Mağaza İstatistikleri',
            'title'                       => 'Yönetim Paneli',
            'to'                          => 'Bitiş',
            'today-customers'             => 'Bugünkü Müşteriler',
            'today-details'               => 'Bugünkü Detaylar',
            'today-orders'                => 'Bugünkü Siparişler',
            'today-sales'                 => 'Bugünkü Satışlar',
            'top-performing-categories'   => 'En İyi Performans Gösteren Kategoriler',
            'top-selling-products'        => 'En Çok Satılan Ürünler',
            'total-customers'             => 'Toplam Müşteri',
            'total-orders'                => 'Toplam Sipariş',
            'total-sales'                 => 'Toplam Satış',
            'total-stock'                 => ':total_stock Stok',
            'total-unpaid-invoices'       => 'Toplam Ödenmemiş Faturalar',
            'unique-visitors'             => ':count Benzersiz',
            'user-info'                   => 'Mağazanızda neler olup bittiğini hızlıca gözden geçirin',
            'user-name'                   => 'Merhaba! :user_name',
            'visitors'                    => 'Ziyaretçi',
        ],
    ],

    'sales' => [
        'orders' => [
            'index' => [
                'create-btn' => 'Sipariş Oluştur',
                'title'      => 'Siparişler',

                'search-customer' => [
                    'create-btn'  => 'Müşteri Oluştur',
                    'empty-info'  => 'Arama terimi için müşteri bulunamadı.',
                    'empty-title' => 'Müşteri bulunamadı',
                    'search-by'   => 'E-posta veya isme göre ara',
                    'title'       => 'Müşteri Seç',
                ],

                'datagrid' => [
                    'canceled'        => 'İptal Edildi',
                    'channel-name'    => 'Kanal',
                    'closed'          => 'Kapatıldı',
                    'completed'       => 'Tamamlandı',
                    'customer'        => 'Müşteri',
                    'date'            => 'Tarih',
                    'email'           => 'E-posta',
                    'fraud'           => 'Dolandırıcılık',
                    'grand-total'     => 'Genel Toplam',
                    'id'              => '#:id',
                    'items'           => 'Öğeler',
                    'location'        => 'Konum',
                    'order-id'        => 'Sipariş Kimliği',
                    'pay-by'          => 'Şunu İle Ödeme Yap - :method',
                    'pay-via'         => 'Şunu İle Ödeme Yap - :method',
                    'pending-payment' => 'Ödeme Bekliyor',
                    'pending'         => 'Bekliyor',
                    'processing'      => 'İşleniyor',
                    'product-count'   => ':count + Daha Fazla Ürün',
                    'status'          => 'Durum',
                    'success'         => 'Başarılı',
                    'view'            => 'Görüntüle',
                ],
            ],

            'create' => [
                'add-to-cart'             => 'Sepete Ekle',
                'back-btn'                => 'Geri',
                'check-billing-address'   => 'Fatura adresi eksik.',
                'check-shipping-address'  => 'Teslimat adresi eksik.',
                'configuration'           => 'Yapılandırma',
                'coupon-already-applied'  => 'Kupon kodu zaten uygulanmış.',
                'coupon-applied'          => 'Kupon kodu başarıyla uygulandı.',
                'coupon-error'            => 'Kupon kodu uygulanamıyor.',
                'coupon-not-found'        => 'Kupon Bulunamadı',
                'coupon-remove'           => 'Kupon kodu başarıyla kaldırıldı.',
                'error'                   => 'Bir hata oluştu',
                'minimum-order-error'     => 'Minimum sipariş tutarı karşılanmıyor.',
                'order-placed-success'    => 'Sipariş başarıyla oluşturuldu.',
                'payment-not-supported'   => 'Bu ödeme yöntemi desteklenmiyor',
                'save-btn'                => 'Sipariş Oluştur',
                'specify-payment-method'  => 'Ödeme yöntemi eksik.',
                'specify-shipping-method' => 'Teslimat yöntemi eksik.',
                'title'                   => ':name için Sipariş Oluştur',

                'types' => [
                    'simple' => [
                        'none'         => 'Yok',
                        'total-amount' => 'Toplam Tutar',
                    ],

                    'configurable' => [
                        'select-options' => 'Lütfen bir seçenek seçin',
                    ],

                    'bundle' => [
                        'none'         => 'Yok',
                        'total-amount' => 'Toplam Tutar',
                    ],

                    'grouped' => [
                        'name' => 'Ad',
                    ],

                    'downloadable' => [
                        'title' => 'Bağlantılar',
                    ],

                    'virtual' => [
                        'none'         => 'Yok',
                        'total-amount' => 'Toplam Tutar',
                    ],
                ],

                'cart' => [
                    'success-add-to-cart' => 'Ürün başarıyla sepete eklendi',
                    'success-remove'      => 'Öğe sepetten başarıyla kaldırıldı',
                    'success-update'      => 'Sepet öğesi başarıyla güncellendi',

                    'items' => [
                        'add-product'       => 'Ürün Ekle',
                        'amount-per-unit'   => ':amount Birim Başı x :qty Miktar',
                        'delete'            => 'Sil',
                        'empty-description' => 'Sepetinizde ürün bulunmamaktadır.',
                        'empty-title'       => 'Boş Sepet Öğeleri',
                        'excl-tax'          => 'KDV Hariç',
                        'move-to-wishlist'  => 'İstek Listesine Taşı',
                        'see-details'       => 'Detayları Görüntüle',
                        'sku'               => 'SKU - :sku',
                        'sub-total'         => 'Ara Toplam - :sub_total',
                        'title'             => 'Sepet Öğeleri',

                        'search' => [
                            'add-to-cart'   => 'Sepete Ekle',
                            'available-qty' => ':qty Mevcut',
                            'empty-info'    => 'Arama terimi için ürün bulunamadı.',
                            'empty-title'   => 'Ürün bulunamadı',
                            'product-image' => 'Ürün Görseli',
                            'qty'           => 'Adet',
                            'sku'           => 'SKU - :sku',
                            'title'         => 'Ürünleri Ara',
                        ],
                    ],

                    'address' => [
                        'add-btn'          => 'Adres Ekle',
                        'add-new'          => 'Yeni adres ekle',
                        'add-new-address'  => 'Yeni adres ekle',
                        'addresses'        => 'Adresler',
                        'back'             => 'Geri',
                        'billing-address'  => 'Fatura Adresi',
                        'city'             => 'Şehir',
                        'company-name'     => 'Şirket Adı',
                        'confirm'          => 'Onayla',
                        'country'          => 'Ülke',
                        'edit-btn'         => 'Adresi Düzenle',
                        'email'            => 'E-posta',
                        'first-name'       => 'Ad',
                        'last-name'        => 'Soyad',
                        'postcode'         => 'Posta Kodu',
                        'proceed'          => 'Devam Et',
                        'same-as-billing'  => 'Teslimat için aynı adresi kullan?',
                        'save'             => 'Kaydet',
                        'save-address'     => 'Bu adresi adres defterine kaydet',
                        'select-country'   => 'Ülke Seçin',
                        'select-state'     => 'Eyalet Seçin',
                        'shipping-address' => 'Teslimat Adresi',
                        'state'            => 'Eyalet',
                        'street-address'   => 'Adres',
                        'telephone'        => 'Telefon',
                        'title'            => 'Adres',
                        'vat-id'           => 'Vergi Kimlik Numarası',
                    ],

                    'payment' => [
                        'title' => 'Ödeme',
                    ],

                    'shipping' => [
                        'title' => 'Teslimat',
                    ],

                    'summary' => [
                        'apply-coupon'             => 'Kupon Uygula',
                        'discount-amount'          => 'İndirim Tutarı',
                        'enter-your-code'          => 'Kodunuzu Girin',
                        'grand-total'              => 'Genel Toplam',
                        'place-order'              => 'Sipariş Ver',
                        'processing'               => 'İşleniyor',
                        'shipping-amount-excl-tax' => 'Kargo Tutarı (KDV Hariç)',
                        'shipping-amount-incl-tax' => 'Kargo Tutarı (KDV Dahil)',
                        'shipping-amount'          => 'Kargo Tutarı',
                        'sub-total-excl-tax'       => 'Ara Toplam (KDV Hariç)',
                        'sub-total-incl-tax'       => 'Ara Toplam (KDV Dahil)',
                        'sub-total'                => 'Ara Toplam',
                        'tax'                      => 'KDV',
                        'title'                    => 'Sipariş Özeti',
                    ],
                ],

                'cart-items' => [
                    'add-to-cart'       => 'Sepete Ekle',
                    'delete'            => 'Sil',
                    'empty-description' => 'Sepetinizde ürün bulunmamaktadır.',
                    'empty-title'       => 'Boş Sepet',
                    'excl-tax'          => 'KDV Hariç',
                    'see-details'       => 'Detayları Görüntüle',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Sepet Öğeleri',
                ],

                'recent-order-items' => [
                    'add-to-cart'       => 'Sepete Ekle',
                    'empty-description' => 'Son siparişlerinizde ürün bulunmamaktadır.',
                    'empty-title'       => 'Boş Siparişler',
                    'see-details'       => 'Detayları Görüntüle',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Son Sipariş Öğeleri',
                    'view'              => 'Görüntüle',
                ],

                'wishlist-items' => [
                    'add-to-cart'       => 'Sepete Ekle',
                    'delete'            => 'Sil',
                    'empty-description' => 'İstek listenizde ürün bulunmamaktadır.',
                    'empty-title'       => 'Boş İstek Listesi Öğeleri',
                    'see-details'       => 'Detayları Görüntüle',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'İstek Listesi Öğeleri',
                ],

                'compare-items' => [
                    'add-to-cart'       => 'Sepete Ekle',
                    'delete'            => 'Sil',
                    'empty-description' => 'Karşılaştırma listenizde ürün bulunmamaktadır.',
                    'empty-title'       => 'Boş Karşılaştırma Öğeleri',
                    'sku'               => 'SKU - :sku',
                    'title'             => 'Karşılaştırma Öğeleri',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount Birim Başına x :qty Miktar',
                'billing-address'                => 'Fatura Adresi',
                'cancel'                         => 'İptal',
                'cancel-msg'                     => 'Bu siparişi iptal etmek istediğinizden emin misiniz',
                'cancel-success'                 => 'Sipariş başarıyla iptal edildi',
                'canceled'                       => 'İptal Edildi',
                'channel'                        => 'Kanal',
                'closed'                         => 'Kapalı',
                'comment-success'                => 'Yorum başarıyla eklendi.',
                'comments'                       => 'Yorumlar',
                'completed'                      => 'Tamamlandı',
                'contact'                        => 'İletişim',
                'create-success'                 => 'Sipariş başarıyla oluşturuldu',
                'currency'                       => 'Para Birimi',
                'customer'                       => 'Müşteri',
                'customer-group'                 => 'Müşteri Grubu',
                'customer-not-notified'          => ':date | Müşteri <b>Bildirilmedi</b>',
                'customer-notified'              => ':date | Müşteri <b>Bildirildi</b>',
                'discount'                       => 'İndirim - :discount',
                'download-pdf'                   => 'PDF İndir',
                'fraud'                          => 'Dolandırıcılık',
                'grand-total'                    => 'Toplam - :grand_total',
                'invoice-id'                     => 'Fatura #:invoice',
                'invoices'                       => 'Faturalar',
                'item-canceled'                  => 'İptal Edildi (:qty_canceled)',
                'item-invoice'                   => 'Faturalandı (:qty_invoiced)',
                'item-ordered'                   => 'Sipariş Edildi (:qty_ordered)',
                'item-refunded'                  => 'İade Edildi (:qty_refunded)',
                'item-shipped'                   => 'Gönderildi (:qty_shipped)',
                'name'                           => 'Ad',
                'no-invoice-found'               => 'Fatura Bulunamadı',
                'no-refund-found'                => 'İade Bulunamadı',
                'no-shipment-found'              => 'Gönderim Bulunamadı',
                'notify-customer'                => 'Müşteriye Bildir',
                'order-date'                     => 'Sipariş Tarihi',
                'order-information'              => 'Sipariş Bilgileri',
                'order-status'                   => 'Sipariş Durumu',
                'payment-and-shipping'           => 'Ödeme ve Gönderim',
                'payment-method'                 => 'Ödeme Yöntemi',
                'pending'                        => 'Beklemede',
                'pending_payment'                => 'Ödeme Bekliyor',
                'per-unit'                       => 'Birim Başına',
                'price'                          => 'Fiyat - :price',
                'price-excl-tax'                 => 'Fiyat (KDV Hariç) - :price',
                'price-incl-tax'                 => 'Fiyat (KDV Dahil) - :price',
                'processing'                     => 'İşleniyor',
                'quantity'                       => 'Miktar',
                'refund'                         => 'İade',
                'refund-id'                      => 'İade #:refund',
                'refunded'                       => 'İade Edildi',
                'reorder'                        => 'Yeniden Sipariş Ver',
                'ship'                           => 'Gönder',
                'shipment'                       => 'Gönderim #:shipment',
                'shipments'                      => 'Gönderimler',
                'shipping-address'               => 'Teslimat Adresi',
                'shipping-and-handling'          => 'Kargo ve İşlem',
                'shipping-and-handling-excl-tax' => 'Kargo ve İşlem (KDV Hariç)',
                'shipping-and-handling-incl-tax' => 'Kargo ve İşlem (KDV Dahil)',
                'shipping-method'                => 'Teslimat Yöntemi',
                'shipping-price'                 => 'Kargo Ücreti',
                'sku'                            => 'SKU - :sku',
                'status'                         => 'Durum',
                'sub-total'                      => 'Ara Toplam - :sub_total',
                'sub-total-excl-tax'             => 'Ara Toplam (KDV Hariç) - :sub_total',
                'sub-total-incl-tax'             => 'Ara Toplam (KDV Dahil) - :sub_total',
                'submit-comment'                 => 'Yorumu Gönder',
                'summary-discount'               => 'İndirim',
                'summary-grand-total'            => 'Toplam',
                'summary-sub-total'              => 'Ara Toplam',
                'summary-sub-total-excl-tax'     => 'Ara Toplam (KDV Hariç)',
                'summary-sub-total-incl-tax'     => 'Ara Toplam (KDV Dahil)',
                'summary-tax'                    => 'KDV',
                'tax'                            => 'KDV (:percent) - :tax',
                'title'                          => 'Sipariş #:order_id',
                'total-due'                      => 'Toplam Tutar',
                'total-paid'                     => 'Toplam Ödenen',
                'total-refund'                   => 'Toplam İade',
                'view'                           => 'Görüntüle',
                'write-your-comment'             => 'Yorumunuzu yazın',
            ],
        ],

        'shipments' => [
            'index' => [
                'title' => 'Gönderiler',

                'datagrid' => [
                    'id'               => 'Kimlik',
                    'inventory-source' => 'Envanter Kaynağı',
                    'order-date'       => 'Sipariş Tarihi',
                    'order-id'         => 'Sipariş Kimliği',
                    'shipment-date'    => 'Gönderi Tarihi',
                    'shipment-to'      => 'Gönderi Alıcısı',
                    'total-qty'        => 'Toplam Miktar',
                    'view'             => 'Görüntüle',
                ],
            ],

            'create' => [
                'amount-per-unit'  => ':amount Birim Başına x :qty Miktar',
                'cancel-error'     => 'Sipariş iptal edilemez',
                'carrier-name'     => 'Taşıyıcı Adı',
                'create-btn'       => 'Gönderi Oluştur',
                'creation-error'   => 'Gönderi oluşturma hatası',
                'item-canceled'    => 'İptal Edildi (:qty_canceled)',
                'item-invoice'     => 'Faturalandı (:qty_invoiced)',
                'item-ordered'     => 'Sipariş Edilen (:qty_ordered)',
                'item-refunded'    => 'İade Edildi (:qty_refunded)',
                'item-shipped'     => 'Gönderildi (:qty_shipped)',
                'order-error'      => 'Gönderi geçerli değil',
                'per-unit'         => 'Birim Başına',
                'qty-available'    => 'Mevcut Miktar',
                'qty-to-ship'      => 'Gönderilecek Miktar',
                'quantity-invalid' => 'Miktar Geçersiz',
                'sku'              => 'Ürün Kodu - :sku',
                'source'           => 'Kaynak',
                'success'          => 'Gönderi başarıyla oluşturuldu',
                'title'            => 'Yeni Gönderi Oluştur',
                'tracking-number'  => 'Takip Numarası',
            ],

            'view' => [
                'billing-address'      => 'Fatura Adresi',
                'carrier-title'        => 'Taşıyıcı Başlığı',
                'channel'              => 'Kanal',
                'currency'             => 'Para Birimi',
                'customer'             => 'Müşteri',
                'email'                => 'E-posta - :email',
                'inventory-source'     => 'Envanter Kaynağı',
                'order-date'           => 'Sipariş Tarihi',
                'order-id'             => 'Sipariş Kimliği',
                'order-information'    => 'Sipariş Bilgileri',
                'order-status'         => 'Sipariş Durumu',
                'ordered-items'        => 'Sipariş Edilen Ürünler',
                'payment-and-shipping' => 'Ödeme ve Gönderi',
                'payment-method'       => 'Ödeme Yöntemi',
                'product-image'        => 'Ürün Resmi',
                'qty'                  => 'Miktar - :qty',
                'shipping-address'     => 'Teslimat Adresi',
                'shipping-method'      => 'Kargo Yöntemi',
                'shipping-price'       => 'Kargo Ücreti',
                'sku'                  => 'Ürün Kodu - :sku',
                'title'                => 'Gönderi #:shipment_id',
                'tracking-number'      => 'Takip Numarası',
            ],
        ],

        'refunds' => [
            'index' => [
                'title' => 'İadeler',

                'datagrid' => [
                    'billed-to'       => 'Faturalandı',
                    'id'              => 'Kimlik',
                    'order-id'        => 'Sipariş Kimliği',
                    'refund-date'     => 'İade Tarihi',
                    'refunded-amount' => 'İade Edilen Tutar',
                    'view'            => 'Görüntüle',
                ],
            ],

            'view' => [
                'account-information'        => 'Hesap Bilgileri',
                'adjustment-fee'             => 'Düzenleme Ücreti',
                'adjustment-refund'          => 'Düzenleme İadesi',
                'base-discounted-amount'     => 'İndirimli Tutar - :base_discounted_amount',
                'billing-address'            => 'Fatura Adresi',
                'currency'                   => 'Para Birimi',
                'sub-total-amount-excl-tax'  => 'Ara Toplam (KDV Hariç) - :discounted_amount',
                'sub-total-amount-incl-tax'  => 'Ara Toplam (KDV Dahil) - :discounted_amount',
                'sub-total-amount'           => 'Ara Toplam - :discounted_amount',
                'grand-total'                => 'Genel Toplam',
                'order-channel'              => 'Sipariş Kanalı',
                'order-date'                 => 'Sipariş Tarihi',
                'order-id'                   => 'Sipariş Kimliği',
                'order-information'          => 'Sipariş Bilgileri',
                'order-status'               => 'Sipariş Durumu',
                'payment-information'        => 'Ödeme Bilgileri',
                'payment-method'             => 'Ödeme Yöntemi',
                'price-excl-tax'             => 'Fiyat (KDV Hariç) - :price',
                'price-incl-tax'             => 'Fiyat (KDV Dahil) - :price',
                'price'                      => 'Fiyat - :price',
                'product-image'              => 'Ürün Resmi',
                'product-ordered'            => 'Sipariş Edilen Ürünler',
                'qty'                        => 'Miktar - :qty',
                'refund'                     => 'İade',
                'shipping-address'           => 'Teslimat Adresi',
                'shipping-handling-excl-tax' => 'Kargo ve İşlem (KDV Hariç)',
                'shipping-handling-incl-tax' => 'Kargo ve İşlem (KDV Dahil)',
                'shipping-handling'          => 'Kargo ve İşlem',
                'shipping-method'            => 'Kargo Yöntemi',
                'shipping-price'             => 'Kargo Ücreti',
                'sku'                        => 'Ürün Kodu - :sku',
                'sub-total-excl-tax'         => 'Ara Toplam (KDV Hariç)',
                'sub-total-incl-tax'         => 'Ara Toplam (KDV Dahil)',
                'sub-total'                  => 'Ara Toplam',
                'tax'                        => 'KDV',
                'tax-amount'                 => 'KDV Tutarı - :tax_amount',
                'title'                      => 'İade #:refund_id',
            ],

            'create' => [
                'adjustment-fee'              => 'Düzenleme Ücreti',
                'adjustment-refund'           => 'Düzenleme İadesi',
                'amount-per-unit'             => ':amount Birim Başına x :qty Miktar',
                'create-success'              => 'İade başarıyla oluşturuldu',
                'creation-error'              => 'İade oluşturma izin verilmiyor.',
                'discount-amount'             => 'İndirim Tutarı',
                'grand-total'                 => 'Genel Toplam',
                'invalid-qty'                 => 'Faturalanacak ürünlerde geçersiz miktar bulduk.',
                'invalid-refund-amount-error' => 'İade tutarı sıfır olmamalıdır.',
                'item-canceled'               => 'İptal Edildi (:qty_canceled)',
                'item-invoice'                => 'Faturalandı (:qty_invoiced)',
                'item-ordered'                => 'Sipariş Edilen (:qty_ordered)',
                'item-refunded'               => 'İade Edildi (:qty_refunded)',
                'item-shipped'                => 'Gönderildi (:qty_shipped)',
                'per-unit'                    => 'Birim Başına',
                'price'                       => 'Fiyat',
                'qty-to-refund'               => 'İade Edilecek Miktar',
                'refund-btn'                  => 'İade Et',
                'refund-limit-error'          => 'İade Tutarı :amount işlemi gerçekleştirilemez.',
                'refund-shipping'             => 'Kargo İadesi',
                'sku'                         => 'Ürün Kodu - :sku',
                'subtotal'                    => 'Ara Toplam',
                'tax-amount'                  => 'Vergi Tutarı',
                'title'                       => 'İade Oluştur',
                'update-totals-btn'           => 'Toplamları Güncelle',
            ],
        ],

        'invoices' => [
            'index' => [
                'title' => 'Faturalar',

                'datagrid' => [
                    'action'              => 'İşlemler',
                    'days-left'           => ':count gün kaldı',
                    'days-overdue'        => ':count gün gecikmiş',
                    'grand-total'         => 'Genel Toplam',
                    'id'                  => 'Kimlik',
                    'invoice-date'        => 'Fatura Tarihi',
                    'mass-update-success' => 'Seçilen faturalar başarıyla güncellendi.',
                    'order-id'            => 'Sipariş Kimliği',
                    'overdue'             => 'Vadesi Geçmiş',
                    'overdue-by'          => ':count gün gecikmiş',
                    'paid'                => 'Ödenen',
                    'pending'             => 'Beklemede',
                    'status'              => 'Durum',
                    'update-status'       => 'Durumu güncelle',
                ],
            ],

            'view' => [
                'amount-per-unit'                => ':amount Birim Başına x :qty Miktar',
                'channel'                        => 'Kanal',
                'customer-email'                 => 'E-posta - :email',
                'customer'                       => 'Müşteri',
                'discount'                       => 'İndirim Miktarı - :discount',
                'email'                          => 'E-posta',
                'grand-total'                    => 'Genel Toplam',
                'invoice-items'                  => 'Fatura Kalemleri',
                'invoice-sent'                   => 'Fatura başarıyla gönderildi',
                'invoice-status'                 => 'Fatura Durumu',
                'order-date'                     => 'Sipariş Tarihi',
                'order-id'                       => 'Sipariş Kimliği',
                'order-information'              => 'Sipariş Bilgileri',
                'order-status'                   => 'Sipariş Durumu',
                'price-excl-tax'                 => 'Fiyat (KDV Hariç) - :price',
                'price-incl-tax'                 => 'Fiyat (KDV Dahil) - :price',
                'price'                          => 'Fiyat - :price',
                'print'                          => 'Yazdır',
                'product-image'                  => 'Ürün Resmi',
                'qty'                            => 'Miktar - :qty',
                'send-btn'                       => 'Gönder',
                'send-duplicate-invoice'         => 'Yinelenen Fatura Gönder',
                'send'                           => 'Gönder',
                'shipping-and-handling-excl-tax' => 'Kargo ve İşlem Ücreti (KDV Hariç)',
                'shipping-and-handling-incl-tax' => 'Kargo ve İşlem Ücreti (KDV Dahil)',
                'shipping-and-handling'          => 'Kargo ve İşlem Ücreti',
                'sku'                            => 'Ürün Kodu - :sku',
                'sub-total-excl-tax'             => 'Ara Toplam (KDV Hariç) - :sub_total',
                'sub-total-incl-tax'             => 'Ara Toplam (KDV Dahil) - :sub_total',
                'sub-total-summary-excl-tax'     => 'Ara Toplam (KDV Hariç)',
                'sub-total-summary-incl-tax'     => 'Ara Toplam (KDV Dahil)',
                'sub-total-summary'              => 'Ara Toplam',
                'sub-total'                      => 'Ara Toplam - :sub_total',
                'summary-discount'               => 'İndirim Miktarı',
                'summary-tax'                    => 'KDV Tutarı',
                'tax'                            => 'KDV Tutarı - :tax',
                'title'                          => 'Fatura #:invoice_id',
            ],

            'create' => [
                'amount-per-unit'    => ':amount Birim Başına x :qty Miktar',
                'create-invoice'     => 'Fatura Oluştur',
                'create-success'     => 'Fatura başarıyla oluşturuldu',
                'create-transaction' => 'İşlem Oluştur',
                'creation-error'     => 'Sipariş faturası oluşturma izni verilmiyor.',
                'invalid-qty'        => 'Faturalandırılacak ürünler için geçersiz bir miktar bulduk.',
                'invoice'            => 'Fatura',
                'new-invoice'        => 'Yeni Fatura',
                'product-error'      => 'Ürünler olmadan fatura oluşturulamaz.',
                'product-image'      => 'Ürün Resmi',
                'qty-to-invoiced'    => 'Faturalandırılacak Miktar',
                'sku'                => 'SKU - :sku',
            ],

            'invoice-pdf' => [
                'bank-details'               => 'Banka Detayları',
                'bill-to'                    => 'Fatura Edilen',
                'contact-number'             => 'İletişim Numarası',
                'contact'                    => 'İletişim',
                'date'                       => 'Fatura Tarihi',
                'discount'                   => 'İndirim',
                'excl-tax'                   => 'KDV Hariç:',
                'grand-total'                => 'Genel Toplam',
                'invoice-id'                 => 'Fatura ID',
                'invoice'                    => 'Fatura',
                'order-date'                 => 'Sipariş Tarihi',
                'order-id'                   => 'Sipariş ID',
                'payment-method'             => 'Ödeme Yöntemi',
                'payment-terms'              => 'Ödeme Koşulları',
                'price'                      => 'Fiyat',
                'product-name'               => 'Ürün Adı',
                'qty'                        => 'Miktar',
                'ship-to'                    => 'Gönderilecek Adres',
                'shipping-handling-excl-tax' => 'Kargo ve İşlem Ücreti (KDV Hariç)',
                'shipping-handling-incl-tax' => 'Kargo ve İşlem Ücreti (KDV Dahil)',
                'shipping-handling'          => 'Kargo ve İşlem Ücreti',
                'shipping-method'            => 'Kargo Yöntemi',
                'sku'                        => 'SKU',
                'subtotal-excl-tax'          => 'Ara Toplam (KDV Hariç)',
                'subtotal-incl-tax'          => 'Ara Toplam (KDV Dahil)',
                'subtotal'                   => 'Ara Toplam',
                'tax-amount'                 => 'Vergi Tutarı',
                'tax'                        => 'Vergi',
                'vat-number'                 => 'Vergi Numarası',
            ],
        ],

        'invoice-transaction' => [
            'id'               => 'Kimlik',
            'transaction-date' => 'İşlem Tarihi',
            'transaction-id'   => 'İşlem Kimliği',
            'view'             => 'Görüntüle',
        ],

        'transactions' => [
            'index' => [
                'create-btn' => 'İşlem Oluştur',
                'title'      => 'İşlemler',

                'datagrid' => [
                    'completed'          => 'Tamamlandı',
                    'id'                 => 'Kimlik',
                    'invoice-id'         => 'Fatura Kimliği',
                    'order-id'           => 'Sipariş Kimliği',
                    'paid'               => 'Ödenmiş',
                    'pending'            => 'Beklemede',
                    'status'             => 'Durum',
                    'transaction-amount' => 'Miktar',
                    'transaction-date'   => 'Tarih',
                    'transaction-id'     => 'İşlem Kimliği',
                    'view'               => 'Görüntüle',
                ],

                'create' => [
                    'already-paid'               => 'Zaten ödendi',
                    'amount'                     => 'Miktar',
                    'create-transaction'         => 'İşlem Oluştur',
                    'invoice-id'                 => 'Fatura Kimliği',
                    'invoice-missing'            => 'Fatura Bulunamadı',
                    'payment-method'             => 'Ödeme Yöntemi',
                    'save-transaction'           => 'İşlemi Kaydet',
                    'transaction-amount-exceeds' => 'İşlem Miktarı aşılıyor',
                    'transaction-amount-zero'    => 'İşlem Miktarı sıfır',
                    'transaction-saved'          => 'İşlem başarıyla kaydedildi.',
                ],

                'view' => [
                    'amount'           => 'Miktar',
                    'created-at'       => 'Oluşturulma Tarihi',
                    'invoice-id'       => 'Fatura ID',
                    'order-id'         => 'Sipariş ID',
                    'payment-details'  => 'Ödeme Detayları',
                    'payment-method'   => 'Ödeme Yöntemi',
                    'status'           => 'Durum',
                    'title'            => 'İşlem Detayları',
                    'transaction-id'   => 'İşlem ID',
                ],
            ],
        ],

        'booking' => [
            'index' => [
                'datagrid' => [
                    'created-date' => 'Oluşturulma Tarihi',
                    'from'         => 'Başlangıç',
                    'id'           => 'Kimlik',
                    'order-id'     => 'Sipariş Kimliği',
                    'qty'          => 'Miktar',
                    'to'           => 'Bitiş',
                    'view'         => 'Görüntüle',
                ],

                'title'    => 'Rezervasyonlar',
            ],

            'calendar' => [
                'booking-date'     => 'Rezervasyon Tarihi',
                'booking-details'  => 'Rezervasyon Detayları',
                'canceled'         => 'İptal Edildi',
                'closed'           => 'Kapalı',
                'done'             => 'Tamamlandı',
                'order-id'         => 'Sipariş Kimliği',
                'pending'          => 'Beklemede',
                'price'            => 'Fiyat',
                'status'           => 'Durum',
                'time-slot'        => 'Zaman Dilimi:',
                'view-details'     => 'Detayları Görüntüle',
            ],

            'title' => 'Rezervasyon Ürünü',
        ],
    ],

    'catalog' => [
        'products' => [
            'index' => [
                'already-taken' => ':name zaten alınmış.',
                'create-btn'    => 'Ürün Oluştur',
                'title'         => 'Ürünler',

                'create' => [
                    'back-btn'                => 'Geri',
                    'configurable-attributes' => 'Yapılandırılabilir Özellikler',
                    'create-btn'              => 'Ürün Oluştur',
                    'family'                  => 'Aile',
                    'save-btn'                => 'Ürünü Kaydet',
                    'sku'                     => 'SKU',
                    'title'                   => 'Yeni Ürün Oluştur',
                    'type'                    => 'Tür',
                ],

                'datagrid' => [
                    'active'                 => 'Aktif',
                    'attribute-family'       => 'Özellik Ailesi',
                    'attribute-family-value' => 'Özellik Ailesi - :attribute_family',
                    'category'               => 'Kategori',
                    'channel'                => 'Kanal',
                    'copy-of'                => 'Kopyası :value',
                    'copy-of-slug'           => 'kopyası-:value',
                    'delete'                 => 'Sil',
                    'disable'                => 'Devre Dışı',
                    'id'                     => 'Kimlik',
                    'id-value'               => 'Kimlik - :id',
                    'image'                  => 'Resim',
                    'mass-delete-success'    => 'Seçilen Ürünler Başarıyla Silindi',
                    'mass-update-success'    => 'Seçilen Ürünler Başarıyla Güncellendi',
                    'name'                   => 'Ad',
                    'out-of-stock'           => 'Stokta Yok',
                    'price'                  => 'Fiyat',
                    'product-image'          => 'Ürün Resmi',
                    'qty'                    => 'Miktar',
                    'qty-value'              => ':qty Mevcut',
                    'sku'                    => 'SKU',
                    'sku-value'              => 'SKU - :sku',
                    'status'                 => 'Durum',
                    'type'                   => 'Tür',
                    'update-status'          => 'Durumu Güncelle',
                ],
            ],

            'edit' => [
                'preview'  => 'Önizleme',
                'remove'   => 'Kaldır',
                'save-btn' => 'Ürünü Kaydet',
                'title'    => 'Ürünü Düzenle',

                'channels' => [
                    'title' => 'Kanallar',
                ],

                'price' => [
                    'group' => [
                        'add-group-price'           => 'Grup Fiyatı Ekle',
                        'all-groups'                => 'Tüm Gruplar',
                        'create-btn'                => 'Yeni Ekle',
                        'discount-group-price-info' => ':qty Miktarında indirimli fiyat :price',
                        'edit-btn'                  => 'Düzenle',
                        'empty-info'                => 'Belirli bir gruba ait müşteriler için özel fiyatlandırma.',
                        'fixed-group-price-info'    => ':qty Miktarında sabit fiyat :price',
                        'title'                     => 'Müşteri Grubu Fiyatı',

                        'create' => [
                            'all-groups'     => 'Tüm Gruplar',
                            'create-title'   => 'Müşteri Grubu Fiyatı Oluştur',
                            'customer-group' => 'Müşteri Grubu',
                            'delete-btn'     => 'Sil',
                            'discount'       => 'İndirim',
                            'fixed'          => 'Sabit',
                            'price'          => 'Fiyat',
                            'price-type'     => 'Fiyat Türü',
                            'qty'            => 'Minimum Adet',
                            'save-btn'       => 'Kaydet',
                            'update-title'   => 'Müşteri Grubu Fiyatını Güncelle',
                        ],
                    ],
                ],

                'inventories' => [
                    'pending-ordered-qty'      => 'Bekleyen Sipariş Miktarı: :qty',
                    'pending-ordered-qty-info' => 'Bekleyen sipariş miktarı, sevkiyat sonrası ilgili envanter kaynağından düşürülecektir. İptal durumunda bekleme miktarı satışa sunulacaktır.',
                    'title'                    => 'Envanterler',
                ],

                'categories' => [
                    'title' => 'Kategoriler',
                ],

                'images' => [
                    'info'  => 'Resim çözünürlüğü 560px X 609px gibi olmalıdır',
                    'title' => 'Resimler',
                ],

                'videos' => [
                    'error' => ':attribute :max kilobayttan büyük olamaz. Lütfen daha küçük bir dosya seçin.',
                    'title' => 'Videolar',
                    'info'  => 'Maksimum video boyutu :size gibi olmalıdır',
                ],

                'links' => [
                    'related-products' => [
                        'empty-info' => 'Yoldayken ilgili ürünleri ekleyin.',
                        'info'       => 'Müşteri görüntülediği ürüne ek olarak ilişkili ürünlerle karşılaştırılır.',
                        'title'      => 'İlgili Ürünler',
                    ],

                    'up-sells' => [
                        'empty-info' => 'Yoldayken satış ürünleri ekleyin.',
                        'info'       => 'Müşteri, şu anda görüntüledikleri ürünün bir alternatifi olarak hizmet eden üst satış ürünleriyle karşılaşır.',
                        'title'      => 'Üst Satış Ürünleri',
                    ],

                    'cross-sells' => [
                        'empty-info' => 'Yoldayken çapraz satış ürünleri ekleyin.',
                        'info'       => 'Alışveriş sepetine bitişik olarak, zaten sepetinize eklenen ürünleri tamamlamak için çapraz satış olarak konumlandırılan bu "dürtü alışverişi" ürünlerini bulacaksınız.',
                        'title'      => 'Çapraz Satış Ürünleri',
                    ],

                    'add-btn'           => 'Ürün Ekle',
                    'delete'            => 'Sil',
                    'empty-info'        => ':type ürünleri eklemek için.',
                    'empty-title'       => 'Ürün Ekle',
                    'image-placeholder' => 'Ürün Resmi',
                    'sku'               => 'SKU - :sku',
                ],

                'types' => [
                    'simple' => [
                        'customizable-options' => [
                            'add-btn'           => 'Seçenek ekle',
                            'empty-info'        => 'Özelleştirilebilir seçenekler oluşturmak için.',
                            'empty-title'       => 'Seçenek ekle',
                            'info'              => 'Bu, basit ürünü özelleştirecektir.',
                            'title'             => 'Özelleştirilebilir Öğe',

                            'update-create' => [
                                'is-required'               => 'Gerekli mi',
                                'max-characters'            => 'Maksimum Karakter',
                                'name'                      => 'Başlık',
                                'no'                        => 'Hayır',
                                'price'                     => 'Fiyat',
                                'save-btn'                  => 'Kaydet',
                                'supported-file-extensions' => 'Desteklenen Dosya Uzantıları',
                                'title'                     => 'Seçenek',
                                'type'                      => 'Tür',
                                'yes'                       => 'Evet',
                            ],

                            'option' => [
                                'add-btn'     => 'Seçenek ekle',
                                'delete'      => 'Sil',
                                'delete-btn'  => 'Sil',
                                'edit-btn'    => 'Düzenle',
                                'empty-info'  => 'Çeşitli ürün kombinasyonları oluşturmak için.',
                                'empty-title' => 'Seçenek ekle',

                                'types' => [
                                    'text' => [
                                        'title' => 'Metin',
                                    ],

                                    'textarea' => [
                                        'title' => 'Metin Alanı',
                                    ],

                                    'checkbox' => [
                                        'title' => 'Onay Kutusu',
                                    ],

                                    'radio' => [
                                        'title' => 'Radyo',
                                    ],

                                    'select' => [
                                        'title' => 'Seç',
                                    ],

                                    'multiselect' => [
                                        'title' => 'Çoklu Seçim',
                                    ],

                                    'date' => [
                                        'title' => 'Tarih',
                                    ],

                                    'datetime' => [
                                        'title' => 'Tarih ve Saat',
                                    ],

                                    'time' => [
                                        'title' => 'Saat',
                                    ],

                                    'file' => [
                                        'title' => 'Dosya',
                                    ],
                                ],

                                'items' => [
                                    'update-create' => [
                                        'label'    => 'Etiket',
                                        'price'    => 'Fiyat',
                                        'save-btn' => 'Kaydet',
                                        'title'    => 'Seçenek',
                                    ],
                                ],
                            ],

                            'validations' => [
                                'associated-product' => 'Bu ürün zaten başka bir ürünle ilişkilendirilmiş.',
                            ],
                        ],
                    ],

                    'configurable' => [
                        'add-btn'           => 'Çeşit Ekle',
                        'delete-btn'        => 'Sil',
                        'edit-btn'          => 'Düzenle',
                        'empty-info'        => 'Hızlı bir şekilde ürünün farklı kombinasyonlarını oluşturmak için.',
                        'empty-title'       => 'Çeşit Ekle',
                        'image-placeholder' => 'Ürün Resmi',
                        'info'              => 'Çeşit ürünleri, tüm olası özellik kombinasyonlarına dayanır.',
                        'qty'               => ':qty Adet',
                        'sku'               => 'SKU - :sku',
                        'title'             => 'Çeşitler',

                        'create' => [
                            'description'            => 'Açıklama',
                            'name'                   => 'Ad',
                            'save-btn'               => 'Ekle',
                            'title'                  => 'Çeşit Ekle',
                            'variant-already-exists' => 'Bu çeşit zaten mevcut',
                        ],

                        'edit' => [
                            'disabled'        => 'Devre Dışı',
                            'edit-info'       => 'Ürün bilgilerini detaylı olarak güncellemek isterseniz,',
                            'edit-link-title' => 'Ürün Detayları Sayfasına Git',
                            'enabled'         => 'Etkin',
                            'images'          => 'Resimler',
                            'name'            => 'Ad',
                            'price'           => 'Fiyat',
                            'quantities'      => 'Miktarlar',
                            'save-btn'        => 'Kaydet',
                            'sku'             => 'SKU',
                            'status'          => 'Durum',
                            'title'           => 'Ürün',
                            'weight'          => 'Ağırlık',
                        ],

                        'mass-edit' => [
                            'add-images'          => 'Resim Ekle',
                            'apply-to-all-btn'    => 'Hepsine Uygula',
                            'apply-to-all-name'   => 'Adı tüm varyantlara uygula.',
                            'apply-to-all-sku'    => 'Fiyatı tüm SKU\'lara uygula.',
                            'apply-to-all-status' => 'Durumu tüm varyantlara uygula.',
                            'apply-to-all-weight' => 'Tüm varyantlara ağırlık uygula.',
                            'edit-inventories'    => 'Envanterleri Düzenle',
                            'edit-names'          => 'İsimleri Düzenle',
                            'edit-prices'         => 'Fiyatları Düzenle',
                            'edit-sku'            => 'SKU\'ları Düzenle',
                            'edit-status'         => 'Durumu Düzenle',
                            'edit-weight'         => 'Ağırlığı Düzenle',
                            'name'                => 'Ad',
                            'price'               => 'Fiyat',
                            'remove-images'       => 'Resimleri Kaldır',
                            'remove-variants'     => 'Varyantları Kaldır',
                            'select-action'       => 'Eylemi Seç',
                            'select-variants'     => 'Varyantları Seç',
                            'status'              => 'Durum',
                            'variant-name'        => 'Varyant Adı',
                            'variant-sku'         => 'Varyant SKU',
                            'weight'              => 'Ağırlık',
                        ],
                    ],

                    'grouped' => [
                        'add-btn'           => 'Ürün Ekle',
                        'default-qty'       => 'Varsayılan Miktar',
                        'delete'            => 'Sil',
                        'empty-info'        => 'Hızlı bir şekilde ürünün farklı kombinasyonlarını oluşturmak için.',
                        'empty-title'       => 'Ürün Ekle',
                        'image-placeholder' => 'Ürün Resmi',
                        'info'              => 'Gruplu bir ürün, bir takım olarak sunulan bağımsız ürünlerden oluşur ve müşterilere değer ve kolaylık sağlayan özel bir fiyatla birlikte satılır. Her ürün bireysel olarak veya gruba dahil olarak satın alınabilir.',
                        'sku'               => 'SKU - :sku',
                        'title'             => 'Grup Ürünler',
                    ],

                    'bundle' => [
                        'add-btn'           => 'Seçenek Ekle',
                        'empty-info'        => 'Hızlı bir şekilde paketleme seçenekleri oluşturmak için.',
                        'empty-title'       => 'Seçenek Ekle',
                        'image-placeholder' => 'Ürün Resmi',
                        'info'              => 'Bir paketleme ürünü, bir arada satılan birden fazla ürün veya hizmetin özel bir fiyatla sunulduğu bir pakettir ve müşterilere değer ve kolaylık sağlar.',
                        'title'             => 'Paketleme Ürünleri',

                        'update-create' => [
                            'checkbox'    => 'Onay Kutusu',
                            'is-required' => 'Gerekli mi?',
                            'multiselect' => 'Çoklu Seçim',
                            'name'        => 'Başlık',
                            'no'          => 'Hayır',
                            'radio'       => 'Radyo',
                            'save-btn'    => 'Kaydet',
                            'select'      => 'Seçim',
                            'title'       => 'Seçenek',
                            'type'        => 'Tür',
                            'yes'         => 'Evet',
                        ],

                        'option' => [
                            'add-btn'     => 'Ürün Ekle',
                            'default-qty' => 'Varsayılan Miktar',
                            'delete'      => 'Sil',
                            'delete-btn'  => 'Sil',
                            'edit-btn'    => 'Düzenle',
                            'empty-info'  => 'Hızlı bir şekilde ürünün farklı kombinasyonlarını oluşturmak için.',
                            'empty-title' => 'Ürün Ekle',
                            'sku'         => 'SKU - :sku',

                            'types' => [
                                'checkbox' => [
                                    'info'  => 'Onay kutusu kullanarak varsayılan ürünü ayarlayın',
                                    'title' => 'Onay Kutusu',
                                ],

                                'multiselect' => [
                                    'info'  => 'Çoklu seçim düğmesini kullanarak varsayılan ürünü ayarlayın',
                                    'title' => 'Çoklu Seçim',
                                ],

                                'radio' => [
                                    'info'  => 'Radyo düğmesini kullanarak varsayılan ürünü ayarlayın',
                                    'title' => 'Radyo',
                                ],

                                'select' => [
                                    'info'  => 'Radyo düğmesini kullanarak varsayılan ürünü ayarlayın',
                                    'title' => 'Seçim',
                                ],
                            ],
                        ],
                    ],

                    'booking' => [
                        'available-from' => 'Mevcut Başlangıç',
                        'available-to'   => 'Mevcut Bitiş',
                        'location'       => 'Konum',
                        'qty'            => 'Miktar',
                        'title'          => 'Rezervasyon Türü',

                        'available-every-week' => [
                            'no'    => 'Hayır',
                            'title' => 'Her Hafta Mevcut',
                            'yes'   => 'Evet',
                        ],

                        'appointment' => [
                            'break-duration'         => 'Slotlar Arası Mola Süresi (Dakika)',
                            'slot-duration'          => 'Slot Süresi (Dakika)',

                            'same-slot-for-all-days' => [
                                'no'    => 'Hayır',
                                'title' => 'Tüm Günler İçin Aynı Slot',
                                'yes'   => 'Evet',
                            ],
                        ],

                        'default' => [
                            'add'              => 'Ekle',
                            'break-duration'   => 'Slotlar Arası Mola Süresi (Dakika)',
                            'close'            => 'Kapat',
                            'description'      => 'Rezervasyon Bilgisi',
                            'description-info' => 'Süre, slotlara göre oluşturulacak ve görüntülenecektir. Tüm slotlar arasında benzersiz olacak ve mağaza vitrinde görünecektir',
                            'edit'             => 'Düzenle',
                            'many'             => 'Bir Gün İçin Birden Fazla Rezervasyon',
                            'one'              => 'Birden Fazla Gün İçin Bir Rezervasyon',
                            'open'             => 'Açık',
                            'slot-add'         => 'Slot Ekle',
                            'slot-duration'    => 'Slot Süresi (Dakika)',
                            'slot-title'       => 'Slot Zaman Süresi',
                            'title'            => 'Varsayılan',
                            'unavailable'      => 'Mevcut Değil',

                            'modal'            => [
                                'slot' => [
                                    'add-title'  => 'Slot Ekle',
                                    'close'      => 'Kapat',
                                    'day'        => 'Gün',
                                    'edit-title' => 'Slotları Düzenle',
                                    'friday'     => 'Cuma',
                                    'from'       => 'Başlangıç',
                                    'from-day'   => 'Başlangıç Günü',
                                    'from-time'  => 'Başlangıç Zamanı',
                                    'monday'     => 'Pazartesi',
                                    'open'       => 'Açık',
                                    'saturday'   => 'Cumartesi',
                                    'save'       => 'Kaydet',
                                    'select'     => 'Seç',
                                    'status'     => 'Durum',
                                    'sunday'     => 'Pazar',
                                    'thursday'   => 'Perşembe',
                                    'to'         => 'Bitiş',
                                    'to-day'     => 'Bitiş Günü',
                                    'to-time'    => 'Bitiş Zamanı',
                                    'tuesday'    => 'Salı',
                                    'wednesday'  => 'Çarşamba',
                                    'week'       => ':day',
                                ],
                            ],
                        ],

                        'event' => [
                            'add'                => 'Bilet Ekle',
                            'delete'             => 'Sil',
                            'description'        => 'Açıklama',
                            'description-info'   => 'Mevcut bilet yok.',
                            'edit'               => 'Düzenle',
                            'name'               => 'Ad',
                            'price'              => 'Fiyat',
                            'qty'                => 'Miktar',
                            'special-price'      => 'Özel Fiyat',
                            'special-price-from' => 'Özel Fiyat Başlangıç',
                            'special-price-to'   => 'Özel Fiyat Bitiş',
                            'title'              => 'Biletler',
                            'valid-from'         => 'Geçerli Başlangıç',
                            'valid-until'        => 'Geçerli Bitiş',

                            'modal'              => [
                                'edit' => 'Biletleri Düzenle',
                                'save' => 'Kaydet',
                            ],
                        ],

                        'empty-info' => [
                            'tickets' => [
                                'add' => 'Bilet Ekle',
                            ],

                            'slots'   => [
                                'add'         => 'Slot Ekle',
                                'description' => 'Zaman Süresi ile Mevcut Slotlar.',
                            ],
                        ],

                        'rental' => [
                            'daily'                  => 'Günlük Bazda',
                            'daily-hourly'           => 'Her İkisi (Günlük ve Saatlik Bazda)',
                            'daily-price'            => 'Günlük Fiyat',
                            'hourly'                 => 'Saatlik Bazda',
                            'hourly-price'           => 'Saatlik Fiyat',
                            'title'                  => 'Kiralama Türü',

                            'same-slot-for-all-days' => [
                                'no'    => 'Hayır',
                                'title' => 'Tüm Günler İçin Aynı Slot',
                                'yes'   => 'Evet',
                            ],
                        ],

                        'slots' => [
                            'add'              => 'Slot Ekle',
                            'description-info' => 'Süre, slotlara göre oluşturulacak ve görüntülenecektir. Tüm slotlar arasında benzersiz olacak ve mağaza vitrinde görünecektir',
                            'save'             => 'Kaydet',
                            'title'            => 'Slot Zaman Süresi',
                            'unavailable'      => 'Mevcut Değil',

                            'action'           => [
                                'add' => 'Ekle',
                            ],

                            'modal'            => [
                                'slot' => [
                                    'friday'     => 'Cuma',
                                    'from'       => 'Başlangıç',
                                    'monday'     => 'Pazartesi',
                                    'saturday'   => 'Cumartesi',
                                    'sunday'     => 'Pazar',
                                    'thursday'   => 'Perşembe',
                                    'to'         => 'Bitiş',
                                    'tuesday'    => 'Salı',
                                    'wednesday'  => 'Çarşamba',
                                ],
                            ],
                        ],

                        'table' => [
                            'break-duration'            => 'Slotlar Arası Mola Süresi (Dakika)',
                            'guest-capacity'            => 'Misafir Kapasitesi',
                            'guest-limit'               => 'Masa Başına Misafir Limiti',
                            'prevent-scheduling-before' => 'Önceden Planlamayı Önle',
                            'slot-duration'             => 'Slot Süresi (Dakika)',

                            'charged-per'               => [
                                'guest'  => 'Misafir',
                                'table'  => 'Masa',
                                'title'  => 'Başına Ücret',
                            ],

                            'same-slot-for-all-days'    => [
                                'no'    => 'Hayır',
                                'title' => 'Tüm Günler İçin Aynı Slot',
                                'yes'   => 'Evet',
                            ],
                        ],

                        'type' => [
                            'appointment' => 'Randevu Rezervasyonu',
                            'default'     => 'Varsayılan Rezervasyon',
                            'event'       => 'Etkinlik Rezervasyonu',
                            'many'        => 'Birden Fazla',
                            'one'         => 'Bir',
                            'rental'      => 'Kiralama Rezervasyonu',
                            'table'       => 'Masa Rezervasyonu',
                            'title'       => 'Tür',
                        ],
                    ],

                    'downloadable' => [
                        'links' => [
                            'add-btn'     => 'Bağlantı Ekle',
                            'delete-btn'  => 'Sil',
                            'edit-btn'    => 'Düzenle',
                            'empty-info'  => 'Hızlı bir şekilde bağlantı oluşturmak için.',
                            'empty-title' => 'Bağlantı Ekle',
                            'file'        => 'Dosya : ',
                            'info'        => 'İndirilebilir ürün türü, e-kitaplar, yazılım uygulamaları, müzik, oyunlar vb. gibi dijital ürünleri satmanıza olanak tanır.',
                            'sample-file' => 'Örnek Dosya : ',
                            'sample-url'  => 'Örnek URL : ',
                            'title'       => 'İndirilebilir Bağlantılar',
                            'url'         => 'URL : ',

                            'update-create' => [
                                'downloads'   => 'İndirmeye İzin Verilen',
                                'file'        => 'Dosya',
                                'file-type'   => 'Dosya Türü',
                                'name'        => 'Başlık',
                                'price'       => 'Fiyat',
                                'sample'      => 'Örnek',
                                'sample-type' => 'Örnek Türü',
                                'save-btn'    => 'Kaydet',
                                'title'       => 'Bağlantı',
                                'url'         => 'URL',
                            ],
                        ],

                        'samples' => [
                            'add-btn'     => 'Örnek Ekle',
                            'delete-btn'  => 'Sil',
                            'edit-btn'    => 'Düzenle',
                            'empty-info'  => 'Hızlı bir şekilde örnek oluşturmak için.',
                            'empty-title' => 'Örnek Ekle',
                            'file'        => 'Dosya : ',
                            'info'        => 'İndirilebilir ürün türü, e-kitaplar, yazılım uygulamaları, müzik, oyunlar vb. gibi dijital ürünleri satmanıza olanak tanır.',
                            'title'       => 'İndirilebilir Örnekler',
                            'url'         => 'URL : ',

                            'update-create' => [
                                'file'        => 'Dosya',
                                'file-type'   => 'Dosya Türü',
                                'name'        => 'Başlık',
                                'save-btn'    => 'Kaydet',
                                'title'       => 'Bağlantı',
                                'url'         => 'URL',
                            ],
                        ],
                    ],
                ],
            ],

            'create-success'          => 'Ürün başarıyla oluşturuldu',
            'delete-failed'           => 'Ürün silme başarısız oldu',
            'delete-success'          => 'Ürün başarıyla silindi',
            'product-copied'          => 'Ürün başarıyla kopyalandı',
            'saved-inventory-message' => 'Ürün başarıyla kaydedildi',
            'update-success'          => 'Ürün başarıyla güncellendi',
        ],

        'attributes' => [
            'index' => [
                'create-btn' => 'Özellik Oluştur',
                'title'      => 'Özellikler',

                'datagrid' => [
                    'boolean'             => 'Boolean',
                    'channel-based'       => 'Kanal Bazlı',
                    'checkbox'            => 'Onay Kutusu',
                    'code'                => 'Kod',
                    'created-at'          => 'Oluşturulma Tarihi',
                    'date'                => 'Tarih',
                    'date-time'           => 'Tarih Zaman',
                    'delete'              => 'Sil',
                    'edit'                => 'Düzenle',
                    'false'               => 'Yanlış',
                    'file'                => 'Dosya',
                    'id'                  => 'Kimlik',
                    'image'               => 'Resim',
                    'locale-based'        => 'Yerel Temelli',
                    'mass-delete-success' => 'Seçilen Öznitelik Başarıyla Silindi',
                    'multiselect'         => 'Çoklu Seçim',
                    'name'                => 'Ad',
                    'price'               => 'Fiyat',
                    'required'            => 'Gerekli',
                    'select'              => 'Seçiniz',
                    'text'                => 'Metin',
                    'textarea'            => 'Metin Alanı',
                    'true'                => 'Doğru',
                    'type'                => 'Tür',
                    'unique'              => 'Benzersiz',
                ],
            ],

            'create' => [
                'add-attribute-options' => 'Özellik Seçenekleri Ekle',
                'add-option'            => 'Seçenek Ekle',
                'add-options-info'      => 'Hızlıca Özellik Seçeneği Kombinasyonları Oluşturmak İçin',
                'add-row'               => 'Satır Ekle',
                'admin'                 => 'Yönetici',
                'admin-name'            => 'Yönetici Adı',
                'back-btn'              => 'Geri',
                'boolean'               => 'Boolean',
                'checkbox'              => 'Onay Kutusu',
                'code'                  => 'Özellik Kodu',
                'color'                 => 'Renk',
                'configuration'         => 'Yapılandırma',
                'create-empty-option'   => 'Varsayılan Boş Seçeneği Oluştur',
                'date'                  => 'Tarih',
                'datetime'              => 'Tarih Saati',
                'decimal'               => 'Ondalık',
                'default-value'         => 'Varsayılan Değer',
                'email'                 => 'E-posta',
                'enable-wysiwyg'        => 'WYSIWYG Düzenleyiciyi Etkinleştir',
                'file'                  => 'Dosya',
                'general'               => 'Genel',
                'image'                 => 'Resim',
                'input-options'         => 'Giriş Seçenekleri',
                'input-validation'      => 'Giriş Doğrulama',
                'is-comparable'         => 'Özellik Karşılaştırılabilir',
                'is-configurable'       => 'Yapılandırılabilir Ürün Oluşturmak İçin Kullan',
                'is-filterable'         => 'Katmanlı Gezinmede Kullan',
                'is-required'           => 'Gerekli mi?',
                'is-unique'             => 'Benzersiz mi?',
                'is-visible-on-front'   => 'Ön Yüzde Görünür',
                'label'                 => 'Etiket',
                'multiselect'           => 'Çoklu Seçim',
                'no'                    => 'Hayır',
                'numeric'               => 'Sayı',
                'option-deleted'        => 'Seçenek başarıyla silindi',
                'options'               => 'Seçenekler',
                'position'              => 'Konum',
                'price'                 => 'Fiyat',
                'regex'                 => 'Regex',
                'regex-info'            => 'İfade çift tırnak içinde olmalıdır.',
                'save-btn'              => 'Özelliği Kaydet',
                'select'                => 'Seçenek',
                'select-type'           => 'Seçenek Özellik Türü',
                'swatch'                => 'Renk Örneği',
                'text'                  => 'Metin',
                'textarea'              => 'Metin Alanı',
                'title'                 => 'Özellik Ekle',
                'type'                  => 'Özellik Türü',
                'url'                   => 'URL',
                'use-in-flat'           => 'Ürün Düz Tabloya Ekle',
                'validations'           => 'Doğrulamalar',
                'value-per-channel'     => 'Kanala Göre Değer',
                'value-per-locale'      => 'Yerel Dile Göre Değer',
                'yes'                   => 'Evet',

                'option' => [
                    'color'    => 'Renk Örneği',
                    'dropdown' => 'Açılır Menü',
                    'image'    => 'Resim Örneği',
                    'save-btn' => 'Seçeneği Kaydet',
                    'text'     => 'Metin Örneği',
                ],
            ],

            'edit' => [
                'add-attribute-options' => 'Özellik Seçenekleri Ekle',
                'add-option'            => 'Seçenek Ekle',
                'add-options-info'      => 'Hızlıca Özellik Seçeneği Kombinasyonları Oluşturmak İçin',
                'add-row'               => 'Satır Ekle',
                'admin'                 => 'Yönetici',
                'admin-name'            => 'Yönetici Adı',
                'back-btn'              => 'Geri',
                'boolean'               => 'Boolean',
                'checkbox'              => 'Onay Kutusu',
                'code'                  => 'Özellik Kodu',
                'color'                 => 'Renk',
                'configuration'         => 'Yapılandırma',
                'create-empty-option'   => 'Varsayılan Boş Seçeneği Oluştur',
                'date'                  => 'Tarih',
                'datetime'              => 'Tarih Saati',
                'decimal'               => 'Ondalık',
                'default-value'         => 'Varsayılan Değer',
                'email'                 => 'E-posta',
                'enable-wysiwyg'        => 'WYSIWYG Düzenleyiciyi Etkinleştir',
                'file'                  => 'Dosya',
                'general'               => 'Genel',
                'image'                 => 'Resim',
                'input-options'         => 'Giriş Seçenekleri',
                'input-validation'      => 'Giriş Doğrulama',
                'is-comparable'         => 'Özellik Karşılaştırılabilir',
                'is-configurable'       => 'Yapılandırılabilir Ürün Oluşturmak İçin Kullan',
                'is-filterable'         => 'Katmanlı Gezinmede Kullan',
                'is-required'           => 'Gerekli mi?',
                'is-unique'             => 'Benzersiz mi?',
                'is-visible-on-front'   => 'Ön Yüzde Görünür',
                'label'                 => 'Etiket',
                'multiselect'           => 'Çoklu Seçim',
                'no'                    => 'Hayır',
                'numeric'               => 'Sayı',
                'option-deleted'        => 'Seçenek başarıyla silindi',
                'options'               => 'Seçenekler',
                'position'              => 'Konum',
                'price'                 => 'Fiyat',
                'regex'                 => 'Regex',
                'regex-info'            => 'İfade çift tırnak içinde olmalıdır.',
                'save-btn'              => 'Özelliği Kaydet',
                'select'                => 'Seçenek',
                'select-type'           => 'Seçenek Özellik Türü',
                'swatch'                => 'Renk Kartelası',
                'text'                  => 'Metin',
                'textarea'              => 'Metin Alanı',
                'title'                 => 'Özelliği Düzenle',
                'type'                  => 'Özellik Türü',
                'url'                   => 'URL',
                'use-in-flat'           => 'Ürün Düz Tabloya Ekle',
                'validations'           => 'Doğrulamalar',
                'value-per-channel'     => 'Kanal Başına Değer',
                'value-per-locale'      => 'Yerel Dil Başına Değer',
                'yes'                   => 'Evet',

                'option' => [
                    'color'    => 'Renk Kartelası',
                    'dropdown' => 'Açılır Menü',
                    'image'    => 'Resim Kartelası',
                    'save-btn' => 'Seçeneği Kaydet',
                    'text'     => 'Metin Kartelası',
                ],
            ],

            'create-success'    => 'Özellik Başarıyla Oluşturuldu',
            'delete-failed'     => 'Özellik Silinemedi',
            'delete-success'    => 'Özellik Başarıyla Silindi',
            'update-success'    => 'Özellik Başarıyla Güncellendi',
            'user-define-error' => 'Sistem Özelliği Silinemez',
        ],

        'categories' => [
            'index' => [
                'add-btn' => 'Kategori Oluştur',
                'title'   => 'Kategoriler',

                'datagrid' => [
                    'active'         => 'Aktif',
                    'delete'         => 'Sil',
                    'delete-success' => 'Seçilen :resource başarıyla silindi',
                    'edit'           => 'Düzenle',
                    'id'             => 'ID',
                    'inactive'       => 'Pasif',
                    'name'           => 'Adı',
                    'no-of-products' => 'Ürün Sayısı',
                    'position'       => 'Pozisyon',
                    'status'         => 'Menüde Görünür',
                    'update-status'  => 'Durumu Güncelle',
                ],
            ],

            'create' => [
                'add-banner'               => 'Banner Ekle',
                'add-logo'                 => 'Logo Ekle',
                'back-btn'                 => 'Geri',
                'banner'                   => 'Banner',
                'banner-size'              => 'Banner boyut oranı (1320px X 300px)',
                'description'              => 'Açıklama',
                'description-and-images'   => 'Açıklama ve Resimler',
                'description-only'         => 'Sadece Açıklama',
                'display-mode'             => 'Görüntüleme Modu',
                'enter-position'           => 'Pozisyonu Girin',
                'filterable-attributes'    => 'Filtreleme Özellikleri',
                'general'                  => 'Genel',
                'logo'                     => 'Logo',
                'logo-size'                => 'Logo çözünürlüğü (110px X 110px) olmalıdır',
                'meta-description'         => 'Meta Açıklama',
                'meta-keywords'            => 'Meta Anahtar Kelimeler',
                'meta-title'               => 'Meta Başlık',
                'name'                     => 'Adı',
                'parent-category'          => 'Ana Kategori',
                'position'                 => 'Pozisyon',
                'products-and-description' => 'Ürünler ve Açıklama',
                'products-only'            => 'Sadece Ürünler',
                'save-btn'                 => 'Kategoriyi Kaydet',
                'select-display-mode'      => 'Görüntüleme Modu Seçin',
                'seo-details'              => 'SEO Detayları',
                'settings'                 => 'Ayarlar',
                'slug'                     => 'URL Yolu',
                'title'                    => 'Yeni Kategori Ekle',
                'visible-in-menu'          => 'Menüde Görünür',
            ],

            'edit' => [
                'add-banner'               => 'Banner Ekle',
                'add-logo'                 => 'Logo Ekle',
                'back-btn'                 => 'Geri',
                'banner'                   => 'Banner',
                'banner-size'              => 'Banner boyut oranı (1320px X 300px)',
                'description'              => 'Açıklama',
                'description-and-images'   => 'Açıklama ve Resimler',
                'description-only'         => 'Sadece Açıklama',
                'display-mode'             => 'Görüntüleme Modu',
                'enter-position'           => 'Pozisyonu Girin',
                'filterable-attributes'    => 'Filtreleme Özellikleri',
                'general'                  => 'Genel',
                'logo'                     => 'Logo',
                'logo-size'                => 'Logo çözünürlüğü (110px X 110px) olmalıdır',
                'meta-description'         => 'Meta Açıklama',
                'meta-keywords'            => 'Meta Anahtar Kelimeler',
                'meta-title'               => 'Meta Başlık',
                'name'                     => 'Adı',
                'position'                 => 'Pozisyon *',
                'products-and-description' => 'Ürünler ve Açıklama',
                'products-only'            => 'Sadece Ürünler',
                'save-btn'                 => 'Kategoriyi Kaydet',
                'select-display-mode'      => 'Görüntüleme Modu Seçin',
                'select-parent-category'   => 'Ana Kategori Seçiniz *',
                'seo-details'              => 'SEO Detayları',
                'settings'                 => 'Ayarlar',
                'slug'                     => 'URL Yolu',
                'title'                    => 'Kategoriyi Düzenle',
                'visible-in-menu'          => 'Menüde Görünür',
            ],

            'category'             => 'Kategori',
            'create-success'       => 'Kategori başarıyla oluşturuldu.',
            'delete-category-root' => 'Ana kategori silinemez.',
            'delete-failed'        => 'Kategori silinirken bir hata oluştu',
            'delete-success'       => 'Kategori başarıyla silindi.',
            'update-success'       => 'Kategori başarıyla güncellendi.',
        ],

        'families' => [
            'index' => [
                'add'   => 'Özellik Ailesi Oluştur',
                'title' => 'Aileler',

                'datagrid' => [
                    'code'           => 'Kod',
                    'delete'         => 'Sil',
                    'delete-success' => 'Seçilen :resource başarıyla silindi',
                    'edit'           => 'Düzenle',
                    'id'             => 'ID',
                    'method-error'   => 'Hata! Yanlış yöntem tespit edildi, lütfen toplu işlem yapılandırmasını kontrol edin',
                    'name'           => 'Ad',
                    'no-resource'    => 'Eylem için yetersiz kaynak sağlandı',
                    'partial-action' => 'Bazı işlemler, :resource üzerindeki sistem kısıtlamaları nedeniyle gerçekleştirilemedi',
                    'update-success' => 'Seçilen :resource başarıyla güncellendi',
                ],
            ],

            'create' => [
                'add-group-btn'                    => 'Grup Ekle',
                'add-group-title'                  => 'Yeni Grup Ekle',
                'back-btn'                         => 'Geri',
                'code'                             => 'Kod',
                'column'                           => 'Sütun',
                'delete-group-btn'                 => 'Grubu Sil',
                'edit-group-info'                  => 'Grubu düzenlemek için çift tıklayın',
                'enter-code'                       => 'Kod Girin',
                'enter-name'                       => 'Ad Girin',
                'general'                          => 'Genel',
                'group-code-already-exists'        => 'Bir özellik grubu kodu zaten mevcut.',
                'group-contains-system-attributes' => 'Bu grup sistem özelliklerini içeriyor. Önce sistem özelliklerini başka bir gruba taşıyın ve tekrar deneyin.',
                'group-name-already-exists'        => 'Bir özellik grubu adı zaten mevcut.',
                'groups'                           => 'Gruplar',
                'groups-info'                      => 'Özellik aile gruplarını yönetin',
                'main-column'                      => 'Ana Sütun',
                'name'                             => 'Ad',
                'removal-not-possible'             => 'Sistem özelliklerini özellik ailesinden kaldıramazsınız.',
                'right-column'                     => 'Sağ Yan Sütun',
                'save-btn'                         => 'Özellik Ailesini Kaydet',
                'select-group'                     => 'Lütfen bir özellik grubu seçin.',
                'title'                            => 'Özellik Ailesi Oluştur',
                'unassigned-attributes'            => 'Atanmamış Özellikler',
                'unassigned-attributes-info'       => 'Bu özellikleri sütunlara veya gruplara eklemek için sürükleyin.',
            ],

            'edit' => [
                'add-group-btn'                    => 'Grup Ekle',
                'add-group-title'                  => 'Yeni Grup Ekle',
                'back-btn'                         => 'Geri',
                'code'                             => 'Kod',
                'column'                           => 'Sütun',
                'delete-group-btn'                 => 'Grubu Sil',
                'edit-group-info'                  => 'Grubu düzenlemek için çift tıklayın',
                'enter-code'                       => 'Kod Girin',
                'enter-name'                       => 'Ad Girin',
                'general'                          => 'Genel',
                'group-code-already-exists'        => 'Bir özellik grubu kodu zaten mevcut.',
                'group-contains-system-attributes' => 'Bu grup sistem özelliklerini içeriyor. Önce sistem özelliklerini başka bir gruba taşıyın ve tekrar deneyin.',
                'group-name-already-exists'        => 'Bir özellik grubu adı zaten mevcut.',
                'groups'                           => 'Gruplar',
                'groups-info'                      => 'Özellik aile gruplarını yönetin',
                'main-column'                      => 'Ana Sütun',
                'name'                             => 'Ad',
                'removal-not-possible'             => 'Sistem özelliklerini özellik ailesinden kaldıramazsınız.',
                'right-column'                     => 'Sağ Yan Sütun',
                'save-btn'                         => 'Özellik Ailesini Kaydet',
                'select-group'                     => 'Lütfen bir özellik grubu seçin.',
                'title'                            => 'Özellik Ailesini Düzenle',
                'unassigned-attributes'            => 'Atanmamış Özellikler',
                'unassigned-attributes-info'       => 'Bu özellikleri sütunlara veya gruplara eklemek için sürükleyin.',
            ],

            'attribute-family'        => 'Özellik Ailesi',
            'attribute-product-error' => 'Aile ürünlerde kullanılıyor.',
            'create-success'          => 'Aile başarıyla oluşturuldu.',
            'delete-failed'           => 'Aile silme işlemi sırasında bir hata oluştu.',
            'delete-success'          => 'Aile başarıyla silindi.',
            'family'                  => 'Aile',
            'last-delete-error'       => 'En az bir aile gereklidir.',
            'update-success'          => 'Aile başarıyla güncellendi.',
            'user-define-error'       => 'Sistem Özellik Ailesi silinemez',
        ],
    ],

    'customers' => [
        'customers' => [
            'index' => [
                'title'         => 'Müşteriler',
                'login-message' => ':customer_name olarak giriş yaptınız',

                'datagrid' => [
                    'active'         => 'Aktif',
                    'address'        => ':address  Adres(ler)',
                    'address-count'  => 'Adres Sayısı',
                    'channel'        => 'Kanal',
                    'delete'         => 'Sil',
                    'delete-success' => 'Seçilen veriler başarıyla silindi',
                    'email'          => 'E-posta',
                    'gender'         => 'Cinsiyet',
                    'group'          => 'Grup',
                    'id'             => 'Müşteri ID',
                    'id-value'       => 'Kimlik - :id',
                    'inactive'       => 'Pasif',
                    'method-error'   => 'Hata! Yanlış yöntem algılandı, lütfen kitlesel eylem yapılandırmasını kontrol edin',
                    'name'           => 'Müşteri Adı',
                    'no-resource'    => 'Eylem için sağlanan kaynak yetersiz',
                    'order'          => ':order Sipariş(ler)',
                    'order-count'    => 'Sipariş Sayısı',
                    'order-pending'  => 'Müşterinin bekleyen siparişi var',
                    'partial-action' => 'Bazı eylemler, :resource üzerindeki sınırlı sistem kısıtlamaları nedeniyle gerçekleştirilmedi',
                    'phone'          => 'İletişim Numarası',
                    'revenue'        => 'Gelir',
                    'status'         => 'Durum',
                    'suspended'      => 'Askıya Alındı',
                    'update-status'  => 'Durumu Güncelle',
                    'update-success' => 'Seçilen Müşteriler başarıyla güncellendi',
                ],

                'create' => [
                    'contact-number'        => 'İletişim Numarası',
                    'create-btn'            => 'Müşteri Oluştur',
                    'create-success'        => 'Müşteri başarıyla oluşturuldu',
                    'customer-group'        => 'Müşteri Grubu',
                    'date-of-birth'         => 'Doğum Tarihi',
                    'email'                 => 'E-posta',
                    'female'                => 'Kadın',
                    'first-name'            => 'Adı',
                    'gender'                => 'Cinsiyet',
                    'last-name'             => 'Soyadı',
                    'male'                  => 'Erkek',
                    'other'                 => 'Diğer',
                    'save-btn'              => 'Müşteriyi Kaydet',
                    'select-customer-group' => 'Müşteri Grubu Seçin',
                    'select-gender'         => 'Cinsiyet Seçin',
                    'title'                 => 'Yeni Müşteri Oluştur',
                ],
            ],

            'view' => [
                'account-delete-confirmation' => 'Bu hesabı silmek istediğinizden emin misiniz?',
                'active'                      => 'Aktif',
                'address-delete-confirmation' => 'Bu adresi silmek istediğinizden emin misiniz?',
                'back-btn'                    => 'Geri',
                'create-order'                => 'Sipariş Oluştur',
                'customer'                    => 'Müşteri',
                'date-of-birth'               => 'Doğum Tarihi - :dob',
                'default-address'             => 'Varsayılan Adres',
                'delete-account'              => 'Hesabı Sil',
                'delete'                      => 'Sil',
                'email'                       => 'E-posta - :email',
                'empty-description'           => 'Müşteri için yeni adresler oluşturun',
                'empty-title'                 => 'Müşteri Adresi Ekle',
                'gender'                      => 'Cinsiyet - :gender',
                'group'                       => 'Grup - :group_code',
                'inactive'                    => 'Pasif',
                'login-as-customer'           => 'Müşteri olarak giriş yap',
                'note-created-success'        => 'Not Başarıyla Oluşturuldu',
                'order-create-confirmation'   => 'Bu müşteri için sipariş oluşturmak istediğinizden emin misiniz?',
                'phone'                       => 'Telefon - :phone',
                'set-as-default'              => 'Varsayılan Olarak Ayarla',
                'suspended'                   => 'Askıya Alındı',
                'title'                       => 'Müşteri Görünümü',

                'address' => [
                    'count' => 'Adresler (:count)',

                    'create' => [
                        'city'               => 'Şehir',
                        'company-name'       => 'Şirket Adı',
                        'country'            => 'Ülke',
                        'create-btn'         => 'Oluştur',
                        'create-address-btn' => 'Yeni Adres Ekle',
                        'default-address'    => 'Varsayılan Adres',
                        'email'              => 'E-posta',
                        'first-name'         => 'Adı',
                        'last-name'          => 'Soyadı',
                        'phone'              => 'Telefon',
                        'post-code'          => 'Posta Kodu',
                        'save-btn-title'     => 'Adresi Kaydet',
                        'select-country'     => 'Ülke Seçin',
                        'state'              => 'Eyalet',
                        'street-address'     => 'Cadde Adresi',
                        'title'              => 'Adres Oluştur',
                        'vat-id'             => 'KDV Kimlik Numarası',
                    ],

                    'edit' => [
                        'city'            => 'Şehir',
                        'company-name'    => 'Şirket Adı',
                        'country'         => 'Ülke',
                        'default-address' => 'Varsayılan Adres',
                        'edit-btn'        => 'Düzenle',
                        'email'           => 'E-posta',
                        'first-name'      => 'Adı',
                        'last-name'       => 'Soyadı',
                        'phone'           => 'Telefon',
                        'post-code'       => 'Posta Kodu',
                        'save-btn-title'  => 'Adresi Kaydet',
                        'select-country'  => 'Ülke Seçin',
                        'state'           => 'Eyalet',
                        'street-address'  => 'Cadde Adresi',
                        'title'           => 'Adresi Düzenle',
                        'vat-id'          => 'KDV Kimlik Numarası',
                    ],

                    'address-delete-success' => 'Adres Başarıyla Silindi',
                    'create-success'         => 'Adres Başarıyla Oluşturuldu',
                    'set-default-success'    => 'Varsayılan Adres Başarıyla Güncellendi',
                    'success-mass-delete'    => 'Adres Toplu Silme Başarıyla Gerçekleştirildi',
                    'update-success'         => 'Adres Başarıyla Güncellendi',
                ],

                'datagrid' => [
                    'invoices' => [
                        'empty-invoice'  => 'İnceleme Yok',
                        'increment-id'   => 'Fatura ID',
                        'invoice-amount' => 'Fatura Tutarı',
                        'invoice-date'   => 'Fatura Tarihi',
                        'order-id'       => 'Sipariş ID',
                        'view'           => 'Görüntüle',
                    ],

                    'orders' => [
                        'canceled'        => 'İptal Edildi',
                        'channel-name'    => 'Kanal Adı',
                        'closed'          => 'Kapatıldı',
                        'completed'       => 'Tamamlandı',
                        'customer-name'   => 'Müşteri Adı',
                        'date'            => 'Tarih',
                        'empty-order'     => 'Sipariş Yok',
                        'email'           => 'E-posta',
                        'fraud'           => 'Sahtekarlık',
                        'grand-total'     => 'Toplam Tutar',
                        'location'        => 'Konum',
                        'order-id'        => 'Sipariş ID',
                        'pay-via'         => 'Ödeme Yoluyla',
                        'pending'         => 'Beklemede',
                        'pending-payment' => 'Ödeme Bekliyor',
                        'processing'      => 'İşleniyor',
                        'status'          => 'Durum',
                        'view'            => 'Görüntüle',
                    ],

                    'reviews' => [
                        'approved'      => 'Onaylandı',
                        'comment'       => 'Yorum',
                        'created-at'    => 'Oluşturulma Tarihi',
                        'disapproved'   => 'Onaylanmadı',
                        'empty-reviews' => 'Fatura Yok',
                        'id'            => 'ID',
                        'invoice-date'  => 'Fatura Tarihi',
                        'pending'       => 'Beklemede',
                        'product-id'    => 'Ürün ID',
                        'product-name'  => 'Ürün Adı',
                        'rating'        => 'Puan',
                        'status'        => 'Durum',
                        'title'         => 'Başlık',
                    ],
                ],

                'edit' => [
                    'contact-number'        => 'İletişim Numarası',
                    'customer-group'        => 'Müşteri Grubu',
                    'date-of-birth'         => 'Doğum Tarihi',
                    'edit-btn'              => 'Düzenle',
                    'email'                 => 'E-posta',
                    'female'                => 'Kadın',
                    'first-name'            => 'Adı',
                    'gender'                => 'Cinsiyet',
                    'last-name'             => 'Soyadı',
                    'male'                  => 'Erkek',
                    'other'                 => 'Diğer',
                    'save-btn'              => 'Müşteriyi Kaydet',
                    'select-customer-group' => 'Müşteri Grubu Seçin',
                    'select-gender'         => 'Cinsiyet Seçin',
                    'status'                => 'Durum',
                    'suspended'             => 'Askıya Alındı',
                    'title'                 => 'Müşteriyi Düzenle',
                ],

                'invoices' => [
                    'count'        => 'Faturalar (:count)',
                    'increment-id' => '# :increment_id',
                ],

                'notes' => [
                    'add-note'              => 'Not Ekle',
                    'customer-not-notified' => ':date | Müşteri <b>Bildirilmedi</b>',
                    'customer-notified'     => ':date | Müşteri <b>Bildirildi</b>',
                    'note'                  => 'Not',
                    'note-placeholder'      => 'Buraya Notunuzu Yazın',
                    'notify-customer'       => 'Müşteriyi Bilgilendir',
                    'submit-btn-title'      => 'Notu Gönder',
                ],

                'orders' => [
                    'count'         => 'Siparişler (:count)',
                    'increment-id'  => '# :increment_id',
                    'total-revenue' => 'Toplam Gelir - :revenue',
                ],

                'reviews' => [
                    'id'    => 'ID - :id',
                    'count' => 'Değerlendirmeler (:count)',
                ],

                'cart' => [
                    'delete-success' => 'Ürün başarıyla silindi.',
                ],

                'wishlist' => [
                    'delete-success' => 'İstek listesi öğesi başarıyla silindi.',
                ],

                'compare' => [
                    'delete-success' => 'Karşılaştırma öğesi başarıyla silindi.',
                ],
            ],

            'delete-failed'  => 'Müşteri Silme Başarısız',
            'delete-success' => 'Müşteri Başarıyla Silindi',
            'order-pending'  => 'Sipariş Beklemede',
            'update-success' => 'Müşteri Başarıyla Güncellendi',
        ],

        'groups' => [
            'index' => [
                'title' => 'Gruplar',

                'create' => [
                    'code'       => 'Kod',
                    'create-btn' => 'Grup Oluştur',
                    'name'       => 'Adı',
                    'save-btn'   => 'Grubu Kaydet',
                    'success'    => 'Grup başarıyla oluşturuldu',
                    'title'      => 'Yeni Grup Oluştur',
                ],

                'edit' => [
                    'delete-failed'  => 'Grup Silme Başarısız',
                    'delete-success' => 'Grup Başarıyla Silindi',
                    'group-default'  => 'Varsayılan Grup Silinemez',
                    'success'        => 'Grup Başarıyla Güncellendi',
                    'title'          => 'Grubu Düzenle',
                ],

                'datagrid' => [
                    'code'   => 'Kod',
                    'delete' => 'Sil',
                    'edit'   => 'Düzenle',
                    'id'     => 'ID',
                    'name'   => 'Adı',
                ],
            ],
        ],

        'gdpr' => [
            'index' => [
                'title' => 'GDPR Talebi',

                'datagrid' => [
                    'completed'     => 'Tamamlandı',
                    'created-at'    => 'Oluşturulma Tarihi',
                    'customer-name' => 'Müşteri Adı',
                    'declined'      => 'Reddedildi',
                    'delete'        => 'Sil',
                    'edit'          => 'Düzenle',
                    'id'            => 'ID',
                    'message'       => 'Mesaj',
                    'pending'       => 'Beklemede',
                    'processing'    => 'İşleniyor',
                    'revoked'       => 'İptal edildi',
                    'status'        => 'Durum',
                    'type'          => 'Tür',
                ],

                'modal' => [
                    'completed'     => 'Tamamlandı',
                    'declined'      => 'Reddedildi',
                    'message'       => 'Mesaj',
                    'pending'       => 'Beklemede',
                    'processing'    => 'İşleniyor',
                    'revoked'       => 'İptal edildi',
                    'save-btn'      => 'Kaydet',
                    'status'        => 'Durum',
                    'title'         => 'GDPR Veri Talebini Düzenle',
                    'type'          => 'Tür',
                ],

                'update-success'              => 'Veri Talebi başarıyla güncellendi ve Müşteriye E-posta Gönderildi.',
                'delete-success'              => 'Veri Talebi başarıyla silindi.',
                'attribute-reason-error'      => 'Silinemiyor.',
                'update-success-unsent-email' => 'Veri Talebi başarıyla güncellendi ancak Müşteriye E-posta gönderilemedi.',
            ],
        ],

        'reviews' => [
            'index' => [
                'date'        => 'Tarih',
                'description' => 'Açıklama',
                'id'          => 'Kimlik',
                'name'        => 'Ad',
                'product'     => 'Ürün',
                'rating'      => 'Değerlendirme',
                'status'      => 'Durum',
                'title'       => 'Değerlendirmeler',

                'edit' => [
                    'approved'       => 'Onaylandı',
                    'customer'       => 'Müşteri',
                    'date'           => 'Tarih',
                    'disapproved'    => 'Onaylanmadı',
                    'id'             => 'Kimlik',
                    'images'         => 'Resimler',
                    'pending'        => 'Beklemede',
                    'product'        => 'Ürün',
                    'rating'         => 'Değerlendirme',
                    'review-comment' => 'Yorum',
                    'review-title'   => 'Başlık',
                    'save-btn'       => 'Kaydet',
                    'status'         => 'Durum',
                    'title'          => 'Değerlendirmeyi Düzenle',
                    'update-success' => 'Başarıyla Güncellendi',
                ],

                'datagrid' => [
                    'approved'            => 'Onaylandı',
                    'comment'             => 'Yorum',
                    'customer-names'      => 'Ad',
                    'date'                => 'Tarih',
                    'delete'              => 'Sil',
                    'delete-success'      => 'Değerlendirme Başarıyla Silindi',
                    'disapproved'         => 'Onaylanmadı',
                    'edit'                => 'Düzenle',
                    'id'                  => 'Kimlik',
                    'mass-delete-error'   => 'Bir şeyler yanlış gitti',
                    'mass-delete-success' => 'Seçilen Değerlendirmeler Başarıyla Silindi',
                    'mass-update-success' => 'Seçilen Değerlendirmeler Başarıyla Güncellendi',
                    'pending'             => 'Beklemede',
                    'product'             => 'Ürün',
                    'rating'              => 'Değerlendirme',
                    'review-id'           => 'Kimlik - :review_id',
                    'status'              => 'Durum',
                    'title'               => 'Başlık',
                    'update-status'       => 'Durumu Güncelle',
                ],
            ],
        ],
    ],

    'marketing' => [
        'communications' => [
            'templates' => [
                'index' => [
                    'create-btn' => 'Şablon Oluştur',
                    'title'      => 'E-posta Şablonları',

                    'datagrid' => [
                        'active'   => 'Aktif',
                        'draft'    => 'Taslak',
                        'id'       => 'Kimlik',
                        'inactive' => 'Pasif',
                        'name'     => 'Adı',
                        'status'   => 'Durum',
                    ],
                ],

                'create' => [
                    'active'         => 'Aktif',
                    'back-btn'       => 'Geri',
                    'content'        => 'İçerik',
                    'create-success' => 'E-posta şablonu başarıyla oluşturuldu.',
                    'draft'          => 'Taslak',
                    'general'        => 'Genel',
                    'inactive'       => 'Pasif',
                    'name'           => 'Adı',
                    'save-btn'       => 'Şablonu Kaydet',
                    'select-status'  => 'Durumu Seçiniz',
                    'status'         => 'Durum',
                    'title'          => 'Şablon Oluştur',
                ],

                'edit' => [
                    'active'         => 'Aktif',
                    'back-btn'       => 'Geri',
                    'content'        => 'İçerik*',
                    'draft'          => 'Taslak',
                    'general'        => 'Genel',
                    'inactive'       => 'Pasif',
                    'name'           => 'Adı',
                    'save-btn'       => 'Şablonu Kaydet',
                    'status'         => 'Durum',
                    'title'          => 'Şablonu Düzenle',
                    'update-success' => 'Başarıyla güncellendi',
                ],

                'email-template' => 'E-posta Şablonu',
                'delete-success' => 'Şablon başarıyla silindi',
                'delete-failed'  => ':name Silme Başarısız',
            ],

            'campaigns' => [
                'index' => [
                    'create-btn' => 'Kampanya Oluştur',
                    'title'      => 'Kampanyalar',

                    'datagrid' => [
                        'active'   => 'Aktif',
                        'delete'   => 'Sil',
                        'edit'     => 'Düzenle',
                        'id'       => 'ID',
                        'inactive' => 'Pasif',
                        'name'     => 'Adı',
                        'status'   => 'Durum',
                        'subject'  => 'Konu',
                    ],
                ],

                'create' => [
                    'active'          => 'Aktif',
                    'back-btn'        => 'Geri',
                    'channel'         => 'Kanal',
                    'customer-group'  => 'Müşteri Grubu',
                    'email-template'  => 'E-posta Şablonu',
                    'event'           => 'Etkinlik',
                    'general'         => 'Genel',
                    'inactive'        => 'Pasif',
                    'name'            => 'Adı',
                    'save-btn'        => 'Kampanyayı Kaydet',
                    'select-channel'  => 'Kanal seç',
                    'select-event'    => 'Etkinlik Seçin',
                    'select-group'    => 'Grup seç',
                    'select-status'   => 'Durum Seçin',
                    'select-template' => 'Şablon Seçin',
                    'setting'         => 'Ayar',
                    'status'          => 'Durum',
                    'subject'         => 'Konu',
                    'title'           => 'Kampanya Oluştur',
                ],

                'edit' => [
                    'active'          => 'Aktif',
                    'audience'        => 'Hedef Kitle',
                    'back-btn'        => 'Geri',
                    'channel'         => 'Kanal',
                    'customer-group'  => 'Müşteri Grubu',
                    'email-template'  => 'E-posta Şablonu',
                    'event'           => 'Etkinlik',
                    'general'         => 'Genel',
                    'inactive'        => 'Pasif',
                    'name'            => 'Adı',
                    'save-btn'        => 'Kampanyayı Kaydet',
                    'select-event'    => 'Etkinlik Seçin',
                    'select-status'   => 'Durum Seçin',
                    'select-template' => 'Şablon Seçin',
                    'status'          => 'Durum',
                    'subject'         => 'Konu',
                    'title'           => 'Kampanya Düzenle',
                ],

                'create-success' => 'Kampanya başarıyla oluşturuldu.',
                'delete-failed'  => ':name Silme başarısız',
                'delete-success' => 'Kampanya başarıyla silindi',
                'email-campaign' => 'E-posta Kampanyası',
                'update-success' => 'Kampanya başarıyla güncellendi.',
            ],

            'events' => [
                'index' => [
                    'create-btn' => 'Etkinlik Oluştur',
                    'event'      => 'Etkinlik',
                    'title'      => 'Etkinlikler',

                    'datagrid' => [
                        'actions' => 'Aksiyonlar',
                        'date'    => 'Tarih',
                        'delete'  => 'Sil',
                        'edit'    => 'Düzenle',
                        'id'      => 'ID',
                        'name'    => 'Adı',
                    ],

                    'create' => [
                        'date'           => 'Tarih',
                        'delete-warning' => 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?',
                        'description'    => 'Açıklama',
                        'general'        => 'Genel',
                        'name'           => 'Adı',
                        'save-btn'       => 'Etkinliği Kaydet',
                        'success'        => 'Etkinlik Başarıyla Oluşturuldu',
                        'title'          => 'Etkinlik Oluştur',
                    ],

                    'edit' => [
                        'success' => 'Etkinlik Başarıyla Güncellendi',
                        'title'   => 'Etkinlikleri Düzenle',
                    ],
                ],

                'delete-failed'  => ':name Silme Başarısız',
                'delete-success' => 'Etkinlikler Başarıyla Silindi',
                'edit-error'     => 'Etkinlik Düzenlenemiyor',
            ],

            'subscribers' => [
                'index' => [
                    'title' => 'Bülten Abonelikleri',

                    'datagrid' => [
                        'actions'    => 'Aksiyonlar',
                        'delete'     => 'Sil',
                        'edit'       => 'Düzenle',
                        'email'      => 'E-posta',
                        'false'      => 'Yanlış',
                        'id'         => 'ID',
                        'subscribed' => 'Abone',
                        'true'       => 'Doğru',
                    ],

                    'edit' => [
                        'back-btn'      => 'Geri',
                        'email'         => 'E-posta',
                        'false'         => 'Yanlış',
                        'save-btn'      => 'Aboneyi Kaydet',
                        'subscribed'    => 'Abone',
                        'success'       => 'Bülten Aboneliği Başarıyla Güncellendi',
                        'title'         => 'Bülten Aboneliğini Düzenle',
                        'true'          => 'Doğru',
                        'update-failed' => 'Bülten Aboneliği Güncellenemedi',
                    ],
                ],

                'delete-failed'  => 'Abone Silme Başarısız',
                'delete-success' => 'Abone Başarıyla Silindi',
                'delete-warning' => 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?',
            ],
        ],

        'promotions' => [
            'index' => [
                'cart-rule-title'    => 'Sepet Kuralları',
                'catalog-rule-title' => 'Katalog Kuralları',
            ],

            'cart-rules' => [
                'index' => [
                    'create-btn' => 'Sepet Kuralı Oluştur',
                    'title'      => 'Sepet Kuralları',

                    'datagrid' => [
                        'active'      => 'Aktif',
                        'copy'        => 'Kopyala',
                        'copy-of'     => ':value',
                        'coupon-code' => 'Kupon Kodu',
                        'delete'      => 'Sil',
                        'draft'       => 'Taslak',
                        'edit'        => 'Düzenle',
                        'end'         => 'Bitiş',
                        'id'          => 'ID',
                        'inactive'    => 'Pasif',
                        'name'        => 'Adı',
                        'priority'    => 'Öncelik',
                        'start'       => 'Başlangıç',
                        'status'      => 'Durum',
                    ],
                ],

                'create' => [
                    'action-type'                               => 'Aksiyon Türü',
                    'actions'                                   => 'Aksiyonlar',
                    'add-condition'                             => 'Koşul Ekle',
                    'additional'                                => 'Ek',
                    'all-conditions-true'                       => 'Tüm Koşullar Doğru',
                    'any-conditions-true'                       => 'Herhangi Bir Koşul Doğru',
                    'apply-to-shipping'                         => 'Kargo Üzerine Uygula',
                    'attribute-family'                          => 'Özellik Ailesi',
                    'attribute-name-children-only'              => 'Özellik Adı (Sadece Çocuklar)',
                    'attribute-name-parent-only'                => 'Özellik Adı (Sadece Ebeveynler)',
                    'auto-generate-coupon'                      => 'Kupon Otomatik Üret',
                    'back-btn'                                  => 'Geri',
                    'buy-x-get-y-free'                          => 'X Ürün Al, Y Ürün Bedava',
                    'buy-x-quantity'                            => 'X Miktar Al',
                    'cart-attribute'                            => 'Sepet Özelliği',
                    'cart-item-attribute'                       => 'Sepet Ürün Özelliği',
                    'categories'                                => 'Kategoriler',
                    'channels'                                  => 'Kanallar',
                    'children-categories'                       => 'Kategoriler (Sadece Çocuklar)',
                    'choose-condition-to-add'                   => 'Eklemek için bir koşul seçin',
                    'condition-type'                            => 'Koşul Türü',
                    'conditions'                                => 'Koşullar',
                    'contain'                                   => 'İçerir',
                    'contains'                                  => 'İçerir',
                    'coupon-code'                               => 'Kupon Kodu',
                    'coupon-type'                               => 'Kupon Türü',
                    'create-success'                            => 'Sepet kuralı başarıyla oluşturuldu',
                    'customer-groups'                           => 'Müşteri Grupları',
                    'description'                               => 'Açıklama',
                    'discount-amount'                           => 'İndirim Tutarı',
                    'does-not-contain'                          => 'İçermez',
                    'end-of-other-rules'                        => 'Diğer Kuralların Sonu',
                    'equals-or-greater-than'                    => 'Eşit veya Büyük',
                    'equals-or-less-than'                       => 'Eşit veya Küçük',
                    'fixed-amount'                              => 'Sabit Miktar',
                    'fixed-amount-whole-cart'                   => 'Sabit Miktar Tüm Sepet',
                    'free-shipping'                             => 'Ücretsiz Kargo',
                    'from'                                      => 'Başlangıç',
                    'general'                                   => 'Genel',
                    'greater-than'                              => 'Büyük',
                    'is-equal-to'                               => 'Eşittir',
                    'is-not-equal-to'                           => 'Eşit Değil',
                    'less-than'                                 => 'Küçük',
                    'marketing-time'                            => 'Pazarlama Zamanı',
                    'maximum-quantity-allowed-to-be-discounted' => 'İndirim yapılmasına izin verilen maksimum miktar',
                    'name'                                      => 'Adı',
                    'no'                                        => 'Hayır',
                    'no-coupon'                                 => 'Kupon Yok',
                    'parent-categories'                         => 'Kategoriler (Sadece Ebeveynler)',
                    'payment-method'                            => 'Ödeme Yöntemi',
                    'percentage-product-price'                  => 'Ürün Fiyatına Yüzde',
                    'price-in-cart'                             => 'Sepetteki Fiyat',
                    'priority'                                  => 'Öncelik',
                    'product-attribute'                         => 'Ürün Özelliği',
                    'qty-in-cart'                               => 'Sepetteki Miktar',
                    'save-btn'                                  => 'Sepet Kuralını Kaydet',
                    'settings'                                  => 'Ayarlar',
                    'shipping-country'                          => 'Kargo Ülkesi',
                    'shipping-method'                           => 'Kargo Yöntemi',
                    'shipping-postcode'                         => 'Kargo Posta Kodu',
                    'shipping-state'                            => 'Kargo Eyaleti',
                    'specific-coupon'                           => 'Belirli Kupon',
                    'status'                                    => 'Durum',
                    'subtotal'                                  => 'Ara Toplam',
                    'title'                                     => 'Sepet Kuralı Oluştur',
                    'to'                                        => 'Bitiş',
                    'total-items-qty'                           => 'Toplam Ürün Adeti',
                    'total-weight'                              => 'Toplam Ağırlık',
                    'uses-per-coupon'                           => 'Kupon Başına Kullanım Sayısı',
                    'uses-per-customer'                         => 'Müşteri Başına Kullanım Sayısı',
                    'uses-per-customer-control-info'            => 'Yalnızca giriş yapmış müşteriler için kullanılacaktır.',
                    'yes'                                       => 'Evet',
                ],

                'edit' => [
                    'action-type'                               => 'Aksiyon Türü',
                    'actions'                                   => 'Aksiyonlar',
                    'add-condition'                             => 'Koşul Ekle',
                    'additional'                                => 'Ek',
                    'all-conditions-true'                       => 'Tüm Koşullar Doğru',
                    'alphabetical'                              => 'Alfabetik',
                    'alphanumeric'                              => 'Alfasayısal',
                    'any-conditions-true'                       => 'Herhangi Bir Koşul Doğru',
                    'apply-to-shipping'                         => 'Kargo Üzerine Uygula',
                    'attribute-family'                          => 'Özellik Ailesi',
                    'attribute-name-children-only'              => 'Sadece Çocuklar için Özellik Adı',
                    'attribute-name-parent-only'                => 'Sadece Ebeveynler için Özellik Adı',
                    'auto-generate-coupon'                      => 'Otomatik Kupon Oluştur',
                    'back-btn'                                  => 'Geri',
                    'buy-x-get-y-free'                          => 'X Ürün Al Y Ürün Bedava',
                    'buy-x-quantity'                            => 'X Miktar Al',
                    'cart-attribute'                            => 'Sepet Özelliği',
                    'cart-item-attribute'                       => 'Sepet Ürün Özelliği',
                    'categories'                                => 'Kategoriler',
                    'channels'                                  => 'Kanallar',
                    'children-categories'                       => 'Çocuk Kategorileri',
                    'choose-condition-to-add'                   => 'Eklemek için bir koşul seçin',
                    'code-format'                               => 'Kod Formatı',
                    'code-prefix'                               => 'Kod Öneki',
                    'code-suffix'                               => 'Kod Soneki',
                    'condition-type'                            => 'Koşul Türü',
                    'conditions'                                => 'Koşullar',
                    'contain'                                   => 'İçerir',
                    'contains'                                  => 'İçerir',
                    'coupon-code'                               => 'Kupon Kodu',
                    'coupon-length'                             => 'Kupon Uzunluğu',
                    'coupon-qty'                                => 'Kupon Miktarı',
                    'coupon-type'                               => 'Kupon Türü',
                    'customer-group'                            => 'Müşteri Grubu',
                    'customer-groups'                           => 'Müşteri Grupları',
                    'description'                               => 'Açıklama',
                    'discount-amount'                           => 'İndirim Tutarı',
                    'does-not-contain'                          => 'İçermez',
                    'end-of-other-rules'                        => 'Diğer Kuralların Sonu',
                    'equals-or-greater-than'                    => 'Eşit veya Büyük',
                    'equals-or-less-than'                       => 'Eşit veya Küçük',
                    'fixed-amount'                              => 'Sabit Tutar',
                    'fixed-amount-whole-cart'                   => 'Sabit Tutar Tüm Sepet',
                    'free-shipping'                             => 'Ücretsiz Kargo',
                    'from'                                      => 'Başlangıç',
                    'general'                                   => 'Genel',
                    'generate'                                  => 'Oluştur',
                    'greater-than'                              => 'Büyük',
                    'is-equal-to'                               => 'Eşittir',
                    'is-not-equal-to'                           => 'Eşit Değil',
                    'less-than'                                 => 'Küçük',
                    'marketing-time'                            => 'Pazarlama Zamanı',
                    'maximum-quantity-allowed-to-be-discounted' => 'İndirim yapılmasına izin verilen maksimum miktar',
                    'name'                                      => 'Adı',
                    'no'                                        => 'Hayır',
                    'no-coupon'                                 => 'Kupon Yok',
                    'numeric'                                   => 'Sayısal',
                    'parent-categories'                         => 'Ana Kategoriler',
                    'payment-method'                            => 'Ödeme Yöntemi',
                    'percentage-product-price'                  => 'Yüzde Ürün Fiyatı',
                    'price-in-cart'                             => 'Sepetteki Fiyat',
                    'priority'                                  => 'Öncelik',
                    'product-attribute'                         => 'Ürün Özelliği',
                    'qty-in-cart'                               => 'Sepetteki Miktar',
                    'save-btn'                                  => 'Kuralı Kaydet',
                    'settings'                                  => 'Ayarlar',
                    'shipping-country'                          => 'Kargo Ülkesi',
                    'shipping-method'                           => 'Kargo Yöntemi',
                    'shipping-postcode'                         => 'Kargo Posta Kodu',
                    'shipping-state'                            => 'Kargo Eyaleti',
                    'specific-coupon'                           => 'Belirli Kupon',
                    'status'                                    => 'Durum',
                    'subtotal'                                  => 'Ara Toplam',
                    'title'                                     => 'Kuralı Düzenle',
                    'to'                                        => 'Bitiş',
                    'total-items-qty'                           => 'Toplam Ürün Adeti',
                    'total-weight'                              => 'Toplam Ağırlık',
                    'update-success'                            => 'Sepet kuralı başarıyla güncellendi',
                    'uses-per-coupon'                           => 'Kupon Başına Kullanım Sayısı',
                    'uses-per-customer'                         => 'Müşteri Başına Kullanım Sayısı',
                    'uses-per-customer-control-info'            => 'Yalnızca giriş yapmış müşteriler için kullanılacaktır.',
                    'yes'                                       => 'Evet',
                ],

                'delete-failed'  => 'Sepet Kuralı Silme Başarısız',
                'delete-success' => 'Sepet Kuralı Başarıyla Silindi',
            ],

            'catalog-rules' => [
                'index' => [
                    'create-btn' => 'Katalog Kuralı Oluştur',
                    'title'      => 'Katalog Kuralları',

                    'datagrid' => [
                        'active'   => 'Aktif',
                        'delete'   => 'Sil',
                        'edit'     => 'Düzenle',
                        'end'      => 'Bitiş',
                        'id'       => 'ID',
                        'inactive' => 'Pasif',
                        'name'     => 'Adı',
                        'priority' => 'Öncelik',
                        'start'    => 'Başlangıç',
                        'status'   => 'Durum',
                    ],
                ],

                'create' => [
                    'action-type'              => 'Aksiyon Türü',
                    'actions'                  => 'Aksiyonlar',
                    'add-condition'            => 'Koşul Ekle',
                    'all-conditions-true'      => 'Tüm Koşullar Doğru',
                    'any-conditions-true'      => 'Herhangi Bir Koşul Doğru',
                    'attribute-family'         => 'Özellik Ailesi',
                    'back-btn'                 => 'Geri',
                    'categories'               => 'Kategoriler',
                    'channels'                 => 'Kanallar',
                    'choose-condition-to-add'  => 'Eklemek İçin Koşul Seçin',
                    'condition-type'           => 'Koşul Türü',
                    'conditions'               => 'Koşullar',
                    'contain'                  => 'İçerir',
                    'contains'                 => 'İçerir',
                    'customer-groups'          => 'Müşteri Grupları',
                    'description'              => 'Açıklama',
                    'discount-amount'          => 'İndirim Miktarı',
                    'does-not-contain'         => 'İçermez',
                    'end-other-rules'          => 'Diğer Kuralları Sonlandır',
                    'equals-or-greater-than'   => 'Eşit veya Büyük',
                    'equals-or-less-than'      => 'Eşit veya Küçük',
                    'fixed-amount'             => 'Sabit Miktar',
                    'from'                     => 'Başlangıç',
                    'general'                  => 'Genel',
                    'greater-than'             => 'Büyük',
                    'is-equal-to'              => 'Eşittir',
                    'is-not-equal-to'          => 'Eşit Değil',
                    'less-than'                => 'Küçük',
                    'marketing-time'           => 'Pazarlama Zamanı',
                    'name'                     => 'Adı',
                    'no'                       => 'Hayır',
                    'percentage-product-price' => 'Ürün Fiyatının Yüzdesi',
                    'priority'                 => 'Öncelik',
                    'product-attribute'        => 'Ürün Özelliği',
                    'save-btn'                 => 'Katalog Kuralını Kaydet',
                    'settings'                 => 'Ayarlar',
                    'status'                   => 'Durum',
                    'title'                    => 'Katalog Kuralı Oluştur',
                    'to'                       => 'Bitiş',
                    'yes'                      => 'Evet',
                ],

                'edit' => [
                    'action-type'              => 'Aksiyon Türü',
                    'actions'                  => 'Aksiyonlar',
                    'add-condition'            => 'Koşul Ekle',
                    'all-conditions-true'      => 'Tüm Koşullar Doğru',
                    'any-conditions-true'      => 'Herhangi Bir Koşul Doğru',
                    'back-btn'                 => 'Geri',
                    'categories'               => 'Kategoriler',
                    'channels'                 => 'Kanallar',
                    'choose-condition-to-add'  => 'Eklemek İçin Koşul Seçin',
                    'condition-type'           => 'Koşul Türü',
                    'conditions'               => 'Koşullar',
                    'contain'                  => 'İçerir',
                    'contains'                 => 'İçerir',
                    'customer-groups'          => 'Müşteri Grupları',
                    'description'              => 'Açıklama',
                    'discount-amount'          => 'İndirim Miktarı',
                    'does-not-contain'         => 'İçermez',
                    'end-other-rules'          => 'Diğer Kuralları Sonlandır',
                    'equals-or-greater-than'   => 'Eşit veya Büyük',
                    'equals-or-less-than'      => 'Eşit veya Küçük',
                    'fixed-amount'             => 'Sabit Miktar',
                    'from'                     => 'Başlangıç',
                    'general'                  => 'Genel',
                    'greater-than'             => 'Büyük',
                    'is-equal-to'              => 'Eşittir',
                    'is-not-equal-to'          => 'Eşit Değil',
                    'less-than'                => 'Küçük',
                    'marketing-time'           => 'Pazarlama Zamanı',
                    'name'                     => 'Adı',
                    'no'                       => 'Hayır',
                    'percentage-product-price' => 'Ürün Fiyatının Yüzdesi',
                    'priority'                 => 'Öncelik',
                    'product-attribute'        => 'Ürün Özelliği',
                    'save-btn'                 => 'Katalog Kuralını Kaydet',
                    'settings'                 => 'Ayarlar',
                    'status'                   => 'Durum',
                    'title'                    => 'Katalog Kuralını Düzenle',
                    'to'                       => 'Bitiş',
                    'yes'                      => 'Evet',
                ],

                'create-success' => 'Katalog kuralı başarıyla oluşturuldu',
                'delete-success' => 'Katalog kuralı başarıyla silindi',
                'update-success' => 'Katalog kuralı başarıyla güncellendi',
            ],

            'cart-rules-coupons' => [
                'cart-rule-not-defined-error' => 'Sepet kuralı silinemez',
                'delete-success'              => 'Sepet Kuralı Kuponu başarıyla silindi',
                'mass-delete-success'         => 'Seçilen öğeler başarıyla silindi',
                'success'                     => ':name başarıyla oluşturuldu',

                'datagrid' => [
                    'coupon-code'     => 'Kupon Kodu',
                    'created-date'    => 'Oluşturma Tarihi',
                    'delete'          => 'Sil',
                    'expiration-date' => 'Son Kullanma Tarihi',
                    'id'              => 'Kimlik',
                    'times-used'      => 'Kullanım Sayısı',
                ],
            ],
        ],

        'search-seo' => [
            'search-terms' => [
                'index' => [
                    'create-btn' => 'Arama Terimi Oluştur',
                    'title'      => 'Arama Terimleriniz',

                    'datagrid' => [
                        'actions'             => 'İşlemler',
                        'channel'             => 'Kanal',
                        'delete'              => 'Sil',
                        'edit'                => 'Düzenle',
                        'id'                  => 'ID',
                        'locale'              => 'Yerel Ayar',
                        'mass-delete-success' => 'Seçilen Arama Terimleri Başarıyla Silindi',
                        'redirect-url'        => 'Yönlendirme URL\'si',
                        'results'             => 'Sonuçlar',
                        'search-query'        => 'Arama Sorgusu',
                        'uses'                => 'Kullanım',
                    ],

                    'create' => [
                        'channel'        => 'Kanal',
                        'delete-warning' => 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?',
                        'locale'         => 'Yerel Ayar',
                        'redirect-url'   => 'Yönlendirme URL\'si',
                        'results'        => 'Sonuçlar',
                        'save-btn'       => 'Arama Terimini Kaydet',
                        'search-query'   => 'Arama Sorgusu',
                        'success'        => 'Arama Terimi Başarıyla Oluşturuldu',
                        'title'          => 'Arama Terimi Oluştur',
                        'uses'           => 'Kullanım',
                    ],

                    'edit' => [
                        'delete-success' => 'Arama Terimi Başarıyla Silindi',
                        'success'        => 'Arama Terimi Başarıyla Güncellendi',
                        'title'          => 'Arama Terimini Düzenle',
                    ],
                ],
            ],

            'search-synonyms' => [
                'index' => [
                    'create-btn' => 'Arama Eşanlamlısı Oluştur',
                    'title'      => 'Arama Eşanlamlıları',

                    'datagrid' => [
                        'actions'             => 'İşlemler',
                        'delete'              => 'Sil',
                        'edit'                => 'Düzenle',
                        'id'                  => 'Kimlik',
                        'mass-delete-success' => 'Seçilen Arama Eşanlamlıları Başarıyla Silindi',
                        'name'                => 'Adı',
                        'terms'               => 'Terimler',
                    ],

                    'create' => [
                        'delete-warning' => 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?',
                        'name'           => 'Adı',
                        'save-btn'       => 'Arama Eşanlamı Kaydet',
                        'success'        => 'Arama Eşanlamı başarıyla oluşturuldu',
                        'terms'          => 'Terimler',
                        'terms-info'     => 'Eşanlamlıları virgülle ayrılmış bir liste olarak girin, örneğin "ayakkabı,ayakkabı." Bu, aramayı tüm terimleri içerecek şekilde genişletir.',
                        'title'          => 'Arama Eşanlamı Oluştur',
                    ],

                    'edit' => [
                        'delete-success' => 'Arama Eşanlamı başarıyla silindi',
                        'success'        => 'Arama Eşanlamı başarıyla güncellendi',
                        'title'          => 'Arama Eşanlamı Düzenle',
                    ],
                ],
            ],

            'sitemaps' => [
                'index' => [
                    'create-btn' => 'Site Haritası Oluştur',
                    'sitemap'    => 'Site Haritası',
                    'title'      => 'Site Haritaları',

                    'datagrid' => [
                        'actions'         => 'İşlemler',
                        'delete'          => 'Sil',
                        'edit'            => 'Düzenle',
                        'file-name'       => 'Dosya Adı',
                        'id'              => 'ID',
                        'link-for-google' => 'Google için Link',
                        'path'            => 'Yol',
                    ],

                    'create' => [
                        'delete-warning' => 'Emin misiniz, bu işlemi gerçekleştirmek istediğinize?',
                        'file-name'      => 'Dosya Adı',
                        'file-name-info' => 'Örnek: sitemap.xml',
                        'path'           => 'Yol',
                        'path-info'      => 'Örnek: "/sitemap/" veya "Ana yol için /"',
                        'save-btn'       => 'Site Haritasını Kaydet',
                        'success'        => 'Site haritası başarıyla oluşturuldu',
                        'title'          => 'Site Haritası Oluştur',
                    ],

                    'edit' => [
                        'delete-success' => 'Site haritası başarıyla silindi',
                        'success'        => 'Site haritası başarıyla güncellendi',
                        'title'          => 'Site Haritasını Düzenle',
                    ],
                ],

                'edit' => [
                    'back-btn'       => 'Geri',
                    'file-name'      => 'Dosya Adı',
                    'file-name-info' => 'Örnek: sitemap.xml',
                    'general'        => 'Genel',
                    'path'           => 'Yol',
                    'path-info'      => 'Örnek: "/sitemap/" veya "Ana yol için /"',
                    'save-btn'       => 'Site Haritasını Kaydet',
                ],

                'delete-failed' => ':name Silme Başarısız',
            ],

            'url-rewrites' => [
                'index' => [
                    'create-btn' => 'URL Yeniden Yönlendirme Oluştur',
                    'title'      => 'URL Yeniden Yönlendirmeleri',

                    'datagrid' => [
                        'actions'             => 'İşlemler',
                        'category'            => 'Kategori',
                        'cms-page'            => 'CMS Sayfası',
                        'delete'              => 'Sil',
                        'edit'                => 'Düzenle',
                        'for'                 => 'İçin',
                        'id'                  => 'ID',
                        'locale'              => 'Yerel',
                        'mass-delete-success' => 'Seçili URL Yeniden Yönlendirmeleri Başarıyla Silindi',
                        'permanent-redirect'  => 'Kalıcı (301)',
                        'product'             => 'Ürün',
                        'redirect-type'       => 'Yönlendirme Türü',
                        'request-path'        => 'İstek Yolu',
                        'target-path'         => 'Hedef Yol',
                        'temporary-redirect'  => 'Geçici (302)',
                    ],

                    'create' => [
                        'category'           => 'Kategori',
                        'cms-page'           => 'CMS Sayfası',
                        'delete-warning'     => 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?',
                        'for'                => 'İçin',
                        'locale'             => 'Yerel',
                        'permanent-redirect' => 'Kalıcı (301)',
                        'product'            => 'Ürün',
                        'redirect-type'      => 'Yönlendirme Türü',
                        'request-path'       => 'İstek Yolu',
                        'save-btn'           => 'URL Yeniden Yönlendirmeyi Kaydet',
                        'success'            => 'URL Yeniden Yönlendirmesi başarıyla oluşturuldu',
                        'target-path'        => 'Hedef Yol',
                        'temporary-redirect' => 'Geçici (302)',
                        'title'              => 'URL Yeniden Yönlendirme Oluştur',
                    ],

                    'edit' => [
                        'delete-success' => 'URL Yeniden Yönlendirmesi başarıyla silindi',
                        'success'        => 'URL Yeniden Yönlendirmesi başarıyla güncellendi',
                        'title'          => 'URL Yeniden Yönlendirmeyi Düzenle',
                    ],
                ],
            ],
        ],
    ],

    'cms' => [
        'index' => [
            'already-taken' => ':name zaten alınmış.',
            'channel'       => 'Kanal',
            'create-btn'    => 'Sayfa Oluştur',
            'language'      => 'Dil',
            'title'         => 'Sayfalar',

            'datagrid' => [
                'channel'             => 'Kanal',
                'delete'              => 'Sil',
                'edit'                => 'Düzenle',
                'id'                  => 'ID',
                'mass-delete-success' => 'Seçilen Veriler Başarıyla Silindi',
                'page-title'          => 'Sayfa Başlığı',
                'url-key'             => 'URL Anahtarı',
                'view'                => 'Görüntüle',
            ],
        ],

        'create' => [
            'channels'         => 'Kanallar',
            'content'          => 'İçerik',
            'description'      => 'Açıklama',
            'general'          => 'Genel',
            'meta-description' => 'Meta Açıklama',
            'meta-keywords'    => 'Meta Anahtar Kelimeler',
            'meta-title'       => 'Meta Başlık',
            'page-title'       => 'Başlık',
            'save-btn'         => 'Sayfayı Kaydet',
            'seo'              => 'SEO',
            'title'            => 'Sayfa Oluştur',
            'url-key'          => 'URL Anahtarı',
        ],

        'edit' => [
            'back-btn'         => 'Geri',
            'channels'         => 'Kanallar',
            'content'          => 'İçerik',
            'description'      => 'Açıklama',
            'general'          => 'Genel',
            'meta-description' => 'Meta Açıklama',
            'meta-keywords'    => 'Meta Anahtar Kelimeler',
            'meta-title'       => 'Meta Başlık',
            'page-title'       => 'Sayfa Başlığı',
            'preview-btn'      => 'Sayfayı Önizle',
            'save-btn'         => 'Sayfayı Kaydet',
            'seo'              => 'SEO',
            'title'            => 'Sayfayı Düzenle',
            'url-key'          => 'URL Anahtarı',
        ],

        'create-success' => 'CMS başarıyla oluşturuldu.',
        'delete-success' => 'CMS başarıyla silindi.',
        'no-resource'    => 'Kaynak mevcut değil.',
        'update-success' => 'CMS başarıyla güncellendi.',
    ],

    'settings' => [
        'locales' => [
            'index' => [
                'create-btn' => 'Yerel Oluştur',
                'locale'     => 'Yerel',
                'logo-size'  => 'Resim çözünürlüğü 24px x 16px gibi olmalıdır',
                'title'      => 'Yereller',

                'datagrid' => [
                    'actions'   => 'İşlemler',
                    'code'      => 'Kodu',
                    'delete'    => 'Sil',
                    'direction' => 'Yön',
                    'edit'      => 'Düzenle',
                    'id'        => 'ID',
                    'ltr'       => 'LTR',
                    'name'      => 'Adı',
                    'rtl'       => 'RTL',
                ],

                'create' => [
                    'code'             => 'Kodu',
                    'direction'        => 'Yön',
                    'locale-logo'      => 'Yerel Logosu',
                    'name'             => 'Adı',
                    'save-btn'         => 'Yereli Kaydet',
                    'select-direction' => 'Yön seç',
                    'title'            => 'Yerel Oluştur',
                ],

                'edit' => [
                    'title' => 'Yerelleri Düzenle',
                ],

                'create-success'    => 'Yerel başarıyla oluşturuldu.',
                'delete-failed'     => 'Yerel silme başarısız',
                'delete-success'    => 'Yerel başarıyla silindi.',
                'delete-warning'    => 'Emin misiniz, bu işlemi gerçekleştirmek istediğinize?',
                'last-delete-error' => 'En az bir Yerel gereklidir.',
                'update-success'    => 'Yerel başarıyla güncellendi.',
            ],
        ],

        'currencies' => [
            'index' => [
                'title'      => 'Para Birimleri',
                'create-btn' => 'Para Birimi Oluştur',
                'currency'   => 'Para Birimi',

                'datagrid' => [
                    'actions'        => 'İşlemler',
                    'code'           => 'Kodu',
                    'delete'         => 'Sil',
                    'edit'           => 'Düzenle',
                    'id'             => 'ID',
                    'method-error'   => 'Hata! Yanlış yöntem algılandı, lütfen kitlesel işlem yapılandırmasını kontrol edin',
                    'name'           => 'Adı',
                    'no-resource'    => 'Eylem için yetersiz kaynak sağlandı',
                    'partial-action' => 'Kısıtlanmış sistem kısıtlamaları nedeniyle bazı işlemler gerçekleştirilmedi',
                    'update-success' => 'Seçilen :resource başarıyla güncellendi',
                ],

                'create' => [
                    'code'                   => 'Kodu',
                    'create-btn'             => 'Para Birimi Oluştur',
                    'currency-position'      => 'Para Birimi Pozisyonu',
                    'decimal'                => 'Ondalık',
                    'decimal-separator'      => 'Ondalık Ayırıcı',
                    'decimal-separator-note' => ':attribute alanı yalnızca virgül (,) ve nokta (.) operatörlerini kabul edebilir',
                    'delete-warning'         => 'Emin misiniz, bu işlemi gerçekleştirmek istediğinize?',
                    'general'                => 'Genel',
                    'group-separator'        => 'Grup Ayırıcı',
                    'group-separator-note'   => ':attribute alanı yalnızca virgül (,), nokta (.), kesme işareti (\') ve boşluk ( ) karakterlerini kabul edebilir.',
                    'name'                   => 'Adı',
                    'save-btn'               => 'Para Birimini Kaydet',
                    'symbol'                 => 'Sembol',
                    'title'                  => 'Yeni Para Birimi Oluştur',
                ],

                'edit' => [
                    'title' => 'Para Birimini Düzenle',
                ],

                'create-success'    => 'Para birimi başarıyla oluşturuldu.',
                'delete-failed'     => 'Para birimi silinemedi',
                'delete-success'    => 'Para birimi başarıyla silindi.',
                'last-delete-error' => 'En az bir Para Birimi gereklidir.',
                'update-success'    => 'Para birimi başarıyla güncellendi.',
            ],
        ],

        'data-transfer' => [
            'imports' => [
                'create' => [
                    'action'              => 'Aksiyon',
                    'allowed-errors'      => 'İzin Verilen Hatalar',
                    'back-btn'            => 'Geri',
                    'create-update'       => 'Oluştur/Güncelle',
                    'delete'              => 'Sil',
                    'download-sample'     => 'Örnek İndir',
                    'field-separator'     => 'Alan Ayracı',
                    'file-info-example'   => 'Örneğin, urun-resimleri için dosyalar /project-root/storage/app/import/product-images klasörüne yerleştirilmelidir.',
                    'file-info'           => '/project-root/storage/app/import klasörüne göre mutlak yol kullanın, Örnek: urun-resimleri, import-resimler.',
                    'file'                => 'Dosya',
                    'general'             => 'Genel',
                    'images-directory'    => 'Resim Klasör Yolu',
                    'process-in-queue'    => 'Sıra İşlemesi',
                    'results'             => 'Sonuçlar',
                    'save-btn'            => 'Kaydet Import',
                    'settings'            => 'Ayarlar',
                    'skip-errors'         => 'Hataları Atla',
                    'stop-on-errors'      => 'Hatalara Dur',
                    'title'               => 'Import Oluştur',
                    'type'                => 'Tür',
                    'validation-strategy' => 'Doğrulama Yaklaşımı',
                ],

                'edit' => [
                    'action'              => 'Aksiyon',
                    'allowed-errors'      => 'İzin Verilen Hatalar',
                    'back-btn'            => 'Geri',
                    'create-update'       => 'Oluştur/Güncelle',
                    'current-file'        => 'Mevcut Yüklenen Dosya',
                    'delete'              => 'Sil',
                    'download-sample'     => 'Örnek İndir',
                    'field-separator'     => 'Alan Ayracı',
                    'file-info-example'   => 'Örneğin, urun-resimleri için dosyalar /project-root/storage/app/import/product-images klasörüne yerleştirilmelidir.',
                    'file-info'           => '/project-root/storage/app/import klasörüne göre mutlak yol kullanın, Örnek: urun-resimleri, import-resimler.',
                    'file'                => 'Dosya',
                    'general'             => 'Genel',
                    'images-directory'    => 'Resim Klasör Yolu',
                    'process-in-queue'    => 'Sıra İşlemesi',
                    'results'             => 'Sonuçlar',
                    'save-btn'            => 'Kaydet Import',
                    'settings'            => 'Ayarlar',
                    'skip-errors'         => 'Hataları Atla',
                    'stop-on-errors'      => 'Hatalara Dur',
                    'title'               => 'Import Düzenle',
                    'type'                => 'Tür',
                    'validation-strategy' => 'Doğrulama Yaklaşımı',
                ],

                'index' => [
                    'button-title' => 'Import Oluştur',
                    'title'        => 'Importlar',

                    'datagrid' => [
                        'actions'       => 'Eylemler',
                        'completed-at'  => 'Tamamlandı İçin',
                        'created'       => 'Oluşturuldu',
                        'delete'        => 'Sil',
                        'deleted'       => 'Silindi',
                        'edit'          => 'Düzenle',
                        'error-file'    => 'Hata Dosyası',
                        'id'            => 'ID',
                        'started-at'    => 'Başladı',
                        'state'         => 'Durum',
                        'summary'       => 'Özet',
                        'updated'       => 'Güncellendi',
                        'uploaded-file' => 'Yüklenen Dosya',
                    ],
                ],

                'import' => [
                    'back-btn'                => 'Geri',
                    'completed-batches'       => 'Toplam Tamamlanan Batches:',
                    'download-error-report'   => 'Tam Hata Raporu İndir',
                    'edit-btn'                => 'Düzenle',
                    'imported-info'           => 'Tebrikler! Import işlemi başarıyla gerçekleşti.',
                    'importing-info'          => 'Import İşlemi Devam Ediyor',
                    'indexing-info'           => 'Ürün Fiyatı, Stok ve Elastik Arama İndekslemesi Devam Ediyor',
                    'linking-info'            => 'Ürün Bağlantısı Devam Ediyor',
                    'progress'                => 'İlerleme:',
                    'title'                   => 'Import',
                    'total-batches'           => 'Toplam Batches:',
                    'total-created'           => 'Toplam Kayıt Oluşturuldu:',
                    'total-deleted'           => 'Toplam Kayıt Silindi:',
                    'total-errors'            => 'Toplam Hatalar:',
                    'total-invalid-rows'      => 'Toplam Geçersiz Satırlar:',
                    'total-rows-processed'    => 'Toplam İşlenen Satırlar:',
                    'total-updated'           => 'Toplam Kayıt Güncellendi:',
                    'validate-info'           => 'Doğrulama Verilerine Tıklayarak importunuzu kontrol edin.',
                    'validate'                => 'Doğrula',
                    'validating-info'         => 'Veriler okundu ve Doğrulanıyor',
                    'validation-failed-info'  => 'Importunuz geçersiz. Aşağıdaki hataları düzeltin ve tekrar deneyin.',
                    'validation-success-info' => 'Importunuz geçerli. Import işlemini başlatmak için Tıklayın.',
                ],

                'create-success'    => 'Import başarıyla oluşturuldu.',
                'delete-failed'     => 'Import silinirken beklenmedik bir hata oluştu.',
                'delete-success'    => 'Import başarıyla silindi.',
                'not-valid'         => 'Import geçersiz',
                'nothing-to-import' => 'İçe aktarmak için kaynak yok.',
                'setup-queue-error' => 'Import işlemi için "database" veya "redis" olarak kuyruk sürücünü değiştirin.',
                'update-success'    => 'Import başarıyla güncellendi.',
            ],
        ],

        'exchange-rates' => [
            'index' => [
                'create-btn'    => 'Döviz Kuru Oluştur',
                'exchange-rate' => 'Döviz Kuru',
                'title'         => 'Döviz Kurları',
                'update-rates'  => 'Döviz Kuru Güncelle',

                'create' => [
                    'delete-warning'         => 'Emin misiniz, bu işlemi gerçekleştirmek istediğinize?',
                    'rate'                   => 'Kur',
                    'save-btn'               => 'Döviz Kuru Kaydet',
                    'select-target-currency' => 'Hedef para birimini seç',
                    'source-currency'        => 'Kaynak Para Birimi',
                    'target-currency'        => 'Hedef Para Birimi',
                    'title'                  => 'Döviz Kuru Oluştur',
                ],

                'edit' => [
                    'title' => 'Döviz Kurlarını Düzenle',
                ],

                'datagrid' => [
                    'actions'       => 'İşlemler',
                    'currency-name' => 'Para Birimi Adı',
                    'delete'        => 'Sil',
                    'edit'          => 'Düzenle',
                    'exchange-rate' => 'Döviz Kuru',
                    'id'            => 'ID',
                ],

                'create-success' => 'Döviz Kuru Başarıyla Oluşturuldu',
                'delete-error'   => 'Döviz Kuru Silme Hatası',
                'delete-success' => 'Döviz Kuru Başarıyla Silindi',
                'update-success' => 'Döviz Kuru Başarıyla Güncellendi',
            ],
        ],

        'inventory-sources' => [
            'index' => [
                'create-btn' => 'Envanter Kaynağı Oluştur',
                'title'      => 'Envanter Kaynakları',

                'datagrid' => [
                    'active'   => 'Aktif',
                    'code'     => 'Kod',
                    'delete'   => 'Sil',
                    'edit'     => 'Düzenle',
                    'id'       => 'ID',
                    'inactive' => 'Pasif',
                    'name'     => 'Adı',
                    'priority' => 'Öncelik',
                    'status'   => 'Durum',
                ],
            ],

            'create' => [
                'add-title'      => 'Envanter Kaynağı Ekle',
                'address'        => 'Kaynak Adresi',
                'back-btn'       => 'Geri',
                'city'           => 'Şehir',
                'code'           => 'Kod',
                'contact-email'  => 'E-posta',
                'contact-fax'    => 'Faks',
                'contact-info'   => 'İletişim Bilgileri',
                'contact-name'   => 'Adı',
                'contact-number' => 'İletişim Numarası',
                'country'        => 'Ülke',
                'description'    => 'Açıklama',
                'general'        => 'Genel',
                'latitude'       => 'Enlem',
                'longitude'      => 'Boylam',
                'name'           => 'Adı',
                'postcode'       => 'Posta Kodu',
                'priority'       => 'Öncelik',
                'save-btn'       => 'Envanter Kaynaklarını Kaydet',
                'select-country' => 'Ülke Seç',
                'select-state'   => 'Eyalet Seç',
                'settings'       => 'Ayarlar',
                'state'          => 'Eyalet',
                'status'         => 'Durum',
                'street'         => 'Sokak',
                'title'          => 'Envanter Kaynakları',
            ],

            'edit' => [
                'back-btn'       => 'Geri',
                'city'           => 'Şehir',
                'code'           => 'Kod',
                'contact-email'  => 'E-posta',
                'contact-fax'    => 'Faks',
                'contact-info'   => 'İletişim Bilgileri',
                'contact-name'   => 'Adı',
                'contact-number' => 'İletişim Numarası',
                'country'        => 'Ülke',
                'description'    => 'Açıklama',
                'general'        => 'Genel',
                'latitude'       => 'Enlem',
                'longitude'      => 'Boylam',
                'name'           => 'Adı',
                'postcode'       => 'Posta Kodu',
                'priority'       => 'Öncelik',
                'save-btn'       => 'Envanter Kaynaklarını Kaydet',
                'select-country' => 'Ülke Seç',
                'select-state'   => 'Eyalet Seç',
                'settings'       => 'Ayarlar',
                'source-address' => 'Kaynak Adresi',
                'state'          => 'Eyalet',
                'status'         => 'Durum',
                'street'         => 'Sokak',
                'title'          => 'Envanter Kaynaklarını Düzenle',
            ],

            'create-success'    => 'Envanter Kaynağı Başarıyla Oluşturuldu',
            'delete-failed'     => 'Envanter Kaynakları Silme Başarısız',
            'delete-success'    => 'Envanter Kaynakları Başarıyla Silindi',
            'last-delete-error' => 'Son Envanter Kaynağı Silinemez',
            'update-success'    => 'Envanter Kaynakları Başarıyla Güncellendi',
        ],

        'taxes' => [
            'categories' => [
                'index' => [
                    'delete-warning' => 'Silmek istediğinizden emin misiniz?',
                    'tax-category'   => 'Vergi Kategorisi',
                    'title'          => 'Vergi Kategorileri',

                    'datagrid' => [
                        'actions' => 'İşlemler',
                        'code'    => 'Kodu',
                        'delete'  => 'Sil',
                        'edit'    => 'Düzenle',
                        'id'      => 'ID',
                        'name'    => 'Adı',
                    ],

                    'create' => [
                        'add-tax-rates' => 'Vergi Oranları Ekle',
                        'code'          => 'Kodu',
                        'description'   => 'Açıklama',
                        'empty-text'    => 'Vergi Oranları mevcut değil, lütfen yeni Vergi Oranları oluşturun.',
                        'general'       => 'Vergi Kategorisi',
                        'name'          => 'Adı',
                        'save-btn'      => 'Vergi Kategorisini Kaydet',
                        'select'        => 'Seç',
                        'tax-rates'     => 'Vergi Oranları',
                        'title'         => 'Vergi Kategorisi Oluştur',
                    ],

                    'edit' => [
                        'title'   => 'Vergi Kategorilerini Düzenle',
                    ],

                    'can-not-delete' => 'Vergi Oranı Atanmış Kategoriler silinemez.',
                    'create-success' => 'Yeni Vergi Kategorisi Oluşturuldu',
                    'delete-failed'  => 'Vergi Kategorisi Silme Başarısız',
                    'delete-success' => 'Vergi Kategorisi Başarıyla Silindi',
                    'update-success' => 'Vergi Kategorisi Başarıyla Güncellendi',
                ],
            ],

            'rates' => [
                'index' => [
                    'button-title' => 'Vergi Oranı Oluştur',
                    'tax-rate'     => 'Vergi Oranı',
                    'title'        => 'Vergi Oranları',

                    'datagrid' => [
                        'country'    => 'Ülke',
                        'delete'     => 'Sil',
                        'edit'       => 'Düzenle',
                        'id'         => 'ID',
                        'identifier' => 'Tanımlayıcı',
                        'state'      => 'Eyalet',
                        'tax-rate'   => 'Vergi Oranı',
                        'zip-code'   => 'Posta Kodu',
                        'zip-from'   => 'Posta Kodu Başlangıcı',
                        'zip-to'     => 'Posta Kodu Bitişi',
                    ],
                ],

                'create' => [
                    'back-btn'       => 'Geri',
                    'country'        => 'Ülke',
                    'general'        => 'Genel',
                    'identifier'     => 'Tanımlayıcı',
                    'is-zip'         => 'Posta Kodu Aralığını Etkinleştir',
                    'save-btn'       => 'Vergi Oranını Kaydet',
                    'select-country' => 'Ülke Seç',
                    'select-state'   => 'Eyalet Seç',
                    'settings'       => 'Ayarlar',
                    'state'          => 'Eyalet',
                    'tax-rate'       => 'Oran',
                    'title'          => 'Vergi Oranı Oluştur',
                    'zip-code'       => 'Posta Kodu',
                    'zip-from'       => 'Posta Kodu Başlangıç',
                    'zip-to'         => 'Posta Kodu Bitiş',
                ],

                'edit' => [
                    'back-btn'       => 'Geri',
                    'country'        => 'Ülke',
                    'identifier'     => 'Tanımlayıcı',
                    'save-btn'       => 'Vergi Oranını Kaydet',
                    'select-country' => 'Ülke Seç',
                    'select-state'   => 'Eyalet Seç',
                    'settings'       => 'Ayarlar',
                    'state'          => 'Eyalet',
                    'tax-rate'       => 'Oran',
                    'title'          => 'Vergi Oranı Düzenle',
                    'zip-code'       => 'Posta Kodu',
                    'zip-from'       => 'Posta Kodu Başlangıç',
                    'zip-to'         => 'Posta Kodu Bitiş',
                ],

                'create-success' => 'Vergi oranı başarıyla oluşturuldu.',
                'delete-failed'  => 'Vergi oranı silme başarısız',
                'delete-success' => 'Vergi oranı başarıyla silindi.',
                'update-success' => 'Vergi Oranı Başarıyla Güncellendi',
            ],
        ],

        'channels' => [
            'index' => [
                'create-btn'        => 'Kanal Oluştur',
                'delete-failed'     => 'Kanal Silme Başarısız',
                'delete-success'    => 'Kanal başarıyla silindi.',
                'last-delete-error' => 'Son Kanal silinemedi.',
                'title'             => 'Kanallar',

                'datagrid' => [
                    'code'      => 'Kod',
                    'delete'    => 'Sil',
                    'edit'      => 'Düzenle',
                    'host-name' => 'Ana Bilgisayar Adı',
                    'id'        => 'ID',
                    'name'      => 'Adı',
                ],
            ],

            'create' => [
                'allowed-ips'             => 'İzin Verilen IP Adresleri',
                'cancel'                  => 'Geri',
                'code'                    => 'Kod',
                'create-success'          => 'Kanal başarıyla oluşturuldu.',
                'currencies'              => 'Para Birimleri',
                'currencies-and-locales'  => 'Para Birimleri ve Diller',
                'default-currency'        => 'Varsayılan Para Birimi',
                'default-locale'          => 'Varsayılan Dil',
                'description'             => 'Açıklama',
                'design'                  => 'Tasarım',
                'favicon'                 => 'Favicon',
                'favicon-size'            => 'Resim çözünürlüğü 16 piksel X 16 piksel gibi olmalıdır',
                'general'                 => 'Genel',
                'hostname'                => 'Ana Bilgisayar Adı',
                'hostname-placeholder'    => 'https://www.example.com (Sonuna kesme işareti ekleme.)',
                'inventory-sources'       => 'Envanter Kaynakları',
                'last-delete-error'       => 'En az bir Kanal gereklidir.',
                'locales'                 => 'Diller',
                'logo'                    => 'Logo',
                'logo-size'               => 'Resim çözünürlüğü 192 piksel X 50 piksel gibi olmalıdır',
                'maintenance-mode-text'   => 'Mesaj',
                'name'                    => 'Adı',
                'root-category'           => 'Kök Kategori',
                'save-btn'                => 'Kanalı Kaydet',
                'select-default-currency' => 'Varsayılan Para Birimini Seç',
                'select-default-locale'   => 'Varsayılan Yerelimi Seç',
                'select-root-category'    => 'Kök Kategoriyi Seç',
                'select-theme'            => 'Tema Seç',
                'seo'                     => 'Ana Sayfa SEO',
                'seo-description'         => 'Meta açıklama',
                'seo-keywords'            => 'Meta anahtar kelimeler',
                'seo-title'               => 'Meta başlığı',
                'settings'                => 'Ayarlar',
                'status'                  => 'Durum',
                'theme'                   => 'Tema',
                'title'                   => 'Kanal Oluştur',
            ],

            'edit' => [
                'allowed-ips'            => 'İzin Verilen IP Adresleri',
                'back-btn'               => 'Geri',
                'code'                   => 'Kod',
                'currencies'             => 'Para Birimleri',
                'currencies-and-locales' => 'Para Birimleri ve Diller',
                'default-currency'       => 'Varsayılan Para Birimi',
                'default-locale'         => 'Varsayılan Dil',
                'description'            => 'Açıklama',
                'design'                 => 'Tasarım',
                'favicon'                => 'Favicon',
                'favicon-size'           => 'Resim çözünürlüğü 16 piksel X 16 piksel gibi olmalıdır',
                'general'                => 'Genel',
                'hostname'               => 'Ana Bilgisayar Adı',
                'hostname-placeholder'   => 'https://www.example.com (Sonuna kesme işareti ekleme.)',
                'inventory-sources'      => 'Envanter Kaynakları',
                'last-delete-error'      => 'En az bir Kanal gereklidir.',
                'locales'                => 'Diller',
                'logo'                   => 'Logo',
                'logo-size'              => 'Resim çözünürlüğü 192 piksel X 50 piksel gibi olmalıdır',
                'maintenance-mode'       => 'Bakım Modu',
                'maintenance-mode-text'  => 'Mesaj',
                'name'                   => 'Adı',
                'root-category'          => 'Kök Kategori',
                'save-btn'               => 'Kanalı Kaydet',
                'seo'                    => 'Ana Sayfa SEO',
                'seo-description'        => 'Meta açıklama',
                'seo-keywords'           => 'Meta anahtar kelimeler',
                'seo-title'              => 'Meta başlığı',
                'status'                 => 'Durum',
                'theme'                  => 'Tema',
                'title'                  => 'Kanalı Düzenle',
                'update-success'         => 'Kanal Başarıyla Güncellendi',
            ],
        ],

        'users' => [
            'index' => [
                'admin' => 'Yönetici',
                'title' => 'Kullanıcılar',
                'user'  => 'Kullanıcı',

                'create' => [
                    'confirm-password'  => 'Şifreyi Onayla',
                    'email'             => 'E-posta',
                    'name'              => 'Adı',
                    'password'          => 'Şifre',
                    'role'              => 'Rol',
                    'save-btn'          => 'Kullanıcıyı Kaydet',
                    'status'            => 'Durum',
                    'title'             => 'Kullanıcı Oluştur',
                    'upload-image-info' => 'Profil Resmi Yükleyin (PNG veya JPG Formatında 110 piksel X 110 piksel)',
                ],

                'datagrid' => [
                    'actions'  => 'Eylemler',
                    'active'   => 'Aktif',
                    'delete'   => 'Sil',
                    'edit'     => 'Düzenle',
                    'email'    => 'E-posta',
                    'id'       => 'ID',
                    'inactive' => 'Pasif',
                    'name'     => 'Adı',
                    'role'     => 'Rol',
                    'status'   => 'Durum',
                ],

                'edit' => [
                    'title' => 'Kullanıcıyı Düzenle',
                ],
            ],

            'edit' => [
                'back-btn'         => 'Geri',
                'confirm-password' => 'Şifreyi Onayla',
                'email'            => 'E-posta',
                'general'          => 'Genel',
                'name'             => 'Adı',
                'password'         => 'Şifre',
                'role'             => 'Rol',
                'save-btn'         => 'Kullanıcıyı Kaydet',
                'status'           => 'Durum',
                'title'            => 'Kullanıcıyı Düzenle',
            ],

            'activate-warning'   => 'Hesabınız henüz etkinleştirilmedi, lütfen yöneticiyle iletişime geçin.',
            'cannot-change'      => 'Kullanıcı değiştirilemez.',
            'create-success'     => 'Kullanıcı başarıyla oluşturuldu.',
            'delete-failed'      => 'Kullanıcı silme başarısız.',
            'delete-success'     => 'Kullanıcı başarıyla silindi.',
            'delete-warning'     => 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?',
            'incorrect-password' => 'Yanlış şifre',
            'last-delete-error'  => 'Son kullanıcıyı silme başarısız.',
            'login-error'        => 'Kimlik bilgilerinizi kontrol edin ve yeniden deneyin.',
            'update-success'     => 'Kullanıcı başarıyla güncellendi.',
        ],

        'roles' => [
            'index' => [
                'create-btn' => 'Rol Oluştur',
                'title'      => 'Roller',

                'datagrid' => [
                    'custom'          => 'Özel',
                    'all'             => 'Tümü',
                    'permission-type' => 'İzin Türü',
                    'name'            => 'Adı',
                    'id'              => 'Id',
                    'edit'            => 'Düzenle',
                    'delete'          => 'Sil',
                ],
            ],

            'create' => [
                'access-control' => 'Erişim Kontrolü',
                'all'            => 'Tümü',
                'back-btn'       => 'Geri',
                'custom'         => 'Özel',
                'description'    => 'Açıklama',
                'general'        => 'Genel',
                'name'           => 'Adı',
                'permissions'    => 'İzinler',
                'save-btn'       => 'Rolü Kaydet',
                'title'          => 'Rol Oluştur',
            ],

            'edit' => [
                'access-control' => 'Erişim Kontrolü',
                'all'            => 'Tümü',
                'back-btn'       => 'Geri',
                'custom'         => 'Özel',
                'description'    => 'Açıklama',
                'general'        => 'Genel',
                'name'           => 'Adı',
                'permissions'    => 'İzinler',
                'save-btn'       => 'Rolü Kaydet',
                'title'          => 'Rolü Düzenle',
            ],

            'being-used'        => 'Rol zaten Yönetici Kullanıcısında kullanılıyor',
            'create-success'    => 'Roller Başarıyla Oluşturuldu',
            'delete-failed'     => 'Rol Silme Başarısız',
            'delete-success'    => 'Rol Başarıyla Silindi',
            'last-delete-error' => 'Son Rol silinemedi',
            'update-success'    => 'Rol Başarıyla Güncellendi',
        ],

        'themes' => [
            'index' => [
                'create-btn' => 'Tema Oluştur',
                'title'      => 'Temalar',

                'datagrid' => [
                    'active'        => 'Aktif',
                    'channel_name'  => 'Kanal Adı',
                    'change-status' => 'Durumu değiştir',
                    'delete'        => 'Sil',
                    'id'            => 'Kimlik',
                    'inactive'      => 'Pasif',
                    'name'          => 'Ad',
                    'sort-order'    => 'Sıralama Sırası',
                    'status'        => 'Durum',
                    'theme'         => 'Tema',
                    'type'          => 'Tür',
                    'view'          => 'Görünüm',
                ],
            ],

            'create' => [
                'name'       => 'Ad',
                'save-btn'   => 'Tema kaydet',
                'sort-order' => 'Sıralama Sırası',
                'themes'     => 'Temalar',
                'title'      => 'Tema Oluştur',

                'type' => [
                    'category-carousel' => 'Kategori Karuseli',
                    'footer-links'      => 'Alt Bağlantıları',
                    'image-carousel'    => 'Görüntü Karuseli',
                    'product-carousel'  => 'Ürün Karuseli',
                    'services-content'  => 'Hizmetlerin İçeriği',
                    'static-content'    => 'Statik İçerik',
                    'title'             => 'Tür',
                ],
            ],

            'edit' => [
                'active'                        => 'Aktif',
                'add-filter-btn'                => 'Filtre Ekle',
                'add-footer-link-btn'           => 'Alt Bağlantı Ekle',
                'add-image-btn'                 => 'Resim Ekle',
                'add-link'                      => 'Bağlantı Ekle',
                'asc'                           => 'artarak',
                'back'                          => 'Geri',
                'category-carousel'             => 'Kategori Karuseli',
                'category-carousel-description' => 'Dinamik kategorileri çekici bir şekilde göstermek için duyarlı bir kategori karuseli kullanın.',
                'channels'                      => 'Kanallar',
                'column'                        => 'Sütun',
                'create-filter'                 => 'Filtre Oluştur',
                'css'                           => 'CSS',
                'delete'                        => 'Sil',
                'desc'                          => 'azalarak',
                'edit'                          => 'Düzenle',
                'featured'                      => 'Öne Çıkan',
                'filter-title'                  => 'Başlık',
                'filters'                       => 'Filtreler',
                'footer-link'                   => 'Alt Bağlantıları',
                'footer-link-description'       => 'Sorunsuz web sitesi keşfi ve bilgi için alt bağlantılar aracılığıyla gezinin.',
                'footer-link-form-title'        => 'Alt Bağlantı',
                'footer-title'                  => 'Başlık',
                'general'                       => 'Genel',
                'html'                          => 'HTML',
                'image'                         => 'Resim',
                'image-size'                    => 'Resim çözünürlüğü (1920 piksel x 700 piksel) olmalıdır',
                'image-title'                   => 'Resim Başlığı',
                'image-upload-message'          => 'Sadece resimler (.jpeg, .jpg, .png, .webp, ..) izin verilir.',
                'inactive'                      => 'Pasif',
                'key'                           => 'Anahtar: :key',
                'key-input'                     => 'Anahtar',
                'limit'                         => 'Sınır',
                'link'                          => 'Bağlantı',
                'name'                          => 'Ad',
                'new'                           => 'Yeni',
                'no'                            => 'Hayır',
                'parent-id'                     => 'Üst Kimlik',
                'parent-id-hint'                => 'Birden fazla üst kimlik girmek için virgülle ayırarak (örneğin: 12,15,34) değerlerini girin.',
                'category-id'                   => 'Kategori Kimliği',
                'preview'                       => 'Önizleme',
                'product-carousel'              => 'Ürün Karuseli',
                'product-carousel-description'  => 'Dinamik ve duyarlı bir ürün karuseli ile ürünleri zarif bir şekilde sergileyin.',
                'save-btn'                      => 'Kaydet',
                'select'                        => 'Seç',
                'slider'                        => 'Slider',
                'slider-add-btn'                => 'Slider Ekle',
                'slider-description'            => 'Slider ile ilgili tema özelleştirmeleri.',
                'slider-image'                  => 'Slider Resmi',
                'slider-required'               => 'Slider alanı gereklidir.',
                'sort'                          => 'Sırala',
                'sort-order'                    => 'Sıralama Sırası',
                'static-content'                => 'Statik İçerik',
                'static-content-description'    => 'Kitleniz için özgün, bilgilendirici statik içerikle etkileşimi artırın.',
                'status'                        => 'Durum',
                'themes'                        => 'Temalar',
                'title'                         => 'Tema Düzenle',
                'update-slider'                 => 'Slider\'ı Güncelle',
                'url'                           => 'URL',
                'value'                         => 'Değer: :value',
                'value-input'                   => 'Değer',

                'services-content' => [
                    'add-btn'            => 'Hizmetler Ekle',
                    'channels'           => 'Kanallar',
                    'delete'             => 'Sil',
                    'description'        => 'Açıklama',
                    'general'            => 'Genel',
                    'name'               => 'Ad',
                    'save-btn'           => 'Kaydet',
                    'service-icon'       => 'Hizmet İkonu',
                    'service-icon-class' => 'Hizmet İkon Sınıfı',
                    'service-info'       => 'Hizmetle ilgili tema özelleştirme.',
                    'services'           => 'Hizmetler',
                    'sort-order'         => 'Sıralama Düzeni',
                    'status'             => 'Durum',
                    'title'              => 'Başlık',
                    'update-service'     => 'Hizmetleri Güncelle',
                ],
                'yes'                           => 'Evet',
            ],

            'create-success' => 'Tema başarıyla oluşturuldu',
            'delete-success' => 'Tema başarıyla silindi',
            'update-success' => 'Tema başarıyla güncellendi',
        ],
    ],

    'reporting' => [
        'sales' => [
            'index' => [
                'abandoned-carts'               => 'Terkedilen Sepetler',
                'abandoned-products'            => 'Terkedilen Ürünler',
                'abandoned-rate'                => 'Terk Oranı',
                'abandoned-revenue'             => 'Terk Edilen Gelir',
                'added-to-cart'                 => 'Sepete Eklendi',
                'added-to-cart-info'            => 'Sadece :progress ziyaretçisi ürünleri sepete ekledi',
                'all-channels'                  => 'Tüm Kanallar',
                'average-order-value-over-time' => 'Zaman İçinde Ortalama Sipariş Değeri',
                'average-sales'                 => 'Ortalama Sipariş Değeri',
                'count'                         => 'Sayı',
                'end-date'                      => 'Bitiş Tarihi',
                'id'                            => 'Kimlik',
                'interval'                      => 'Aralık',
                'name'                          => 'Ad',
                'orders'                        => 'Siparişler',
                'orders-over-time'              => 'Zaman İçinde Siparişler',
                'payment-method'                => 'Ödeme Yöntemi',
                'product-views'                 => 'Ürün Görünümleri',
                'product-views-info'            => 'Sadece :progress ziyaretçisi ürünleri görüntülüyor',
                'purchase-funnel'               => 'Satın Alma Hunisi',
                'purchased'                     => 'Satın Alınan',
                'purchased-info'                => 'Sadece :progress ziyaretçisi alışveriş yapıyor',
                'refunds'                       => 'İadeler',
                'refunds-over-time'             => 'Zaman İçinde İadeler',
                'sales-over-time'               => 'Zaman İçinde Satışlar',
                'shipping-collected'            => 'Toplanan Nakliye Ücreti',
                'shipping-collected-over-time'  => 'Zaman İçinde Toplanan Nakliye Ücreti',
                'start-date'                    => 'Başlangıç Tarihi',
                'tax-collected'                 => 'Toplanan Vergi',
                'tax-collected-over-time'       => 'Zaman İçinde Toplanan Vergi',
                'title'                         => 'Satışlar',
                'top-payment-methods'           => 'En İyi Ödeme Yöntemleri',
                'top-shipping-methods'          => 'En İyi Kargo Yöntemleri',
                'top-tax-categories'            => 'En İyi Vergi Kategorileri',
                'total'                         => 'Toplam',
                'total-orders'                  => 'Toplam Siparişler',
                'total-sales'                   => 'Toplam Satışlar',
                'total-visits'                  => 'Toplam ziyaretler',
                'total-visits-info'             => 'Mağazada toplam ziyaretçi',
                'view-details'                  => 'Detayları Görüntüle',
            ],
        ],

        'customers' => [
            'index' => [
                'all-channels'                => 'Tüm Kanallar',
                'count'                       => 'Sayı',
                'customers'                   => 'Müşteriler',
                'customers-over-time'         => 'Zaman İçinde Müşteriler',
                'customers-traffic'           => 'Müşteri Trafik',
                'customers-with-most-orders'  => 'En Fazla Sipariş Veren Müşteriler',
                'customers-with-most-reviews' => 'En Fazla İnceleme Yapan Müşteriler',
                'customers-with-most-sales'   => 'En Fazla Satış Yapan Müşteriler',
                'email'                       => 'E-posta',
                'end-date'                    => 'Bitiş Tarihi',
                'id'                          => 'Kimlik',
                'interval'                    => 'Aralık',
                'name'                        => 'Ad',
                'orders'                      => 'Siparişler',
                'reviews'                     => 'İncelemeler',
                'start-date'                  => 'Başlangıç Tarihi',
                'title'                       => 'Müşteriler',
                'top-customer-groups'         => 'En İyi Müşteri Grupları',
                'total'                       => 'Toplam',
                'total-customers'             => 'Toplam Müşteriler',
                'total-visitors'              => 'Toplam Ziyaretçiler',
                'traffic-over-week'           => 'Haftalık Trafik',
                'unique-visitors'             => 'Benzersiz Ziyaretçiler',
                'view-details'                => 'Detayları Görüntüle',
            ],
        ],

        'products' => [
            'index' => [
                'all-channels'                     => 'Tüm Kanallar',
                'channel'                          => 'Kanal',
                'end-date'                         => 'Bitiş Tarihi',
                'id'                               => 'Kimlik',
                'interval'                         => 'Aralık',
                'last-search-terms'                => 'Son arama terimleri',
                'locale'                           => 'Bölge',
                'name'                             => 'Ad',
                'orders'                           => 'Siparişler',
                'price'                            => 'Fiyat',
                'products-added-over-time'         => 'Zaman İçinde Eklenen Ürünler',
                'products-with-most-reviews'       => 'En Çok İnceleme Alan Ürünler',
                'products-with-most-visits'        => 'En Çok Ziyaret Alan Ürünler',
                'quantities'                       => 'Miktarlar',
                'quantities-sold-over-time'        => 'Zaman İçinde Satılan Miktarlar',
                'results'                          => 'Sonuçlar',
                'revenue'                          => 'Gelir',
                'reviews'                          => 'İncelemeler',
                'search-term'                      => 'Arama terimi',
                'start-date'                       => 'Başlangıç Tarihi',
                'title'                            => 'Ürünler',
                'top-search-terms'                 => 'En popüler arama terimleri',
                'top-selling-products-by-quantity' => 'Miktarına Göre En Çok Satılan Ürünler',
                'top-selling-products-by-revenue'  => 'Gelire Göre En Çok Satılan Ürünler',
                'total'                            => 'Toplam',
                'total-products-added-to-wishlist' => 'İstek Listesine Eklenen Ürünler',
                'total-sold-quantities'            => 'Satılan Ürün Miktarı',
                'uses'                             => 'Kullanımlar',
                'view-details'                     => 'Detayları Görüntüle',
                'visits'                           => 'Ziyaretler',
            ],
        ],

        'view' => [
            'all-channels'  => 'Tüm Kanallar',
            'back-btn'      => 'Geri',
            'day'           => 'Gün',
            'end-date'      => 'Bitiş Tarihi',
            'export-csv'    => 'CSV Olarak Dışa Aktar',
            'export-xls'    => 'XLS Olarak Dışa Aktar',
            'month'         => 'Ay',
            'not-available' => 'Kayıt bulunamadı.',
            'start-date'    => 'Başlangıç Tarihi',
            'year'          => 'Yıl',
        ],

        'empty' => [
            'info'  => 'Seçilen aralık için veri mevcut değil',
            'title' => 'Veri Mevcut Değil',
        ],
    ],

    'configuration' => [
        'index' => [
            'back-btn'                     => 'Geri',
            'delete'                       => 'Sil',
            'enable-at-least-one-payment'  => 'En az bir ödeme yöntemi etkinleştirin.',
            'enable-at-least-one-shipping' => 'En az bir kargo yöntemi etkinleştirin.',
            'no-result-found'              => 'Sonuç bulunamadı',
            'save-btn'                     => 'Ayarları Kaydet',
            'save-message'                 => 'Ayarlar başarıyla kaydedildi',
            'search'                       => 'Ara',
            'select-country'               => 'Ülke Seçin',
            'select-state'                 => 'Bölge Seçin',
            'title'                        => 'Ayarlar',

            'general' => [
                'info'  => 'Birim seçeneklerini ayarlayın.',
                'title' => 'Genel',

                'general' => [
                    'info'  => 'Birim seçeneklerini ayarlayın ve breadcrumbs\'ı etkinleştirin veya devre dışı bırakın.',
                    'title' => 'Genel',

                    'unit-options' => [
                        'info'        => 'Birim seçeneklerini ayarlayın.',
                        'title'       => 'Birim Seçenekleri',
                        'title-info'  => 'Ağırlığı pound (lbs) veya kilogram (kgs) cinsinden yapılandırın.',
                        'weight-unit' => 'Ağırlık Birimi',
                    ],

                    'breadcrumbs' => [
                        'shop'       => 'Mağaza Breadcrumbs',
                        'title'      => 'Breadcrumbs',
                        'title-info' => 'Mağazada breadcrumbs gezinmesini etkinleştirin veya devre dışı bırakın.',
                    ],
                ],

                'content' => [
                    'info'  => 'Başlık teklif başlığını ve özel komut dosyalarını ayarlayın.',
                    'title' => 'İçerik',

                    'header-offer' => [
                        'title'             => 'Başlık Teklifi',
                        'title-info'        => 'Başlık Teklifi Başlığını, teklif başlığını ve yönlendirme bağlantısını yapılandırın.',
                        'offer-title'       => 'Teklif Başlığı',
                        'redirection-title' => 'Yönlendirme Başlığı',
                        'redirection-link'  => 'Yönlendirme Bağlantısı',
                    ],

                    'speculation-rules' => [
                        'enable-speculation' => 'Spekülasyon Kurallarını Etkinleştir',
                        'info'               => 'Otomatik spekülasyon mantığını etkinleştirmek veya devre dışı bırakmak için ayarları yapılandırın.',
                        'title'              => 'Spekülasyon Kuralları',

                        'prerender' => [
                            'conservative'           => 'Muhafazakar',
                            'eager'                  => 'İstekli',
                            'eagerness'              => 'Prerender İsteklilik Seviyesi',
                            'eagerness-info'         => 'Spekülasyon kurallarının ne kadar agresif uygulanacağını kontrol eder. Seçenekler: istekli (maksimum), ılımlı (varsayılan), muhafazakar (düşük).',
                            'enabled'                => 'Prerender Spekülasyon Kurallarını Etkinleştir',
                            'ignore-url-params'      => 'Prerender URL Parametrelerini Yoksay',
                            'ignore-url-params-info' => 'Spekülasyon kurallarında yoksayılacak URL parametrelerini belirtin. Birden çok parametreyi ayırmak için boru işareti (|) kullanın.',
                            'ignore-urls'            => 'Prerender URL\'lerini Yoksay',
                            'ignore-urls-info'       => 'Spekülasyon mantığından hariç tutulacak URL\'leri girin. Birden çok URL\'yi boru işareti (|) ile ayırın.',
                            'info'                   => 'Spekülasyon kuralları durumunu ayarla.',
                            'moderate'               => 'Ilımlı',
                        ],

                        'prefetch' => [
                            'conservative'           => 'Muhafazakar',
                            'eager'                  => 'İstekli',
                            'eagerness'              => 'Prefetch İsteklilik Seviyesi',
                            'eagerness-info'         => 'Spekülasyon kurallarının ne kadar agresif uygulanacağını kontrol eder. Seçenekler: istekli (maksimum), ılımlı (varsayılan), muhafazakar (düşük).',
                            'enabled'                => 'Prefetch Spekülasyon Kurallarını Etkinleştir',
                            'ignore-url-params'      => 'Prefetch URL Parametrelerini Yoksay',
                            'ignore-url-params-info' => 'Spekülasyon kurallarında yoksayılacak URL parametrelerini belirtin. Birden çok parametreyi ayırmak için boru işareti (|) kullanın.',
                            'ignore-urls'            => 'Prefetch URL\'lerini Yoksay',
                            'ignore-urls-info'       => 'Spekülasyon mantığından hariç tutulacak URL\'leri girin. Birden çok URL\'yi boru işareti (|) ile ayırın.',
                            'info'                   => 'Spekülasyon kuralları durumunu ayarla.',
                            'moderate'               => 'Ilımlı',
                        ],
                    ],

                    'custom-scripts' => [
                        'custom-css'        => 'Özel CSS',
                        'custom-javascript' => 'Özel Javascript',
                        'title'             => 'Özel Komut Dosyaları',
                        'title-info'        => 'Özel komut dosyaları, yazılımın özelliklerini benzersiz bir şekilde geliştiren belirli işlevler veya özellikler eklemek için oluşturulan kişiselleştirilmiş kod parçalarıdır.',
                    ],
                ],

                'design' => [
                    'info'  => 'Yönetici paneli için logo ve favicon simgesini ayarlayın.',
                    'title' => 'Tasarım',

                    'admin-logo' => [
                        'favicon'    => 'Favicon',
                        'logo-image' => 'Logo Resmi',
                        'title'      => 'Yönetici Logosu',
                        'title-info' => 'Web sitenizin ön ucu için logo ve favicon görüntülerini daha iyi markalaşma ve tanınma için yapılandırın.',
                    ],

                    'menu-category' => [
                        'default'         => 'Varsayılan Menü',
                        'info'            => 'Bu ayar, başlık menüsündeki kategorilerin görünürlüğünü kontrol eder. Yalnızca ana kategorileri veya tüm iç içe kategorileri gösterebilirsiniz.',
                        'preview-default' => 'Varsayılan Menüyü Önizle',
                        'preview-sidebar' => 'Kenar Çubuğu Menüsünü Önizle',
                        'sidebar'         => 'Kenar Çubuğu Menüsü',
                        'title'           => 'Menü Kategori Görünümü',
                    ],
                ],

                'magic-ai' => [
                    'info'  => 'Magic AI seçeneklerini ayarlayın ve içerik oluşturmayı otomatikleştirmek için bazı seçeneklere izin verin.',
                    'title' => 'Magic AI',

                    'settings' => [
                        'api-key'        => 'API Anahtarı',
                        'enabled'        => 'Etkin',
                        'llm-api-domain' => 'LLM API Alanı',
                        'organization'   => 'Organizasyon',
                        'title'          => 'Genel Ayarlar',
                        'title-info'     => 'Magic AI özelliğinden tam anlamıyla yararlanmak için özel API Anahtarınızı girin ve ilgili Organizasyonu belirtin. OpenAI kimlik bilgilerinizi kontrol edin ve ayarları belirli ihtiyaçlarınıza göre özelleştirin.',
                    ],

                    'content-generation' => [
                        'category-description-prompt'      => 'Kategori Açıklama İsteği',
                        'cms-page-content-prompt'          => 'CMS Sayfa İçeriği İsteği',
                        'enabled'                          => 'Etkin',
                        'product-description-prompt'       => 'Ürün Açıklama İsteği',
                        'product-short-description-prompt' => 'Ürün Kısa Açıklama İsteği',
                        'title'                            => 'İçerik Oluşturma',
                        'title-info'                       => 'Bu özellik, AI kullanarak içeriği yönetmek istediğiniz her WYSIWYG düzenleyici için Magic AI\'yi etkinleştirir.<br/><br/>Etkinleştirildiğinde, herhangi bir düzenleyiciye giderek içerik oluşturabilirsiniz.',
                    ],

                    'image-generation' => [
                        'enabled'    => 'Etkin',
                        'title'      => 'Görüntü Oluşturma',
                        'title-info' => 'Bu özellik, DALL-E kullanarak görüntü oluşturmak istediğiniz her görüntü yüklemesi için Magic AI\'yi etkinleştirir.<br/><br/>Etkinleştirildiğinde, herhangi bir görüntü yüklemeye giderek görüntü oluşturabilirsiniz.',
                    ],

                    'review-translation' => [
                        'deepseek-r1-8b'    => 'DeepSeek R1 (8b)',
                        'enabled'           => 'Etkin',
                        'gemini-2-0-flash'  => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'       => 'OpenAI gpt-4 Turbo',
                        'gpt-4o'            => 'OpenAI gpt-4o',
                        'gpt-4o-mini'       => 'OpenAI gpt-4o mini',
                        'llama-groq'        => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'       => 'Llama 3.1 (8B)',
                        'llama3-2-1b'       => 'Llama 3.2 (1B)',
                        'llama3-2-3b'       => 'Llama 3.2 (3B)',
                        'llama3-8b'         => 'Llama 3 (8B)',
                        'llava-7b'          => 'Llava (7b)',
                        'mistral-7b'        => 'Mistral (7b)',
                        'model'             => 'Model',
                        'orca-mini'         => 'Orca Mini',
                        'phi3-5'            => 'Phi 3.5',
                        'qwen2-5-0-5b'      => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'      => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'       => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'        => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'        => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'    => 'Starling-lm (7b)',
                        'title'             => 'İnceleme Çevirisi',
                        'title-info'        => 'Müşteriye veya ziyaretçiye müşteri incelemesini İngilizceye çevirme seçeneği sunun.<br/><br/>Etkinleştirildiğinde, incelemeye gidin ve İngilizce dışındaki bir incelemeyi İngilizceye çevirmek için "İngilizceye Çevir" düğmesini bulacaksınız.',
                        'vicuna-13b'        => 'Vicuna (13b)',
                        'vicuna-7b'         => 'Vicuna (7b)',
                    ],

                    'checkout-message' => [
                        'deepseek-r1-8b'    => 'DeepSeek R1 (8b)',
                        'enabled'           => 'Etkin',
                        'gemini-2-0-flash'  => 'Gemini 2.0 Flash',
                        'gpt-4-turbo'       => 'OpenAI gpt 4 Turbo',
                        'gpt-4o'            => 'OpenAI gpt-4o',
                        'gpt-4o-mini'       => 'OpenAI gpt-4o mini',
                        'llama-groq'        => 'Llama 3.3 (Groq)',
                        'llama3-1-8b'       => 'Llama 3.1 (8B)',
                        'llama3-2-1b'       => 'Llama 3.2 (1B)',
                        'llama3-2-3b'       => 'Llama 3.2 (3B)',
                        'llama3-8b'         => 'Llama 3 (8B)',
                        'llava-7b'          => 'Llava (7b)',
                        'mistral-7b'        => 'Mistral (7b)',
                        'model'             => 'Model',
                        'orca-mini'         => 'Orca Mini',
                        'phi3-5'            => 'Phi 3.5',
                        'prompt'            => 'İstek',
                        'qwen2-5-0-5b'      => 'Qwen 2.5 (0.5b)',
                        'qwen2-5-1-5b'      => 'Qwen 2.5 (1.5b)',
                        'qwen2-5-14b'       => 'Qwen 2.5 (14b)',
                        'qwen2-5-3b'        => 'Qwen 2.5 (3b)',
                        'qwen2-5-7b'        => 'Qwen 2.5 (7b)',
                        'starling-lm-7b'    => 'Starling-lm (7b)',
                        'title'             => 'Kişiselleştirilmiş Ödeme Mesajı',
                        'title-info'        => 'Müşterilere Teşekkür Sayfasında kişiselleştirilmiş bir ödeme mesajı hazırlayın, içeriği bireysel tercihlere göre uyarlayarak genel satın alma sonrası deneyimini geliştirin.',
                        'vicuna'            => 'Vicuna',
                        'vicuna-13b'        => 'Vicuna (13b)',
                        'vicuna-7b'         => 'Vicuna (7b)',
                    ],
                ],

                'sitemap' => [
                    'info'  => 'Site haritası seçeneklerini ayarlayın.',
                    'title' => 'Site Haritası',

                    'settings' => [
                        'enabled' => 'Etkin',
                        'info'    => 'Arama motoru optimizasyonunu iyileştirmek ve kullanıcı deneyimini artırmak için web siteniz için site haritasını etkinleştirin veya devre dışı bırakın.',
                        'title'   => 'Ayarlar',
                    ],

                    'file-limits' => [
                        'info'             => 'Dosya limitleri seçeneklerini ayarlayın.',
                        'max-file-size'    => 'Maksimum dosya boyutu',
                        'max-url-per-file' => 'Dosya başına maksimum URL sayısı',
                        'title'            => 'Dosya Limitleri',
                    ],
                ],
            ],

            'gdpr' => [
                'title' => 'GDPR',
                'info'  => 'GDPR Uyumluluk Ayarları',

                'settings' => [
                    'title'   => 'GDPR Uyumluluk Ayarları',
                    'info'    => 'Gizlilik politikası dahil olmak üzere GDPR uyumluluk ayarlarını yönetin. Gereksinimlere göre GDPR özelliklerini etkinleştirin veya devre dışı bırakın.',
                    'enabled' => 'GDPR\'yi Etkinleştir',
                ],

                'agreement' => [
                    'title'          => 'GDPR Anlaşması',
                    'info'           => 'GDPR düzenlemelerine uygun olarak müşteri onayını yönetin. Veri toplama ve işleme için gerekli onayı etkinleştirin.',
                    'enable'         => 'Müşteri Onayını Etkinleştir',
                    'checkbox-label' => 'Onay için Etiket',
                    'content'        => 'Onay İçeriği',
                ],

                'cookie' => [
                    'bottom-left'  => 'Alt Sol',
                    'bottom-right' => 'Alt Sağ',
                    'center'       => 'Merkez',
                    'description'  => 'Açıklama',
                    'enable'       => 'Çerez Bildirimini Etkinleştir',
                    'identifier'   => 'Statik Blok ID',
                    'info'         => 'Kullanıcıları veri toplama ve gizlilik politikası hakkında bilgilendirmek için çerez onay ayarlarını yapılandırın.',
                    'position'     => 'Çerez Blok Konumu',
                    'title'        => 'Çerez Bildirim Ayarları',
                    'top-left'     => 'Üst Sol',
                    'top-right'    => 'Üst Sağ',
                ],

                'cookie-consent' => [
                    'title'                  => 'Çerez Ayarlarını Yönet',
                    'info'                   => 'Veri kullanımı yönetimi için tercih edilen çerez ayarlarını seçin. Farklı çerez türleri için onay ayarlarını yapılandırın.',
                    'strictly-necessary'     => 'Kesinlikle Gerekli',
                    'basic-interaction'      => 'Temel Etkileşim ve İşlevsellik',
                    'experience-enhancement' => 'Deneyim Geliştirme',
                    'measurement'            => 'Ölçüm',
                    'targeting-advertising'  => 'Hedefleme ve Reklam',
                ],
            ],

            'catalog' => [
                'info'  => 'Katalog',
                'title' => 'Katalog',

                'products' => [
                    'info'  => 'Ürün görüntüleme sayfası, sepet görüntüleme sayfası, mağaza vitrini, inceleme ve özellik sosyal paylaşımı.',
                    'title' => 'Ürünler',

                    'settings' => [
                        'compare-options'     => 'Karşılaştırma seçenekleri',
                        'image-search-option' => 'Görsel Arama Seçeneği',
                        'title'               => 'Ayarlar',
                        'title-info'          => 'Ayarlar, kullanıcı tercihlerine ve gereksinimlerine uygun olarak bir sistemin, uygulamanın veya cihazın nasıl davrandığını kontrol eden yapılandırılabilir seçeneklerdir.',
                        'wishlist-options'    => 'İstek listesi seçenekleri',
                    ],

                    'search' => [
                        'admin-mode'            => 'Yönetici Arama Modu',
                        'admin-mode-info'       => 'Mega Arama, Veri Tablosu ve diğer arama işlevleri yönetici panelinde seçilen arama motoruna dayanacaktır.',
                        'database'              => 'Veritabanı',
                        'elastic'               => 'Elastic Search',
                        'max-query-length'      => 'Maksimum sorgu uzunluğu',
                        'max-query-length-info' => 'Arama sorguları için maksimum sorgu uzunluğunu ayarlayın.',
                        'min-query-length'      => 'Minimum sorgu uzunluğu',
                        'min-query-length-info' => 'Arama sorguları için minimum sorgu uzunluğunu ayarlayın.',
                        'search-engine'         => 'Arama Motoru',
                        'storefront-mode'       => 'Mağaza ön yüzü Arama Modu',
                        'storefront-mode-info'  => 'Mağaza ön yüzünde kategori sayfası, arama sayfası ve diğer arama işlevleri de dahil olmak üzere seçilen arama motoruna dayalı arama işlevselliği sağlanır.',
                        'title'                 => 'Arama',
                        'title-info'            => 'Ürün aramaları için arama motorunu ayarlamak için gereksinimlerinize bağlı olarak bir veritabanı ve Elasticsearch arasında seçim yapabilirsiniz. Büyük bir ürün sayınız varsa, Elasticsearch önerilir.',
                    ],

                    'guest-checkout' => [
                        'allow-guest-checkout'      => 'Konuk Ödemesine İzin Ver',
                        'allow-guest-checkout-hint' => 'İpucu: Etkinleştirildiğinde, bu seçenek her ürün için ayrı ayrı yapılandırılabilir.',
                        'title'                     => 'Konuk Ödemesi',
                        'title-info'                => 'Konuk ödemesi, müşterilerin bir hesap oluşturmadan ürün satın almalarına olanak tanır, kullanım kolaylığı ve daha hızlı işlemler için satın alma sürecini basitleştirir.',
                    ],

                    'product-view-page' => [
                        'allow-no-of-related-products'  => 'İzin Verilen İlgili Ürün Sayısı',
                        'allow-no-of-up-sells-products' => 'İzin Verilen Yükseltme Ürün Sayısı',
                        'title'                         => 'Ürün görüntüleme sayfası yapılandırması',
                        'title-info'                    => 'Ürün görüntüleme sayfası yapılandırması, bir ürünün görüntüleme sayfasındaki düzeni ve öğeleri ayarlamayı içerir, kullanıcı deneyimini ve bilgi sunumunu geliştirir.',
                    ],

                    'cart-view-page' => [
                        'allow-no-of-cross-sells-products' => 'İzin Verilen Çapraz Satış Ürün Sayısı',
                        'title'                            => 'Sepet görüntüleme sayfası yapılandırması',
                        'title-info'                       => 'Sepet görüntüleme sayfası yapılandırması, alışveriş sepeti sayfasındaki öğeleri, ayrıntıları ve seçenekleri düzenlemeyi içerir, kullanıcı etkileşimini ve satın alma akışını iyileştirir.',
                    ],

                    'storefront' => [
                        'buy-now-button-display' => 'Müşterilere doğrudan ürün satın alma izni ver',
                        'cheapest-first'         => 'En Ucuz İlk',
                        'comma-separated'        => 'Virgülle Ayrılmış',
                        'default-list-mode'      => 'Varsayılan Liste Modu',
                        'expensive-first'        => 'En Pahalı İlk',
                        'from-a-z'               => 'A\'dan Z\'ye',
                        'from-z-a'               => 'Z\'den A\'ya',
                        'grid'                   => 'Izgara',
                        'latest-first'           => 'En Yeni İlk',
                        'list'                   => 'Liste',
                        'oldest-first'           => 'En Eski İlk',
                        'products-per-page'      => 'Sayfa Başına Ürün',
                        'sort-by'                => 'Sırala',
                        'title'                  => 'Mağaza ön yüzü',
                        'title-info'             => 'Mağaza ön yüzü, bir çevrimiçi mağazanın müşteriye yönelik arayüzüdür ve sorunsuz bir alışveriş deneyimi için ürünleri, kategorileri ve gezinmeyi sergiler.',
                    ],

                    'small-image' => [
                        'height'      => 'Yükseklik',
                        'placeholder' => 'Küçük Resim Yer Tutucusu',
                        'title'       => 'Küçük Resim',
                        'title-info'  => 'Mağaza ön yüzü, bir çevrimiçi mağazanın müşteriye yönelik arayüzüdür ve ürünleri, kategorileri ve gezinmeyi sergiler.',
                        'width'       => 'Genişlik',
                    ],

                    'medium-image' => [
                        'height'      => 'Yükseklik',
                        'placeholder' => 'Orta Resim Yer Tutucusu',
                        'title'       => 'Orta Resim',
                        'title-info'  => 'Orta resim, ayrıntı ve ekran alanı arasında bir denge sunan orta boyutlu bir resimdir ve genellikle görseller için kullanılır.',
                        'width'       => 'Genişlik',
                    ],

                    'large-image' => [
                        'height'      => 'Yükseklik',
                        'placeholder' => 'Büyük Resim Yer Tutucusu',
                        'title'       => 'Büyük resim',
                        'title-info'  => 'Büyük resim, genellikle ürünleri veya grafikleri sergilemek için kullanılan, ayrıntıları ve görsel etkiyi artıran yüksek çözünürlüklü bir resmi temsil eder.',
                        'width'       => 'Genişlik',
                    ],

                    'review' => [
                        'allow-customer-review'   => 'Müşteri İncelemesine İzin Ver',
                        'allow-guest-review'      => 'Konuk İncelemesine İzin Ver',
                        'censoring-reviewer-name' => 'İnceleyen Adını Sansürleme',
                        'display-review-count'    => 'Derecelendirmeler için inceleme sayısını görüntüleyin.',
                        'display-star-count'      => 'Derecelendirmelerdeki yıldız sayısını görüntüleyin.',
                        'summary'                 => 'Özet',
                        'title'                   => 'İnceleme',
                        'title-info'              => 'Bir şeyin değerlendirilmesi veya değerlendirilmesi, genellikle görüşler ve geri bildirimler içeren bir değerlendirme sürecidir.',
                    ],

                    'attribute' => [
                        'file-upload-size'  => 'İzin Verilen Dosya Yükleme Boyutu (Kb cinsinden)',
                        'image-upload-size' => 'İzin Verilen Resim Yükleme Boyutu (Kb cinsinden)',
                        'title'             => 'Öznitelik',
                        'title-info'        => 'Bir nesneyi tanımlayan, davranışını, görünümünü veya işlevini etkileyen özellik veya özellik.',
                    ],

                    'social-share' => [
                        'title-info'                  => 'Instagram, Twitter, WhatsApp, Facebook, Pinterest, LinkedIn ve e-posta aracılığıyla ürün paylaşımını etkinleştirmek için sosyal paylaşım ayarlarını yapılandırın.',
                        'title'                       => 'Sosyal Paylaşım',
                        'share-message'               => 'Paylaşım Mesajı',
                        'share'                       => 'Paylaş',
                        'enable-social-share'         => 'Sosyal Paylaşımı Etkinleştir?',
                        'enable-share-whatsapp-info'  => 'Sadece mobil cihazlarda WhatsApp paylaşım bağlantısı görünecektir.',
                        'enable-share-whatsapp'       => 'WhatsApp\'ta Paylaşımı Etkinleştir?',
                        'enable-share-twitter'        => 'Twitter\'da Paylaşımı Etkinleştir?',
                        'enable-share-pinterest'      => 'Pinterest\'ta Paylaşımı Etkinleştir?',
                        'enable-share-linkedin'       => 'Linkedin\'de Paylaşımı Etkinleştir?',
                        'enable-share-facebook'       => 'Facebook\'ta Paylaşımı Etkinleştir?',
                        'enable-share-email'          => 'E-posta ile Paylaşımı Etkinleştir?',
                    ],
                ],

                'rich-snippets' => [
                    'info'  => 'Ürünler ve kategorileri ayarlayın.',
                    'title' => 'Zengin Snippetler',

                    'products' => [
                        'enable'          => 'Etkinleştir',
                        'show-categories' => 'Kategorileri Göster',
                        'show-images'     => 'Resimleri Göster',
                        'show-offers'     => 'Teklifleri Göster',
                        'show-ratings'    => 'Değerlendirmeleri Göster',
                        'show-reviews'    => 'İncelemeleri Göster',
                        'show-sku'        => 'SKU\'yu Göster',
                        'show-weight'     => 'Ağırlığı Göster',
                        'title'           => 'Ürünler',
                        'title-info'      => 'SKU, ağırlık, kategoriler, resimler, yorumlar, derecelendirmeler, teklifler vb. dahil ürün ayarlarını yapılandırın.',
                    ],

                    'categories' => [
                        'enable'                  => 'Etkinleştir',
                        'show-search-input-field' => 'Arama Giriş Alanını Göster',
                        'title'                   => 'Kategoriler',
                        'title-info'              => '"Kategoriler", benzer ürünleri veya öğeleri gruplandırmak ve daha kolay gezinme için bir araya getirmek için kullanılan gruplar veya sınıflandırmalardır.',
                    ],
                ],

                'inventory' => [
                    'title'      => 'Envanter',
                    'title-info' => 'Geri siparişlere izin vermek ve stok dışı eşik değerini tanımlamak için envanter ayarlarını yapılandırın.',

                    'product-stock-options' => [
                        'allow-back-orders'       => 'Geri Siparişlere İzin Ver',
                        'max-qty-allowed-in-cart' => 'Alışveriş Sepetinde İzin Verilen Maksimum Miktar',
                        'min-qty-allowed-in-cart' => 'Alışveriş Sepetinde İzin Verilen Minimum Miktar',
                        'out-of-stock-threshold'  => 'Stokta Olmayan Eşik Değeri',
                        'title'                   => 'Ürün Stok Seçeneği',
                        'info'                    => 'Geri siparişlere izin vermek için ürün stok seçeneklerini yapılandırın, minimum ve maksimum sepette miktarları ayarlayın ve stok dışı eşikleri tanımlayın.',
                    ],
                ],
            ],

            'customer' => [
                'info'  => 'Müşteri',
                'title' => 'Müşteri',

                'address' => [
                    'info'  => 'Ülke, il, posta kodu ve sokak adresindeki satırları ayarlayın.',
                    'title' => 'Adres',

                    'requirements' => [
                        'city'       => 'Şehir',
                        'country'    => 'Ülke',
                        'state'      => 'İl',
                        'title'      => 'Gereksinimler',
                        'title-info' => 'Gereksinimler, bir şeyin başarıyla yerine getirilmesi, gerçekleştirilmesi veya karşılanması için gerekli olan koşullar, özellikler veya özelliklerdir.',
                        'zip'        => 'Posta Kodu',
                    ],

                    'information' => [
                        'street-lines' => 'Sokak Adresindeki Satırlar',
                        'title'        => 'Bilgi',
                        'title-info'   => '"Sokak adresindeki satırlar", genellikle virgüllerle ayrılan, konum bilgisi sağlayan bir adreste yer alan bireysel segmentlerdir, ev numarası, sokak, şehir ve daha fazlası gibi.',
                    ],
                ],

                'captcha' => [
                    'info'  => 'Site anahtarını, gizli anahtarını ve durumunu ayarlayın.',
                    'title' => 'Google Captcha',

                    'credentials' => [
                        'secret-key' => 'Gizli Anahtar',
                        'site-key'   => 'Site Anahtarı',
                        'status'     => 'Durum',
                        'title'      => 'Kimlik Bilgileri',
                        'title-info' => '"Site haritası: Arama motorları için web sitesi düzen haritası. Gizli anahtar: Veri şifreleme, kimlik doğrulama veya API erişim koruması için güvenli kod."',
                    ],

                    'validations' => [
                        'captcha'  => 'Bir şeyler yanlış gitti! Lütfen tekrar deneyin.',
                        'required' => 'Lütfen CAPTCHA seçin',
                    ],
                ],

                'settings' => [
                    'settings-info' => 'İstek listesi, giriş yönlendirmesi, bülten abonelikleri, varsayılan grup seçeneği, e-posta doğrulamaları ve sosyal giriş ayarlarını yapılandırın.',
                    'title'         => 'Ayarlar',

                    'login-as-customer' => [
                        'allow-option' => 'Müşteri olarak Girişe İzin Ver',
                        'title'        => 'Müşteri Olarak Giriş',
                        'title-info'   => '"Müşteri olarak giriş" işlevselliğini etkinleştirin.',
                    ],

                    'wishlist' => [
                        'allow-option' => 'İstek Listesi seçeneğine izin ver',
                        'title'        => 'İstek Listesi',
                        'title-info'   => 'İstek listesi seçeneğini etkinleştirin veya devre dışı bırakın.',
                    ],

                    'login-options' => [
                        'account'          => 'Hesap',
                        'home'             => 'Ana Sayfa',
                        'redirect-to-page' => 'Müşteriyi seçilen sayfaya yönlendir',
                        'title'            => 'Giriş Seçenekleri',
                        'title-info'       => 'Müşterilerin giriş yaptıktan sonra yönlendirilecek sayfayı belirlemek için giriş seçeneklerini yapılandırın.',
                    ],

                    'create-new-account-option' => [
                        'news-letter'      => 'Bülten\'e İzin Ver',
                        'news-letter-info' => 'Kayıt sayfasında bülten aboneliği seçeneğini etkinleştirin.',
                        'title'            => 'Yeni Hesap Oluşturma Seçenekleri',
                        'title-info'       => 'Yeni hesaplar için seçenekleri ayarlayın, bunlar arasında varsayılan bir müşteri grubu atama ve kayıt sırasında bülten aboneliği seçeneğini etkinleştirme bulunur.',

                        'default-group' => [
                            'general'    => 'Genel',
                            'guest'      => 'Misafir',
                            'title'      => 'Varsayılan Grup',
                            'title-info' => 'Yeni müşteriler için belirli bir müşteri grubunu varsayılan olarak atayın.',
                            'wholesale'  => 'Toptan',
                        ],
                    ],

                    'newsletter' => [
                        'subscription' => 'Bülten Aboneliğine İzin Ver',
                        'title'        => 'Bülten Aboneliği',
                        'title-info'   => '"Bülten bilgileri", abonelere düzenli olarak e-posta yoluyla paylaşılan güncellemeleri, teklifleri veya içerikleri içerir ve onları bilgilendirir ve etkileşimde bulunur.',
                    ],

                    'email' => [
                        'email-verification' => 'E-posta Doğrulamaya İzin Ver',
                        'title'              => 'E-posta Doğrulama',
                        'title-info'         => '"E-posta doğrulama", bir e-posta adresinin gerçekliğini doğrular, genellikle bir onay bağlantısı göndererek hesap güvenliğini ve iletişim güvenilirliğini artırır.',
                    ],

                    'social-login' => [
                        'title' => 'Sosyal Giriş',
                        'info'  => '"Sosyal giriş", kullanıcıların sosyal medya hesaplarını kullanarak bir web sitesine erişmesini sağlar, kayıt ve giriş işlemlerini basitleştirir.',

                        'google' => [
                            'enable-google' => 'Google\'ı Etkinleştir',

                            'client-id' => [
                                'title'      => 'Müşteri ID\'si',
                                'title-info' => 'Google tarafından OAuth uygulamanızı oluştururken verilen benzersiz kimlik numarası.',
                            ],

                            'client-secret' => [
                                'title'      => 'Müşteri Gizlisi',
                                'title-info' => 'Google OAuth müşteri hesabınızla ilişkili gizli anahtar. Gizli tutun.',
                            ],

                            'redirect' => [
                                'title'      => 'Yönlendirme URL\'si',
                                'title-info' => 'Kullanıcılar Google ile kimlik doğrulamasından sonra yönlendirilen geri çağırma URL\'si. Google konsolunuzda yapılandırılan URL ile aynı olmalıdır.',
                            ],
                        ],

                        'facebook' => [
                            'enable-facebook' => 'Facebook\'u Etkinleştir',

                            'client-id' => [
                                'title'      => 'Müşteri ID\'si',
                                'title-info' => 'Facebook geliştirici konsolunda bir uygulama oluştururken Facebook tarafından verilen Uygulama ID\'si.',
                            ],

                            'client-secret' => [
                                'title'      => 'Müşteri Gizlisi',
                                'title-info' => 'Facebook uygulamanızla ilişkili gizli anahtar. Güvenli ve özel tutun.',
                            ],

                            'redirect' => [
                                'title'      => 'Yönlendirme URL\'si',
                                'title-info' => 'Kullanıcılar Facebook ile kimlik doğrulamasından sonra yönlendirilen geri çağırma URL\'si. Facebook uygulama ayarlarınızda yapılandırılan URL ile aynı olmalıdır.',
                            ],
                        ],

                        'github' => [
                            'enable-github' => 'GitHub\'ı Etkinleştir',

                            'client-id' => [
                                'title'      => 'Müşteri ID\'si',
                                'title-info' => 'GitHub tarafından OAuth uygulamanızı oluştururken verilen benzersiz kimlik numarası.',
                            ],

                            'client-secret' => [
                                'title'      => 'Müşteri Gizlisi',
                                'title-info' => 'GitHub OAuth müşteri hesabınızla ilişkili gizli anahtar. Gizli tutun.',
                            ],

                            'redirect' => [
                                'title'      => 'Yönlendirme URL\'si',
                                'title-info' => 'Kullanıcılar GitHub ile kimlik doğrulamasından sonra yönlendirilen geri çağırma URL\'si. GitHub konsolunuzda yapılandırılan URL ile aynı olmalıdır.',
                            ],
                        ],

                        'linkedin' => [
                            'enable-linkedin' => 'LinkedIn\'i Etkinleştir',

                            'client-id' => [
                                'title'      => 'Müşteri ID\'si',
                                'title-info' => 'LinkedIn tarafından OAuth uygulamanızı oluştururken verilen benzersiz kimlik numarası.',
                            ],

                            'client-secret' => [
                                'title'      => 'Müşteri Gizlisi',
                                'title-info' => 'LinkedIn OAuth müşteri hesabınızla ilişkili gizli anahtar. Gizli tutun.',
                            ],

                            'redirect' => [
                                'title'      => 'Yönlendirme URL\'si',
                                'title-info' => 'Kullanıcılar LinkedIn ile kimlik doğrulamasından sonra yönlendirilen geri çağırma URL\'si. LinkedIn konsolunuzda yapılandırılan URL ile aynı olmalıdır.',
                            ],
                        ],

                        'twitter' => [
                            'enable-twitter' => 'Twitter\'ı Etkinleştir',

                            'client-id' => [
                                'title'      => 'Müşteri ID\'si',
                                'title-info' => 'Twitter tarafından OAuth uygulamanızı oluştururken verilen benzersiz kimlik numarası.',
                            ],

                            'client-secret' => [
                                'title'      => 'Müşteri Gizlisi',
                                'title-info' => 'Twitter OAuth müşteri hesabınızla ilişkili gizli anahtar. Gizli tutun.',
                            ],

                            'redirect' => [
                                'title'      => 'Yönlendirme URL\'si',
                                'title-info' => 'Kullanıcılar Twitter ile kimlik doğrulamasından sonra yönlendirilen geri çağırma URL\'si. Twitter konsolunuzda yapılandırılan URL ile aynı olmalıdır.',
                            ],
                        ],
                    ],
                ],
            ],

            'email' => [
                'info'  => 'E-posta',
                'title' => 'E-posta',

                'email-settings' => [
                    'admin-email'           => 'Yönetici E-postası',
                    'admin-email-tip'       => 'Bu kanal için yöneticiye e-posta göndermek için kullanılan e-posta adresi',
                    'admin-name'            => 'Yönetici Adı',
                    'admin-name-tip'        => 'Bu isim tüm yönetici e-postalarında görünecektir',
                    'admin-page-limit'      => 'Varsayılan Sayfa Başına Öğe (Yönetici)',
                    'contact-email'         => 'İletişim E-postası',
                    'contact-email-tip'     => 'E-postalarınızın altında gösterilecek e-posta adresi',
                    'contact-name'          => 'İletişim Adı',
                    'contact-name-tip'      => 'E-postalarınızın altında gösterilecek isim',
                    'email-sender-name'     => 'E-posta Gönderen Adı',
                    'email-sender-name-tip' => 'Bu isim müşterilerin gelen kutusunda görünecektir',
                    'info'                  => 'E-posta gönderen adını, mağaza e-posta adresini, yönetici adını ve yönetici e-posta adresini ayarlayın.',
                    'shop-email-from'       => 'Mağaza E-posta Adresi',
                    'shop-email-from-tip'   => 'Müşterilerinize e-posta göndermek için bu kanalın e-posta adresi',
                    'title'                 => 'E-posta Ayarları',
                ],

                'notifications' => [
                    'cancel-order'                                     => 'Sipariş iptal edildikten sonra müşteriye bildirim gönder',
                    'cancel-order-mail-to-admin'                       => 'Sipariş iptal edildikten sonra yöneticiye bir bildirim e-postası gönder',
                    'customer'                                         => 'Kayıttan sonra müşteri hesap bilgilerini gönder',
                    'customer-registration-confirmation-mail-to-admin' => 'Müşteri kaydından sonra yöneticiye bir onay e-postası gönder',
                    'info'                                             => 'Hesap doğrulaması, sipariş onayları, faturalar, iadeler, gönderimler ve sipariş iptalleri hakkında e-postalar almak için yapılandırın.',
                    'new-inventory-source'                             => 'Gönderim oluşturulduktan sonra envanter kaynağına bir bildirim e-postası gönder',
                    'new-invoice'                                      => 'Yeni bir fatura oluşturulduktan sonra müşteriye bir bildirim e-postası gönder',
                    'new-invoice-mail-to-admin'                        => 'Yeni bir fatura oluşturulduktan sonra yöneticiye bir bildirim e-postası gönder',
                    'new-order'                                        => 'Yeni bir sipariş verildikten sonra müşteriye bir onay e-postası gönder',
                    'new-order-mail-to-admin'                          => 'Yeni bir sipariş verildikten sonra yöneticiye bir onay e-postası gönder',
                    'new-refund'                                       => 'Bir iade oluşturulduktan sonra müşteriye bir bildirim e-postası gönder',
                    'new-refund-mail-to-admin'                         => 'Yeni bir iade oluşturulduktan sonra yöneticiye bir bildirim e-postası gönder',
                    'new-shipment'                                     => 'Bir gönderim oluşturulduktan sonra müşteriye bir bildirim e-postası gönder',
                    'new-shipment-mail-to-admin'                       => 'Yeni bir gönderim oluşturulduktan sonra yöneticiye bir bildirim e-postası gönder',
                    'registration'                                     => 'Müşteri kaydından sonra bir onay e-postası gönder',
                    'title'                                            => 'Bildirimler',
                    'verification'                                     => 'Müşteri kaydından sonra bir doğrulama e-postası gönder',
                ],
            ],

            'sales' => [
                'info'  => 'Satışlar',
                'title' => 'Satışlar',

                'shipping-setting' => [
                    'info'  => 'Ülke, Eyalet, Şehir, Sokak Adresi, Posta Kodu, Mağaza Adı, KDV Numarası, İletişim Numarası ve Banka Bilgileri gibi nakliye ayarlarını yapılandırın.',
                    'title' => 'Nakliye Ayarları',

                    'origin' => [
                        'bank-details'   => 'Banka Bilgileri',
                        'city'           => 'Şehir',
                        'contact-number' => 'İletişim Numarası',
                        'country'        => 'Ülke',
                        'state'          => 'Eyalet',
                        'store-name'     => 'Mağaza Adı',
                        'street-address' => 'Sokak Adresi',
                        'title'          => 'Kaynak',
                        'title-info'     => 'Nakliye kaynağı, malların veya ürünlerin hedeflerine ulaşmadan önce kaynaklandığı konumu ifade eder.',
                        'vat-number'     => 'KDV Numarası',
                        'zip'            => 'Posta Kodu',
                    ],
                ],

                'shipping-methods' => [
                    'info'  => 'Ücretsiz Kargo, Sabit Fiyat ve ihtiyaç duyulması halinde ek seçenekleri içeren nakliye yöntemlerini yapılandırın.',
                    'title' => 'Nakliye Yöntemleri',

                    'free-shipping' => [
                        'description' => 'Açıklama',
                        'page-title'  => 'Ücretsiz Kargo',
                        'status'      => 'Durum',
                        'title'       => 'Başlık',
                        'title-info'  => '"Ücretsiz kargo", kargo ücretinin feragat edildiği ve satıcının alıcıya mal teslim etmek için kargo masraflarını karşıladığı bir nakliye yöntemini ifade eder.',
                    ],

                    'flat-rate-shipping' => [
                        'description' => 'Açıklama',
                        'page-title'  => 'Sabit Fiyatlı Kargo',
                        'rate'        => 'Fiyat',
                        'status'      => 'Durum',
                        'title'       => 'Başlık',
                        'title-info'  => 'Sabit fiyatlı kargo, paketin ağırlığına, boyutuna veya mesafesine bakılmaksızın sabit bir ücretin tahsil edildiği bir nakliye yöntemidir. Bu, hem alıcılar hem de satıcılar için nakliye maliyetlerini basitleştirir ve avantajlı olabilir.',
                        'type'        => [
                            'per-order' => 'Sipariş Başına',
                            'per-unit'  => 'Ürün Başına',
                            'title'     => 'Tip',
                        ],
                    ],
                ],

                'payment-methods' => [
                    'accepted-currencies'            => 'Kabul Edilen Para Birimleri',
                    'accepted-currencies-info'       => 'Para birimi kodunu virgülle ayırarak ekleyin, örn. USD, INR, ...',
                    'business-account'               => 'İş Hesabı',
                    'cash-on-delivery'               => 'Kapıda Ödeme',
                    'cash-on-delivery-info'          => 'Müşterilerin mal veya hizmetleri kapılarında teslim alırken nakit ödeme yaptığı ödeme yöntemi.',
                    'client-id'                      => 'Müşteri Kimliği',
                    'client-id-info'                 => 'Test için "sb" kullanın.',
                    'client-secret'                  => 'Müşteri Sırrı',
                    'client-secret-info'             => 'Gizli anahtarınızı buraya ekleyin',
                    'description'                    => 'Açıklama',
                    'generate-invoice'               => 'Sipariş verildikten sonra faturayı otomatik olarak oluştur',
                    'generate-invoice-applicable'    => 'Otomatik fatura oluşturma etkinse uygulanır',
                    'info'                           => 'Ödeme yöntemi bilgilerini ayarlayın',
                    'instructions'                   => 'Talimatlar',
                    'logo'                           => 'Logo',
                    'logo-information'               => 'Resim çözünürlüğü 55 piksel X 45 piksel gibi olmalıdır',
                    'mailing-address'                => 'Çeki Gönder',
                    'money-transfer'                 => 'Havale',
                    'money-transfer-info'            => 'Fonların bir kişiden veya hesaptan başka bir kişiye veya hesaba, genellikle elektronik olarak, işlemler veya havaleler gibi çeşitli amaçlarla aktarılması.',
                    'page-title'                     => 'Ödeme Yöntemleri',
                    'paid'                           => 'Ödendi',
                    'paypal-smart-button'            => 'PayPal',
                    'paypal-smart-button-info'       => 'PayPal Smart Button: Web siteleri ve uygulamalar için güvenli, çoklu yöntemli işlemler için özelleştirilebilir düğmelerle çevrimiçi ödemeleri kolaylaştırır.',
                    'paypal-standard'                => 'PayPal Standart',
                    'paypal-standard-info'           => 'PayPal Standart, çevrimiçi işletmeler için temel bir PayPal ödeme seçeneğidir ve müşterilerin PayPal hesaplarını veya kredi / banka kartlarını kullanarak ödeme yapmalarını sağlar.',
                    'pending'                        => 'Beklemede',
                    'pending-payment'                => 'Bekleyen Ödeme',
                    'processing'                     => 'İşleniyor',
                    'sandbox'                        => 'Kum Havuzu',
                    'set-invoice-status'             => 'Fatura oluşturulduktan sonra fatura durumunu ayarla',
                    'set-order-status'               => 'Fatura oluşturulduktan sonra sipariş durumunu ayarla',
                    'sort-order'                     => 'Sıralama Sırası',
                    'status'                         => 'Durum',
                    'title'                          => 'Başlık',
                ],

                'order-settings' => [
                    'info'               => 'Sipariş numaralarını, minimum siparişleri ve geri siparişleri ayarlayın.',
                    'title'              => 'Sipariş Ayarları',

                    'order-number' => [
                        'generator'   => 'Sipariş Numarası Oluşturucusu',
                        'info'        => 'Bir müşteri siparişine atanan benzersiz bir tanımlayıcıdır ve satın alma süreci boyunca takip, iletişim ve referans sağlar.',
                        'length'      => 'Sipariş Numarası Uzunluğu',
                        'prefix'      => 'Sipariş Numarası Öneki',
                        'suffix'      => 'Sipariş Numarası Soneki',
                        'title'       => 'Sipariş Numarası Ayarları',
                    ],

                    'minimum-order' => [
                        'description'             => 'Açıklama',
                        'enable'                  => 'Etkinleştir',
                        'include-discount-amount' => 'İndirim Tutarını Dahil Et',
                        'include-tax-amount'      => 'Tutara Vergi Dahil Et',
                        'info'                    => 'Bir siparişin işlenmesi veya faydalar için gereken en düşük miktarı veya değeri belirleyen yapılandırılmış kriterler.',
                        'minimum-order-amount'    => 'Minimum Sipariş Tutarı',
                        'title'                   => 'Minimum Sipariş Ayarları',
                    ],

                    'reorder' => [
                        'admin-reorder'      => 'Yönetici Yeniden Sipariş',
                        'admin-reorder-info' => 'Yönetici kullanıcıları için yeniden sipariş özelliğini etkinleştirin veya devre dışı bırakın.',
                        'info'               => 'Mağaza kullanıcıları için yeniden sipariş özelliğini etkinleştirin veya devre dışı bırakın.',
                        'shop-reorder'       => 'Mağaza Yeniden Sipariş',
                        'shop-reorder-info'  => 'Mağaza kullanıcıları için yeniden sipariş özelliğini etkinleştirin veya devre dışı bırakın.',
                        'title'              => 'Yeniden Siparişe İzin Ver',
                    ],

                    'stock-options' => [
                        'allow-back-orders' => 'Geri Siparişlere İzin Ver',
                        'info'              => 'Stok seçenekleri, potansiyel karları etkileyen belirli bir fiyattan şirket hisselerini satın alma veya satma hakkı veren yatırım sözleşmeleridir.',
                        'title'             => 'Stok Seçenekleri',
                    ],
                ],

                'invoice-settings' => [
                    'info'  => 'Fatura numarasını, ödeme koşullarını, fatura kaydırma tasarımını ve fatura hatırlatıcılarını ayarlayın.',
                    'title' => 'Fatura Ayarları',

                    'invoice-number' => [
                        'generator'  => 'Fatura Numarası Oluşturucusu',
                        'info'       => 'Faturalara benzersiz tanımlama numaraları oluşturmak ve atamak için kuralların veya parametrelerin yapılandırılması.',
                        'length'     => 'Fatura Numarası Uzunluğu',
                        'prefix'     => 'Fatura Numarası Öneki',
                        'suffix'     => 'Fatura Numarası Soneki',
                        'title'      => 'Fatura Numarası Ayarları',
                    ],

                    'payment-terms' => [
                        'due-duration'      => 'Vade Süresi',
                        'due-duration-day'  => ':due-duration Gün',
                        'due-duration-days' => ':due-duration Gün',
                        'info'              => 'Alıcının satıcıya mal veya hizmetler için ne zaman ve nasıl ödeme yapması gerektiğini belirleyen anlaşılan koşullar.',
                        'title'             => 'Ödeme Koşulları',
                    ],

                    'pdf-print-outs' => [
                        'footer-text'      => 'Alt bilgi metni',
                        'footer-text-info' => 'PDF\'nin altbilgisinde görünecek metni girin.',
                        'info'             => 'Fatura Kimliği, Sipariş Kimliği\'ni başlıkta görüntülemek ve fatura logosunu içermek için PDF Yazdırmalarını yapılandırın.',
                        'invoice-id-info'  => 'Fatura Başlığında Fatura Kimliği\'nin görüntülenmesini yapılandırın.',
                        'invoice-id-title' => 'Başlıkta Fatura Kimliği\'ni Göster',
                        'logo'             => 'Logo',
                        'logo-info'        => 'Görüntü çözünürlüğü 131px X 30px olmalıdır.',
                        'order-id-info'    => 'Fatura Başlığında Sipariş Kimliği\'nin görüntülenmesini yapılandırın.',
                        'order-id-title'   => 'Başlıkta Sipariş Kimliği\'ni Göster',
                        'title'            => 'PDF Yazdırmaları',
                    ],

                    'invoice-reminders' => [
                        'info'                       => 'Faturalar için yaklaşan veya gecikmiş ödemeleri müşterilere hatırlatmak için otomatik bildirimler veya iletişimler gönderilir.',
                        'interval-between-reminders' => 'Hatırlatmalar Arasındaki Süre',
                        'maximum-limit-of-reminders' => 'Hatırlatmaların Maksimum Sınırı',
                        'title'                      => 'Fatura Hatırlatıcıları',
                    ],
                ],

                'taxes' => [
                    'title'      => 'Vergiler',
                    'title-info' => 'Vergiler, mallar, hizmetler veya işlemler üzerinde hükümetler tarafından zorunlu olarak uygulanan ve satıcılar tarafından toplanan ve yetkililere aktarılan zorunlu ücretlerdir.',

                    'categories' => [
                        'title'      => 'Vergi Kategorileri',
                        'title-info' => 'Vergi kategorileri, ürünlere veya hizmetlere vergi oranlarını kategorize etmek ve uygulamak için satış vergisi, katma değer vergisi veya özel tüketim vergisi gibi farklı vergi türleri için sınıflandırmalardır.',
                        'product'    => 'Ürün Varsayılan Vergi Kategorisi',
                        'shipping'   => 'Nakliye Vergi Kategorisi',
                        'none'       => 'Yok',
                    ],

                    'calculation' => [
                        'title'            => 'Hesaplama Ayarları',
                        'title-info'       => 'Temel fiyat, indirimler, vergiler ve ek ücretler gibi mal veya hizmetlerin maliyeti hakkında ayrıntılar.',
                        'based-on'         => 'Hesaplama Temeli',
                        'shipping-address' => 'Teslimat Adresi',
                        'billing-address'  => 'Fatura Adresi',
                        'shipping-origin'  => 'Nakliye Kaynağı',
                        'product-prices'   => 'Ürün Fiyatları',
                        'shipping-prices'  => 'Nakliye Fiyatları',
                        'excluding-tax'    => 'Vergi Hariç',
                        'including-tax'    => 'Vergi Dahil',
                    ],

                    'default-destination-calculation' => [
                        'default-country'   => 'Varsayılan Ülke',
                        'default-post-code' => 'Varsayılan Posta Kodu',
                        'default-state'     => 'Varsayılan Eyalet',
                        'title'             => 'Varsayılan Hedef Hesaplama',
                        'title-info'        => 'Önceden belirlenmiş faktörler veya ayarlar temelinde standart veya başlangıç ​​bir hedefin otomatik belirlenmesi.',
                    ],

                    'shopping-cart' => [
                        'title'                   => 'Alışveriş Sepeti Görüntüleme Ayarları',
                        'title-info'              => 'Alışveriş sepetinde vergilerin görüntülenmesini ayarlayın',
                        'display-prices'          => 'Fiyatları Göster',
                        'display-subtotal'        => 'Ara Toplamı Göster',
                        'display-shipping-amount' => 'Nakliye Tutarını Göster',
                        'excluding-tax'           => 'Vergi Hariç',
                        'including-tax'           => 'Vergi Dahil',
                        'both'                    => 'Hem Vergi Hariç Hem de Dahil',
                    ],

                    'sales' => [
                        'title'                   => 'Siparişler, Faturalar, İadeler Görüntüleme Ayarları',
                        'title-info'              => 'Siparişlerde, faturalarda ve iadelerde vergilerin görüntülenmesini ayarlayın',
                        'display-prices'          => 'Fiyatları Göster',
                        'display-subtotal'        => 'Ara Toplamı Göster',
                        'display-shipping-amount' => 'Nakliye Tutarını Göster',
                        'excluding-tax'           => 'Vergi Hariç',
                        'including-tax'           => 'Vergi Dahil',
                        'both'                    => 'Hem Vergi Hariç Hem de Dahil',
                    ],
                ],

                'checkout' => [
                    'title' => 'Ödeme',
                    'info'  => 'Misafir ödeme ayarlarını yapın, Mini Sepeti etkinleştir veya devre dışı bırak, sepet özeti.',

                    'shopping-cart' => [
                        'cart-page'              => 'Sepet Sayfası',
                        'cart-page-info'         => 'Kullanıcı alışveriş deneyimini geliştirmek için Sepet Sayfası görünürlüğünü kontrol edin.',
                        'cross-sell'             => 'Çapraz Satış Ürünleri',
                        'cross-sell-info'        => 'Ek satış fırsatlarını artırmak için çapraz satış ürünlerini etkinleştirin.',
                        'estimate-shipping'      => 'Tahmini Nakliye',
                        'estimate-shipping-info' => 'Ön ödemeli nakliye maliyetlerini sağlamak için tahmini nakliyeyi etkinleştirin.',
                        'guest-checkout'         => 'Misafir ödemesine izin ver',
                        'guest-checkout-info'    => 'Hızlı ve sorunsuz bir satın alma işlemi için misafir ödemesini etkinleştirin.',
                        'info'                   => 'Kullanıcı kolaylığını artırmak ve satın alma sürecini optimize etmek için misafir ödemesini, sepet sayfasını, çapraz satış ürünlerini ve tahmini nakliyeyi etkinleştirin.',
                        'title'                  => 'Alışveriş Sepeti',
                    ],

                    'my-cart' => [
                        'display-item-quantities' => 'Ürün miktarlarını göster',
                        'display-number-in-cart'  => 'Sepetteki ürün sayısını göster',
                        'info'                    => 'My Cart ayarlarını etkinleştirerek, ürün miktarlarının özetini göstermek ve sepetin toplam ürün sayısını görüntülemek için kolay takip için ayarları etkinleştirin.',
                        'summary'                 => 'Özet',
                        'title'                   => 'Sepetim',
                    ],

                    'mini-cart' => [
                        'display-mini-cart'    => 'Mini Sepeti Göster',
                        'info'                 => 'Mini Sepet ayarlarını etkinleştirerek mini sepeti görüntüleyin ve hızlı erişim için Mini Sepet Teklif Bilgilerini gösterin.',
                        'mini-cart-offer-info' => 'Mini Sepet Teklif Bilgileri',
                        'title'                => 'Mini Sepet',
                    ],
                ],
            ],
        ],
    ],

    'components' => [
        'layouts' => [
            'header' => [
                'account-title' => 'Hesap',
                'app-version'   => 'Sürüm : :version',
                'logout'        => 'Çıkış',
                'my-account'    => 'Hesabım',
                'notifications' => 'Bildirimler',
                'visit-shop'    => 'Mağazayı Ziyaret Et',

                'mega-search' => [
                    'categories'                      => 'Kategoriler',
                    'customers'                       => 'Müşteriler',
                    'explore-all-categories'          => 'Tüm kategorileri keşfet',
                    'explore-all-customers'           => 'Tüm müşterileri keşfet',
                    'explore-all-matching-categories' => 'Eşleşen tüm kategorileri keşfet “:query” (:count)',
                    'explore-all-matching-customers'  => 'Eşleşen tüm müşterileri keşfet “:query” (:count)',
                    'explore-all-matching-orders'     => 'Eşleşen tüm siparişleri keşfet “:query” (:count)',
                    'explore-all-matching-products'   => 'Eşleşen tüm ürünleri keşfet “:query” (:count)',
                    'explore-all-orders'              => 'Tüm siparişleri keşfet',
                    'explore-all-products'            => 'Tüm ürünleri keşfet',
                    'orders'                          => 'Siparişler',
                    'products'                        => 'Ürünler',
                    'sku'                             => 'Stok Kodu: :sku',
                    'title'                           => 'Mega Arama',
                ],
            ],

            'sidebar' => [
                'attribute-families'       => 'Özellik Aileleri',
                'attributes'               => 'Özellikler',
                'booking-product'          => 'Rezervasyonlar',
                'campaigns'                => 'Kampanyalar',
                'catalog'                  => 'Katalog',
                'categories'               => 'Kategoriler',
                'channels'                 => 'Kanallar',
                'cms'                      => 'CMS',
                'collapse'                 => 'Daralt',
                'communications'           => 'İletişim',
                'configure'                => 'Yapılandır',
                'currencies'               => 'Para Birimleri',
                'customers'                => 'Müşteriler',
                'dashboard'                => 'Kontrol Paneli',
                'data-transfer'            => 'Veri aktarımı',
                'discount'                 => 'İndirim',
                'email-templates'          => 'E-posta Şablonları',
                'events'                   => 'Etkinlikler',
                'exchange-rates'           => 'Döviz Kurları',
                'gdpr-data-requests'       => 'GDPR Veri Talepleri',
                'groups'                   => 'Gruplar',
                'imports'                  => 'İthalat',
                'inventory-sources'        => 'Envanter Kaynakları',
                'invoices'                 => 'Faturalar',
                'locales'                  => 'Yerel Ayarlar',
                'marketing'                => 'Pazarlama',
                'mode'                     => 'Karanlık Mod',
                'newsletter-subscriptions' => 'Bülten Abonelikleri',
                'orders'                   => 'Siparişler',
                'products'                 => 'Ürünler',
                'promotions'               => 'Promosyonlar',
                'refunds'                  => 'İade',
                'reporting'                => 'Raporlama',
                'reviews'                  => 'Yorumlar',
                'roles'                    => 'Roller',
                'sales'                    => 'Satışlar',
                'search-seo'               => 'Arama ve SEO',
                'search-synonyms'          => 'Arama Eşanlamlıları',
                'search-terms'             => 'Arama Terimleri',
                'settings'                 => 'Ayarlar',
                'shipments'                => 'Gönderiler',
                'sitemaps'                 => 'Site Haritaları',
                'tax-categories'           => 'Vergi Kategorileri',
                'tax-rates'                => 'Vergi Oranları',
                'taxes'                    => 'Vergiler',
                'themes'                   => 'Temalar',
                'transactions'             => 'İşlemler',
                'url-rewrites'             => 'URL Yeniden Yazma',
                'users'                    => 'Kullanıcılar',
            ],

            'powered-by' => [
                'description' => ':webkul tarafından geliştirilen açık kaynaklı bir proje olan :bagisto tarafından desteklenmektedir.',
            ],
        ],

        'datagrid' => [
            'index' => [
                'no-records-selected'              => 'Hiçbir kayıt seçilmedi.',
                'must-select-a-mass-action-option' => 'Toplu işlem seçeneği seçmelisiniz.',
                'must-select-a-mass-action'        => 'Toplu işlem seçmelisiniz.',
            ],

            'toolbar' => [
                'length-of' => ':length nin',
                'of'        => 'nin',
                'per-page'  => 'Sayfa başına',
                'results'   => ':total Sonuçlar',
                'selected'  => ':total Seçilen',

                'mass-actions' => [
                    'select-action' => 'Eylem Seç',
                    'select-option' => 'Seçenek Seç',
                    'submit'        => 'Gönder',
                ],

                'filter' => [
                    'apply-filters-btn' => 'Filtreleri Uygula',
                    'back-btn'          => 'Geri',
                    'create-new-filter' => 'Yeni Filtre Oluştur',
                    'custom-filters'    => 'Özel Filtreler',
                    'delete-error'      => 'Filtre silinirken bir hata oluştu, lütfen tekrar deneyin.',
                    'delete-success'    => 'Filtre başarıyla silindi.',
                    'empty-description' => 'Kaydedilecek seçili filtre yok. Lütfen kaydetmek için filtreleri seçin.',
                    'empty-title'       => 'Kaydedilecek Filtreler Ekle',
                    'name'              => 'Adı',
                    'quick-filters'     => 'Hızlı Filtreler',
                    'save-btn'          => 'Kaydet',
                    'save-filter'       => 'Filtreyi Kaydet',
                    'saved-success'     => 'Filtre başarıyla kaydedildi.',
                    'selected-filters'  => 'Seçilen Filtreler',
                    'title'             => 'Filtre',
                    'update'            => 'Güncelle',
                    'update-filter'     => 'Filtreyi güncelle',
                    'updated-success'   => 'Filtre başarıyla güncellendi.',
                ],

                'search' => [
                    'title' => 'Ara',
                ],
            ],

            'filters' => [
                'select' => 'Seçiniz',
                'title'  => 'Filtreler',

                'dropdown' => [
                    'searchable' => [
                        'atleast-two-chars' => 'En az 2 karakter girin...',
                        'no-results'        => 'Sonuç bulunamadı...',
                    ],
                ],

                'custom-filters' => [
                    'clear-all' => 'Hepsini Temizle',
                    'title'     => 'Özel Filtreler',
                ],

                'boolean-options' => [
                    'false' => 'Yanlış',
                    'true'  => 'Doğru',
                ],

                'date-options' => [
                    'last-month'        => 'Geçen Ay',
                    'last-six-months'   => 'Son 6 Ay',
                    'last-three-months' => 'Son 3 Ay',
                    'this-month'        => 'Bu Ay',
                    'this-week'         => 'Bu Hafta',
                    'this-year'         => 'Bu Yıl',
                    'today'             => 'Bugün',
                    'yesterday'         => 'Dün',
                ],
            ],

            'table' => [
                'actions'              => 'Eylemler',
                'no-records-available' => 'Kullanılabilir Kayıt Yok.',
            ],
        ],

        'modal' => [
            'confirm' => [
                'agree-btn'    => 'Kabul Et',
                'disagree-btn' => 'Reddet',
                'message'      => 'Bu işlemi gerçekleştirmek istediğinizden emin misiniz?',
                'title'        => 'Emin misiniz?',
            ],
        ],

        'products' => [
            'search' => [
                'add-btn'       => 'Seçilen Ürünü Ekle',
                'empty-info'    => 'Arama terimi için hiçbir ürün bulunamadı.',
                'empty-title'   => 'Hiçbir ürün bulunamadı',
                'product-image' => 'Ürün Resmi',
                'qty'           => ':qty Mevcut',
                'sku'           => 'Stok Kodu - :sku',
                'title'         => 'Ürünleri Seç',
            ],
        ],

        'media' => [
            'images' => [
                'add-image-btn'     => 'Resim Ekle',
                'ai-add-image-btn'  => 'Sihirli AI',
                'ai-btn-info'       => 'Resim Oluştur',
                'allowed-types'     => 'png, jpeg, jpg',
                'not-allowed-error' => 'Yalnızca resim dosyaları (.jpeg, .jpg, .png, ..) izin verilir.',

                'ai-generation' => [
                    '1024x1024'        => '1024x1024',
                    '1024x1792'        => '1024x1792',
                    '1792x1024'        => '1792x1024',
                    'apply'            => 'Uygula',
                    'dall-e-2'         => 'Dall.E 2',
                    'dall-e-3'         => 'Dall.E 3',
                    'generate'         => 'Oluştur',
                    'generating'       => 'Oluşturuluyor...',
                    'hd'               => 'HD',
                    'model'            => 'Model',
                    'number-of-images' => 'Resim Sayısı',
                    'prompt'           => 'İpucu',
                    'quality'          => 'Kalite',
                    'regenerate'       => 'Yeniden Oluştur',
                    'regenerating'     => 'Yeniden Oluşturuluyor...',
                    'size'             => 'Boyut',
                    'standard'         => 'Standart',
                    'title'            => 'AI İmaj Oluşturma',
                ],

                'placeholders'  => [
                    'front'     => 'Ön',
                    'next'      => 'Sonraki',
                    'size'      => 'Boyut',
                    'use-cases' => 'Kullanım Alanları',
                    'zoom'      => 'Yakınlaştırma',
                ],
            ],

            'videos' => [
                'add-video-btn'     => 'Video Ekle',
                'allowed-types'     => 'mp4, webm, mkv',
                'not-allowed-error' => 'Yalnızca video dosyaları (.mp4, .mov, .ogg ..) izin verilir.',
            ],
        ],

        'tinymce' => [
            'ai-btn-tile' => 'Sihirli AI',

            'ai-generation' => [
                'apply'                  => 'Uygula',
                'deepseek-r1-8b'         => 'DeepSeek R1 (8b)',
                'enabled'                => 'Etkin',
                'gemini-2-0-flash'       => 'Gemini 2.0 Flash',
                'generate'               => 'Oluştur',
                'generated-content'      => 'Oluşturulan İçerik',
                'generated-content-info' => 'AI içeriği yanıltıcı olabilir. Lütfen oluşturulan içeriği uygulamadan önce gözden geçirin.',
                'generating'             => 'Oluşturuluyor...',
                'gpt-4-turbo'            => 'OpenAI gpt-4 Turbo',
                'gpt-4o'                 => 'OpenAI gpt-4o',
                'gpt-4o-mini'            => 'OpenAI gpt-4o mini',
                'llama-groq'             => 'Llama 3.3 (Groq)',
                'llama3-1-8b'            => 'Llama 3.1 (8B)',
                'llama3-2-1b'            => 'Llama 3.2 (1B)',
                'llama3-2-3b'            => 'Llama 3.2 (3B)',
                'llama3-8b'              => 'Llama 3 (8B)',
                'llava-7b'               => 'Llava (7b)',
                'mistral-7b'             => 'Mistral (7b)',
                'model'                  => 'Model',
                'orca-mini'              => 'Orca Mini',
                'phi3-5'                 => 'Phi 3.5',
                'prompt'                 => 'İpucu',
                'qwen2-5-0-5b'           => 'Qwen 2.5 (0.5b)',
                'qwen2-5-1-5b'           => 'Qwen 2.5 (1.5b)',
                'qwen2-5-14b'            => 'Qwen 2.5 (14b)',
                'qwen2-5-3b'             => 'Qwen 2.5 (3b)',
                'qwen2-5-7b'             => 'Qwen 2.5 (7b)',
                'starling-lm-7b'         => 'Starling-lm (7b)',
                'title'                  => 'AI Yardımı',
                'vicuna-13b'             => 'Vicuna (13b)',
                'vicuna-7b'              => 'Vicuna (7b)',
            ],
        ],
    ],

    'acl' => [
        'addresses'                => 'Adresler',
        'attribute-families'       => 'Özellik Aileleri',
        'attributes'               => 'Özellikler',
        'campaigns'                => 'Kampanyalar',
        'cancel'                   => 'İptal',
        'cart-rules'               => 'Sepet Kuralları',
        'catalog-rules'            => 'Katalog Kuralları',
        'catalog'                  => 'Katalog',
        'categories'               => 'Kategoriler',
        'channels'                 => 'Kanallar',
        'cms'                      => 'İçerik Yönetim Sistemi',
        'communications'           => 'İletişim',
        'configure'                => 'Yapılandır',
        'copy'                     => 'Kopyala',
        'create'                   => 'Yaratmak',
        'currencies'               => 'Para Birimleri',
        'customers'                => 'Müşteriler',
        'dashboard'                => 'Kontrol Paneli',
        'data-transfer'            => 'Veri aktarımı',
        'delete'                   => 'Sil',
        'edit'                     => 'Düzenle',
        'email-templates'          => 'E-posta Şablonları',
        'events'                   => 'Etkinlikler',
        'exchange-rates'           => 'Döviz Kurları',
        'gdpr'                     => 'KVKK',
        'groups'                   => 'Gruplar',
        'import'                   => 'İçe aktarmak',
        'imports'                  => 'İthalat',
        'inventory-sources'        => 'Envanter Kaynakları',
        'invoices'                 => 'Faturalar',
        'locales'                  => 'Yerel Ayarlar',
        'marketing'                => 'Pazarlama',
        'newsletter-subscriptions' => 'Bülten Abonelikleri',
        'note'                     => 'Not',
        'orders'                   => 'Siparişler',
        'products'                 => 'Ürünler',
        'promotions'               => 'Promosyonlar',
        'refunds'                  => 'İade İşlemleri',
        'reporting'                => 'Raporlama',
        'reviews'                  => 'Yorumlar',
        'roles'                    => 'Roller',
        'sales'                    => 'Satışlar',
        'search-seo'               => 'Arama ve SEO',
        'search-synonyms'          => 'Arama Eşanlamlıları',
        'search-terms'             => 'Arama Terimleri',
        'settings'                 => 'Ayarlar',
        'shipments'                => 'Gönderiler',
        'sitemaps'                 => 'Site Haritaları',
        'subscribers'              => 'Bülten Aboneleri',
        'tax-categories'           => 'Vergi Kategorileri',
        'tax-rates'                => 'Vergi Oranları',
        'taxes'                    => 'Vergiler',
        'themes'                   => 'Tema Seçenekleri',
        'transactions'             => 'İşlemler',
        'url-rewrites'             => 'URL Yeniden Yazma',
        'users'                    => 'Kullanıcılar',
        'view'                     => 'Görüntüle',
    ],

    'errors' => [
        'dashboard' => 'Kontrol Paneli',
        'go-back'   => 'Geri Git',
        'support'   => 'Sorun devam ederse, yardım için <a href=":link" class=":class">:email</a> adresinden bizimle iletişime geçin.',

        '404' => [
            'description' => 'Oops! Aradığınız sayfa tatile çıkmış gibi görünüyor. Aradığınızı bulamıyor gibi görünüyoruz.',
            'title'       => '404 Sayfa Bulunamadı',
        ],

        '401' => [
            'description' => 'Oops! Bu sayfaya erişiminiz izin verilmiyor gibi görünüyor. Gerekli kimlik bilgilerini eksik gördük.',
            'title'       => '401 Yetkisiz Erişim',
        ],

        '403' => [
            'description' => 'Oops! Bu sayfa erişime kapalı gibi görünüyor. İçeriği görüntülemek için gereken izinlere sahip olmadığınız görünüyor.',
            'title'       => '403 Yasak',
        ],

        '500' => [
            'description' => 'Oops! Bir şeyler ters gitti. Aradığınız sayfayı yüklerken sorun yaşadığımız görünüyor.',
            'title'       => '500 İç Sunucu Hatası',
        ],

        '503' => [
            'description' => 'Oops! Geçici olarak bakım için kapalı gibi görünüyoruz. Lütfen biraz sonra tekrar deneyin.',
            'title'       => '503 Hizmet Kullanılamıyor',
        ],
    ],

    'export' => [
        'csv'        => 'CSV',
        'download'   => 'İndir',
        'export'     => 'Dışa Aktar',
        'no-records' => 'Dışa aktarılacak hiçbir şey yok',
        'xls'        => 'XLS',
        'xlsx'       => 'XLSX',
    ],

    'validations' => [
        'slug-being-used' => 'Bu slug ya kategorilerde ya da ürünlerde kullanılıyor.',
        'slug-reserved'   => 'Bu slug ayrılmış durumda.',
    ],

    'footer' => [
        'copy-right' => 'Tarafından desteklenmektedir <a href="https://bagisto.com/" target="_blank">Bagisto</a>, Bir Topluluk Projesi <a href="https://webkul.com/" target="_blank">Webkul</a>',
    ],

    'emails' => [
        'dear'   => 'Sayın :admin_name',
        'thanks' => 'Herhangi bir yardıma ihtiyacınız varsa, lütfen bize <a href=":link" style=":style">:email</a> adresinden ulaşın.<br/>Teşekkürler!',

        'admin' => [
            'forgot-password' => [
                'description'    => 'Bu e-postayı hesabınız için bir şifre sıfırlama isteği aldığımız için alıyorsunuz.',
                'greeting'       => 'Şifrenizi mi unuttunuz!',
                'reset-password' => 'Şifreyi Sıfırla',
                'subject'        => 'Şifre Sıfırlama E-postası',
            ],
        ],

        'customers' => [
            'registration' => [
                'description' => 'Yeni bir müşteri hesabı başarıyla oluşturuldu. Artık e-posta adreslerini ve şifre bilgilerini kullanarak giriş yapabilirler. Giriş yaptıktan sonra geçmiş siparişleri inceleme, istek listelerini yönetme ve hesap bilgilerini güncelleme dahil olmak üzere çeşitli hizmetlere erişebilecekler.',
                'greeting'    => 'Aramıza yeni kaydolan yeni müşterimiz :customer_name `e sıcak bir karşılama dileriz!',
                'subject'     => 'Yeni Müşteri Kaydı',
            ],

            'gdpr' => [
                'new-delete-request' => 'Veri Silme için Yeni Talep',
                'new-update-request' => 'Veri Güncelleme için Yeni Talep',

                'new-request' => [
                    'customer-name'  => 'Müşteri Adı : ',
                    'delete-summary' => 'Silme isteğinin özeti',
                    'message'        => 'Mesaj : ',
                    'request-status' => 'Talep Durumu : ',
                    'request-type'   => 'Talep Türü : ',
                    'update-summary' => 'Güncelleme isteğinin özeti',
                ],

                'status-update' => [
                    'subject'        => 'GDPR Talebi Güncellendi',
                    'summary'        => 'GDPR Talebinin Durumu Güncellendi',
                    'request-status' => 'Talep Durumu:',
                    'request-type'   => 'Talep Türü:',
                    'message'        => 'Mesaj:',
                ],
            ],
        ],

        'orders' => [
            'created' => [
                'greeting' => ':created_at tarihinde yerleştirilen yeni bir Siparişiniz var :order_id',
                'subject'  => 'Yeni Sipariş Onayı',
                'summary'  => 'Sipariş Özeti',
                'title'    => 'Sipariş Onayı!',
            ],

            'invoiced' => [
                'greeting' => ':created_at tarihinde oluşturulan siparişiniz için faturanız #:invoice_id',
                'subject'  => 'Yeni Fatura Onayı',
                'summary'  => 'Fatura Özeti',
                'title'    => 'Fatura Onayı!',
            ],

            'shipped' => [
                'greeting' => ':created_at tarihinde yerleştirilen siparişi gönderdiniz :order_id',
                'subject'  => 'Yeni Gönderi Onayı',
                'summary'  => 'Gönderi Özeti',
                'title'    => 'Sipariş Gönderildi!',
            ],

            'inventory-source' => [
                'greeting' => ':created_at tarihinde yerleştirilen siparişi gönderdiniz :order_id',
                'subject'  => 'Yeni Gönderi Onayı',
                'summary'  => 'Gönderi Özeti',
                'title'    => 'Sipariş Gönderildi!',
            ],

            'refunded' => [
                'greeting' => ':created_at tarihinde yerleştirilen siparişiniz için iade yaptınız :order_id',
                'subject'  => 'Yeni İade Onayı',
                'summary'  => 'İade Özeti',
                'title'    => 'Sipariş İade Edildi!',
            ],

            'canceled' => [
                'greeting' => ':created_at tarihinde yerleştirilen siparişi iptal ettiniz :order_id',
                'subject'  => 'Yeni Sipariş İptali',
                'summary'  => 'Sipariş Özeti',
                'title'    => 'Sipariş İptal Edildi!',
            ],

            'billing-address'            => 'Fatura Adresi',
            'carrier'                    => 'Taşıyıcı',
            'contact'                    => 'İletişim',
            'discount'                   => 'İndirim',
            'excl-tax'                   => 'Vergi Hariç: ',
            'grand-total'                => 'Genel Toplam',
            'name'                       => 'Ad',
            'payment'                    => 'Ödeme',
            'price'                      => 'Fiyat',
            'qty'                        => 'Adet',
            'shipping-address'           => 'Teslimat Adresi',
            'shipping-handling-excl-tax' => 'Kargo İşlemi (Vergi Hariç)',
            'shipping-handling-incl-tax' => 'Kargo İşlemi (Vergi Dahil)',
            'shipping-handling'          => 'Kargo İşlemi',
            'shipping'                   => 'Kargo',
            'sku'                        => 'SKU',
            'subtotal-excl-tax'          => 'Ara Toplam (Vergi Hariç)',
            'subtotal-incl-tax'          => 'Ara Toplam (Vergi Dahil)',
            'subtotal'                   => 'Ara Toplam',
            'tax'                        => 'Vergi',
            'tracking-number'            => 'Takip Numarası: :tracking_number',
        ],
    ],
];
