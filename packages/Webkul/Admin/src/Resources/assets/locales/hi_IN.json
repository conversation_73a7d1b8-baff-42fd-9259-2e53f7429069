{"code": "hi_IN", "messages": {"_default": "यह {field} मान्य नहीं है", "alpha": "{field} फ़ील्ड में केवल वर्णात्मक अक्षर हो सकते हैं", "alpha_num": "{field} फ़ील्ड में केवल वर्णात्मक और संख्यात्मक अक्षर हो सकते हैं", "alpha_dash": "{field} फ़ील्ड में वर्णात्मक और संख्यात्मक अक्षरों के साथ डैश और अंडरस्कोर हो सकते हैं", "alpha_spaces": "{field} फ़ील्ड में केवल वर्णात्मक अक्षर और अंतर हो सकते हैं", "between": "{field} फ़ील्ड 0:{min} और 1:{max} के बीच होना चाहिए", "confirmed": "{field} फ़ील्ड की पुष्टि मेल नहीं खाती", "digits": "{field} फ़ील्ड संख्यात्मक होनी चाहिए और बिल्कुल 0:{length} अंक होने चाहिए", "dimensions": "{field} फ़ील्ड 0:{width} पिक्सेल और 1:{height} पिक्सेल होना चाहिए", "email": "{field} फ़ील्ड में एक मान्य ईमेल होना चाहिए", "not_one_of": "{field} फ़ील्ड मान्य मूल्य नहीं है", "ext": "{field} फ़ील्ड में मान्य फ़ाइल नहीं है", "image": "{field} फ़ील्ड एक छवि होनी चाहिए", "integer": "{field} फ़ील्ड एक पूर्णांक होना चाहिए", "length": "{field} फ़ील्ड 0:{length} लंबा होना चाहिए", "max_value": "{field} फ़ील्ड 0:{max} या उससे कम होना चाहिए", "max": "{field} फ़ील्ड 0:{length} अक्षरों से अधिक नहीं हो सकता", "mimes": "{field} फ़ील्ड को मान्य फ़ाइल प्रकार होना चाहिए", "min_value": "{field} फ़ील्ड 0:{min} या उससे अधिक होना चाहिए", "min": "{field} फ़ील्ड कम से कम 0:{length} अक्षरों का होना चाहिए", "numeric": "{field} फ़ील्ड में केवल संख्याएँ हो सकती हैं", "one_of": "{field} फ़ील्ड मान्य मूल्य नहीं है", "regex": "{field} फ़ील्ड का प्रारूप अवैध है", "required_if": "{field} फ़ील्ड आवश्यक है", "required": "{field} फ़ील्ड आवश्यक है", "size": "{field} फ़ील्ड का आकार 0:{size}KB से कम होना चाहिए", "url": "{field} फ़ील्ड में एक मान्य URL नहीं है"}}