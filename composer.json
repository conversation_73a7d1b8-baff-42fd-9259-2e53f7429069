{"name": "bagisto/bagisto", "type": "project", "description": "Bagisto Laravel E-Commerce", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "ext-calendar": "*", "ext-curl": "*", "ext-intl": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-pdo": "*", "ext-pdo_mysql": "*", "ext-tokenizer": "*", "astrotomic/laravel-translatable": "^11.0.0", "bagisto/image-cache": "dev-master", "bagistoplus/visual": "^1.0@alpha", "baora/purple": "dev-main", "barryvdh/laravel-dompdf": "^2.0.0", "codenteq/stripe-payment-gateway": "^1.5", "diglactic/laravel-breadcrumbs": "^9.0", "elasticsearch/elasticsearch": "^8.10", "enshrined/svg-sanitize": "^0.16.0", "guzzlehttp/guzzle": "^7.0.1", "intervention/image": "^2.4", "kalnoy/nestedset": "^6.0", "khaled.alshamaa/ar-php": "^6.0.0", "konekt/concord": "^1.2", "laravel/framework": "^11.0", "laravel/octane": "^2.3", "laravel/sanctum": "^4.0", "laravel/socialite": "^5.0", "laravel/tinker": "^2.0", "laravel/ui": "^4.0", "maatwebsite/excel": "^3.1.46", "mpdf/mpdf": "^8.2", "nesbot/carbon": "^2.72.2", "openai-php/laravel": "^0.10.1", "paypal/paypal-checkout-sdk": "1.0.1", "predis/predis": "^2.2", "prettus/l5-repository": "^2.6", "pusher/pusher-php-server": "^7.0", "shetabit/visitor": "^4.1", "spatie/laravel-responsecache": "^7.4", "spatie/laravel-sitemap": "^7.3"}, "require-dev": {"bagisto/laravel-datafaker": "2.2.*", "barryvdh/laravel-debugbar": "^3.8", "fakerphp/faker": "^1.23", "laravel/pint": "^1.19", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "pestphp/pest": "^3.0", "pestphp/pest-plugin-laravel": "^3.0", "phpunit/phpunit": "^11.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Webkul\\Admin\\": "packages/Webkul/Admin/src", "Webkul\\Attribute\\": "packages/Webkul/Attribute/src", "Webkul\\BookingProduct\\": "packages/Webkul/BookingProduct/src", "Webkul\\CartRule\\": "packages/Webkul/CartRule/src", "Webkul\\CatalogRule\\": "packages/Webkul/CatalogRule/src", "Webkul\\Category\\": "packages/Webkul/Category/src", "Webkul\\Checkout\\": "packages/Webkul/Checkout/src", "Webkul\\CMS\\": "packages/Webkul/CMS/src", "Webkul\\Core\\": "packages/Webkul/Core/src", "Webkul\\Customer\\": "packages/Webkul/Customer/src", "Webkul\\DataGrid\\": "packages/Webkul/DataGrid/src", "Webkul\\DataTransfer\\": "packages/Webkul/DataTransfer/src", "Webkul\\DebugBar\\": "packages/Webkul/DebugBar/src", "Webkul\\FPC\\": "packages/Webkul/FPC/src", "Webkul\\GDPR\\": "packages/Webkul/GDPR/src", "Webkul\\Installer\\": "packages/Webkul/Installer/src", "Webkul\\Inventory\\": "packages/Webkul/Inventory/src", "Webkul\\MagicAI\\": "packages/Webkul/MagicAI/src", "Webkul\\Marketing\\": "packages/Webkul/Marketing/src", "Webkul\\Notification\\": "packages/Webkul/Notification/src", "Webkul\\Paypal\\": "packages/Webkul/Paypal/src", "Webkul\\Payment\\": "packages/Webkul/Payment/src", "Webkul\\Product\\": "packages/Webkul/Product/src", "Webkul\\Rule\\": "packages/Webkul/Rule/src", "Webkul\\Sales\\": "packages/Webkul/Sales/src", "Webkul\\Shipping\\": "packages/Webkul/Shipping/src", "Webkul\\Shop\\": "packages/Webkul/Shop/src", "Webkul\\Sitemap\\": "packages/Webkul/Sitemap/src", "Webkul\\SocialLogin\\": "packages/Webkul/SocialLogin/src", "Webkul\\SocialShare\\": "packages/Webkul/SocialShare/src", "Webkul\\Tax\\": "packages/Webkul/Tax/src", "Webkul\\Theme\\": "packages/Webkul/Theme/src", "Webkul\\User\\": "packages/Webkul/User/src"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/", "Webkul\\Admin\\Tests\\": "packages/Webkul/Admin/tests", "Webkul\\Core\\Tests\\": "packages/Webkul/Core/tests", "Webkul\\DataGrid\\Tests\\": "packages/Webkul/DataGrid/tests", "Webkul\\Shop\\Tests\\": "packages/Webkul/Shop/tests"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-create-project-cmd": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\"", "@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": ["intervention/image", "laravel/socialite", "shetabit/visitor"]}}, "repositories": [{"type": "path", "url": "packages/*/*", "options": {"symlink": true}}], "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true, "support": {"issues": "https://github.com/bagisto/bagisto/issues", "forum": "https://forums.bagisto.com", "source": "https://github.com/bagisto/bagisto", "docs": "https://devdocs.bagisto.com"}}