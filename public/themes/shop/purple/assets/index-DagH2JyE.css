/*! tailwindcss v4.1.5 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){*,:before,:after,::backdrop{--tw-translate-x:0;--tw-translate-y:0;--tw-translate-z:0;--tw-scale-x:1;--tw-scale-y:1;--tw-scale-z:1;--tw-rotate-x:initial;--tw-rotate-y:initial;--tw-rotate-z:initial;--tw-skew-x:initial;--tw-skew-y:initial;--tw-space-y-reverse:0;--tw-space-x-reverse:0;--tw-divide-y-reverse:0;--tw-border-style:solid;--tw-leading:initial;--tw-font-weight:initial;--tw-tracking:initial;--tw-shadow:0 0 #0000;--tw-shadow-color:initial;--tw-shadow-alpha:100%;--tw-inset-shadow:0 0 #0000;--tw-inset-shadow-color:initial;--tw-inset-shadow-alpha:100%;--tw-ring-color:initial;--tw-ring-shadow:0 0 #0000;--tw-inset-ring-color:initial;--tw-inset-ring-shadow:0 0 #0000;--tw-ring-inset:initial;--tw-ring-offset-width:0px;--tw-ring-offset-color:#fff;--tw-ring-offset-shadow:0 0 #0000;--tw-outline-style:solid;--tw-blur:initial;--tw-brightness:initial;--tw-contrast:initial;--tw-grayscale:initial;--tw-hue-rotate:initial;--tw-invert:initial;--tw-opacity:initial;--tw-saturate:initial;--tw-sepia:initial;--tw-drop-shadow:initial;--tw-drop-shadow-color:initial;--tw-drop-shadow-alpha:100%;--tw-drop-shadow-size:initial;--tw-backdrop-blur:initial;--tw-backdrop-brightness:initial;--tw-backdrop-contrast:initial;--tw-backdrop-grayscale:initial;--tw-backdrop-hue-rotate:initial;--tw-backdrop-invert:initial;--tw-backdrop-opacity:initial;--tw-backdrop-saturate:initial;--tw-backdrop-sepia:initial;--tw-duration:initial;--tw-ease:initial;--tw-content:""}}}@layer theme{:root,:host{--font-sans:ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--font-serif:ui-serif,Georgia,Cambria,"Times New Roman",Times,serif;--font-mono:ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--color-cyan-100:oklch(95.6% .045 203.388);--color-cyan-600:oklch(60.9% .126 221.723);--color-indigo-100:oklch(93% .034 272.788);--color-indigo-600:oklch(51.1% .262 276.966);--color-gray-200:oklch(92.8% .006 264.531);--color-gray-300:oklch(87.2% .01 258.338);--color-gray-400:oklch(70.7% .022 261.325);--color-gray-500:oklch(55.1% .027 264.364);--color-gray-600:oklch(44.6% .03 256.802);--color-gray-900:oklch(21% .034 264.665);--color-neutral-50:var(--color-neutral-50);--color-neutral-100:var(--color-neutral-100);--color-neutral-200:var(--color-neutral-200);--color-neutral-300:var(--color-neutral-300);--color-neutral-400:var(--color-neutral-400);--color-neutral-500:var(--color-neutral-500);--color-neutral-600:var(--color-neutral-600);--color-neutral-700:var(--color-neutral-700);--color-neutral-800:var(--color-neutral-800);--color-neutral-900:var(--color-neutral-900);--color-neutral-950:var(--color-neutral-950);--color-black:#000;--color-white:#fff;--spacing:.25rem;--container-xs:20rem;--container-sm:24rem;--container-md:28rem;--container-lg:32rem;--container-xl:36rem;--container-2xl:42rem;--container-3xl:48rem;--container-7xl:80rem;--text-xs:.75rem;--text-xs--line-height:calc(1/.75);--text-sm:.875rem;--text-sm--line-height:calc(1.25/.875);--text-base:1rem;--text-base--line-height: 1.5 ;--text-lg:1.125rem;--text-lg--line-height:calc(1.75/1.125);--text-xl:1.25rem;--text-xl--line-height:calc(1.75/1.25);--text-2xl:1.5rem;--text-2xl--line-height:calc(2/1.5);--text-3xl:1.875rem;--text-3xl--line-height: 1.2 ;--text-4xl:2.25rem;--text-4xl--line-height:calc(2.5/2.25);--text-5xl:3rem;--text-5xl--line-height:1;--text-6xl:3.75rem;--text-6xl--line-height:1;--font-weight-normal:400;--font-weight-medium:500;--font-weight-semibold:600;--font-weight-bold:700;--font-weight-extrabold:800;--tracking-tight:-.025em;--leading-tight:1.25;--leading-relaxed:1.625;--radius-md:.375rem;--radius-lg:.5rem;--radius-2xl:1rem;--ease-in:cubic-bezier(.4,0,1,1);--ease-out:cubic-bezier(0,0,.2,1);--ease-in-out:cubic-bezier(.4,0,.2,1);--animate-spin:spin 1s linear infinite;--blur-sm:8px;--aspect-video:16/9;--default-transition-duration:.15s;--default-transition-timing-function:cubic-bezier(.4,0,.2,1);--default-font-family:var(--font-sans);--default-mono-font-family:var(--font-mono);--color-on-background:var(--color-on-background);--color-background:var(--color-background);--color-background-50:var(--color-background-50);--color-background-100:var(--color-background-100);--color-background-200:var(--color-background-200);--color-background-300:var(--color-background-300);--color-background-400:var(--color-background-400);--color-background-500:var(--color-background-500);--color-background-600:var(--color-background-600);--color-background-700:var(--color-background-700);--color-background-800:var(--color-background-800);--color-background-900:var(--color-background-900);--color-background-950:var(--color-background-950);--color-on-primary:var(--color-on-primary);--color-primary:var(--color-primary);--color-primary-50:var(--color-primary-50);--color-primary-100:var(--color-primary-100);--color-primary-200:var(--color-primary-200);--color-primary-300:var(--color-primary-300);--color-primary-400:var(--color-primary-400);--color-primary-500:var(--color-primary-500);--color-primary-600:var(--color-primary-600);--color-primary-700:var(--color-primary-700);--color-primary-800:var(--color-primary-800);--color-primary-900:var(--color-primary-900);--color-primary-950:var(--color-primary-950);--color-on-secondary:var(--color-on-secondary);--color-secondary:var(--color-secondary);--color-secondary-50:var(--color-secondary-50);--color-secondary-100:var(--color-secondary-100);--color-secondary-200:var(--color-secondary-200);--color-secondary-300:var(--color-secondary-300);--color-secondary-400:var(--color-secondary-400);--color-secondary-500:var(--color-secondary-500);--color-secondary-600:var(--color-secondary-600);--color-secondary-700:var(--color-secondary-700);--color-secondary-800:var(--color-secondary-800);--color-secondary-900:var(--color-secondary-900);--color-secondary-950:var(--color-secondary-950);--color-on-accent:var(--color-on-accent);--color-accent:var(--color-accent);--color-accent-50:var(--color-accent-50);--color-accent-100:var(--color-accent-100);--color-accent-200:var(--color-accent-200);--color-accent-300:var(--color-accent-300);--color-accent-400:var(--color-accent-400);--color-accent-500:var(--color-accent-500);--color-accent-600:var(--color-accent-600);--color-accent-700:var(--color-accent-700);--color-accent-800:var(--color-accent-800);--color-accent-900:var(--color-accent-900);--color-accent-950:var(--color-accent-950);--color-on-neutral:var(--color-on-neutral);--color-neutral:var(--color-neutral);--color-on-surface:var(--color-on-surface);--color-surface:var(--color-surface);--color-surface-50:var(--color-surface-50);--color-surface-100:var(--color-surface-100);--color-surface-200:var(--color-surface-200);--color-surface-300:var(--color-surface-300);--color-surface-400:var(--color-surface-400);--color-surface-500:var(--color-surface-500);--color-surface-600:var(--color-surface-600);--color-surface-700:var(--color-surface-700);--color-surface-800:var(--color-surface-800);--color-surface-900:var(--color-surface-900);--color-surface-950:var(--color-surface-950);--color-on-surface-alt:var(--color-on-surface-alt);--color-surface-alt:var(--color-surface-alt);--color-surface-alt-50:var(--color-surface-alt-50);--color-surface-alt-100:var(--color-surface-alt-100);--color-surface-alt-200:var(--color-surface-alt-200);--color-surface-alt-300:var(--color-surface-alt-300);--color-surface-alt-400:var(--color-surface-alt-400);--color-surface-alt-500:var(--color-surface-alt-500);--color-surface-alt-600:var(--color-surface-alt-600);--color-surface-alt-700:var(--color-surface-alt-700);--color-surface-alt-800:var(--color-surface-alt-800);--color-surface-alt-900:var(--color-surface-alt-900);--color-surface-alt-950:var(--color-surface-alt-950);--color-on-success:var(--color-on-success);--color-success:var(--color-success);--color-success-50:var(--color-success-50);--color-success-100:var(--color-success-100);--color-success-200:var(--color-success-200);--color-success-300:var(--color-success-300);--color-success-400:var(--color-success-400);--color-success-500:var(--color-success-500);--color-success-600:var(--color-success-600);--color-success-700:var(--color-success-700);--color-success-800:var(--color-success-800);--color-success-900:var(--color-success-900);--color-success-950:var(--color-success-950);--color-on-warning:var(--color-on-warning);--color-warning:var(--color-warning);--color-warning-50:var(--color-warning-50);--color-warning-100:var(--color-warning-100);--color-warning-200:var(--color-warning-200);--color-warning-300:var(--color-warning-300);--color-warning-400:var(--color-warning-400);--color-warning-500:var(--color-warning-500);--color-warning-600:var(--color-warning-600);--color-warning-700:var(--color-warning-700);--color-warning-800:var(--color-warning-800);--color-warning-900:var(--color-warning-900);--color-warning-950:var(--color-warning-950);--color-on-danger:var(--color-on-danger);--color-danger:var(--color-danger);--color-danger-50:var(--color-danger-50);--color-danger-100:var(--color-danger-100);--color-danger-200:var(--color-danger-200);--color-danger-300:var(--color-danger-300);--color-danger-400:var(--color-danger-400);--color-danger-500:var(--color-danger-500);--color-danger-600:var(--color-danger-600);--color-danger-700:var(--color-danger-700);--color-danger-800:var(--color-danger-800);--color-danger-900:var(--color-danger-900);--color-danger-950:var(--color-danger-950);--color-on-info:var(--color-on-info);--color-info:var(--color-info);--color-info-50:var(--color-info-50);--color-info-100:var(--color-info-100);--color-info-200:var(--color-info-200);--color-info-300:var(--color-info-300);--color-info-400:var(--color-info-400);--color-info-500:var(--color-info-500);--color-info-600:var(--color-info-600);--color-info-700:var(--color-info-700);--color-info-800:var(--color-info-800);--color-info-900:var(--color-info-900);--color-info-950:var(--color-info-950);--animate-slide-in-down:slideInDown .3s ease-out forwards;--animate-slide-out-up:slideOutUp .3s ease-in forwards;--animate-slide-in-up:slideInUp .3s ease-out forwards;--animate-slide-out-down:slideOutDown .3s ease-in forwards}}@layer base{*,:after,:before,::backdrop{box-sizing:border-box;border:0 solid;margin:0;padding:0}::file-selector-button{box-sizing:border-box;border:0 solid;margin:0;padding:0}html,:host{-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;line-height:1.5;font-family:var(--default-font-family,ui-sans-serif,system-ui,sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji");font-feature-settings:var(--default-font-feature-settings,normal);font-variation-settings:var(--default-font-variation-settings,normal);-webkit-tap-highlight-color:transparent}hr{height:0;color:inherit;border-top-width:1px}abbr:where([title]){-webkit-text-decoration:underline dotted;text-decoration:underline dotted}h1,h2,h3,h4,h5,h6{font-size:inherit;font-weight:inherit}a{color:inherit;-webkit-text-decoration:inherit;text-decoration:inherit}b,strong{font-weight:bolder}code,kbd,samp,pre{font-family:var(--default-mono-font-family,ui-monospace,SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace);font-feature-settings:var(--default-mono-font-feature-settings,normal);font-variation-settings:var(--default-mono-font-variation-settings,normal);font-size:1em}small{font-size:80%}sub,sup{vertical-align:baseline;font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}table{text-indent:0;border-color:inherit;border-collapse:collapse}:-moz-focusring{outline:auto}progress{vertical-align:baseline}summary{display:list-item}ol,ul,menu{list-style:none}img,svg,video,canvas,audio,iframe,embed,object{vertical-align:middle;display:block}img,video{max-width:100%;height:auto}button,input,select,optgroup,textarea{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}::file-selector-button{font:inherit;font-feature-settings:inherit;font-variation-settings:inherit;letter-spacing:inherit;color:inherit;opacity:1;background-color:#0000;border-radius:0}:where(select:is([multiple],[size])) optgroup{font-weight:bolder}:where(select:is([multiple],[size])) optgroup option{padding-inline-start:20px}::file-selector-button{margin-inline-end:4px}::placeholder{opacity:1}@supports (not ((-webkit-appearance:-apple-pay-button))) or (contain-intrinsic-size:1px){::placeholder{color:currentColor}@supports (color:color-mix(in lab,red,red)){::placeholder{color:color-mix(in oklab,currentcolor 50%,transparent)}}}textarea{resize:vertical}::-webkit-search-decoration{-webkit-appearance:none}::-webkit-date-and-time-value{min-height:1lh;text-align:inherit}::-webkit-datetime-edit{display:inline-flex}::-webkit-datetime-edit-fields-wrapper{padding:0}::-webkit-datetime-edit{padding-block:0}::-webkit-datetime-edit-year-field{padding-block:0}::-webkit-datetime-edit-month-field{padding-block:0}::-webkit-datetime-edit-day-field{padding-block:0}::-webkit-datetime-edit-hour-field{padding-block:0}::-webkit-datetime-edit-minute-field{padding-block:0}::-webkit-datetime-edit-second-field{padding-block:0}::-webkit-datetime-edit-millisecond-field{padding-block:0}::-webkit-datetime-edit-meridiem-field{padding-block:0}:-moz-ui-invalid{box-shadow:none}button,input:where([type=button],[type=reset],[type=submit]){-webkit-appearance:button;-moz-appearance:button;appearance:button}::file-selector-button{-webkit-appearance:button;-moz-appearance:button;appearance:button}::-webkit-inner-spin-button{height:auto}::-webkit-outer-spin-button{height:auto}[hidden]:where(:not([hidden=until-found])){display:none!important}:root,[data-color-scheme]{--depth:1;background-color:var(--color-background);color:var(--color-on-background)}*,:after,:before,::backdrop{border-color:var(--color-on-background)}::file-selector-button{border-color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){::file-selector-button{border-color:color-mix(in oklab,var(--color-on-background)8%,transparent)}:is(*){border-color:color-mix(in oklab,var(--color-on-background)8%,transparent)}}.box{border-radius:var(--box-radius,0);border-width:var(--box-border-width,0px)}.label-pending,.label-processing,.label-closed,.label-canceled,.label-info,.label-fraud,.label-pending_payment,.label-completed,.label-active{max-width:max-content;padding-inline:calc(var(--spacing)*2);text-align:center;font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height));--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold);border-radius:3.40282e38px;padding-block:1px}.label-pending,.label-pending_payment{background-color:var(--color-warning);color:var(--color-warning-50)}.label-processing{background-color:var(--color-cyan-600);color:var(--color-cyan-100)}.label-completed,.label-active{background-color:var(--color-success);color:var(--color-success-50)}.label-closed{background-color:var(--color-indigo-600);color:var(--color-indigo-100)}.label-canceled,.label-fraud{background-color:var(--color-danger);color:var(--color-danger-50)}.label-info{background-color:var(--color-info-400);color:var(--color-info-50)}.icon-eye{background-image:url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgb(110,110,110)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0'%3E%3C/path%3E%3Ccircle cx='12' cy='12' r='3'%3E%3C/circle%3E%3C/svg%3E")}}@layer components{:where(.btn){width:unset}.btn{cursor:pointer;justify-content:center;align-items:center;gap:calc(var(--spacing)*1.5);text-align:center;-webkit-user-select:none;user-select:none;padding-inline:var(--btn-p);color:var(--btn-fg);background-color:var(--btn-bg);height:var(--btn-size);font-size:var(--btn-font-size,.875rem);border-radius:var(--btn-radius,0);border-width:var(--btn-border-width);border-style:solid;border-color:var(--btn-border);--btn-p:1rem;--btn-size-base:.25rem;--btn-size:calc(var(--btn-size-base)*10);--btn-fg:var(--color-on-background);--btn-bg:var(--btn-color,var(--color-surface));--btn-border:var(--btn-bg);flex-shrink:0;font-weight:600;transition-property:color,background-color,border-color,box-shadow;transition-duration:.2s;transition-timing-function:cubic-bezier(0,0,.2,1);display:inline-flex}@supports (color:color-mix(in lab,red,red)){.btn{--btn-border:color-mix(in oklab,var(--btn-bg),#000 calc(var(--depth,1)*5%))}}.btn:hover{--btn-bg:var(--btn-color,var(--color-surface))}@supports (color:color-mix(in lab,red,red)){.btn:hover{--btn-bg:color-mix(in oklab,var(--btn-color,var(--color-surface)),#000 7%)}}.btn:focus-visible{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--btn-color);--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color);--tw-outline-style:none;outline-style:none}.btn:active{--btn-bg:var(--btn-color,var(--color-surface));--btn-border:var(--btn-color,var(--color-surface));--btn-shadow:0 0 0 0 oklch(0% 0 0/0),0 0 0 0 oklch(0% 0 0/0);translate:0 .5px}@supports (color:color-mix(in lab,red,red)){.btn:active{--btn-bg:color-mix(in oklab,var(--btn-color,var(--color-surface)),#000 5%);--btn-border:color-mix(in oklab,var(--btn-color,var(--color-surface)),#000 7%)}}.btn:disabled:not([loading]),.btn[disabled]:not([loading]){pointer-events:none;--btn-border:#0000;--btn-fg:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){:is(.btn:disabled:not([loading]),.btn[disabled]:not([loading])){--btn-fg:color-mix(in oklch,var(--color-on-background)20%,#0000)}}.btn:disabled:not(.btn-link,.btn-ghost,[loading]),.btn[disabled]:not(.btn-link,.btn-ghost,[loading]),.btn.btn-disabled:not(.btn-link,.btn-ghost,[loading]){background-color:var(--color-on-background);box-shadow:none}@supports (color:color-mix(in lab,red,red)){:is(.btn:disabled:not(.btn-link,.btn-ghost,[loading]),.btn[disabled]:not(.btn-link,.btn-ghost,[loading]),.btn.btn-disabled:not(.btn-link,.btn-ghost,[loading])){background-color:color-mix(in oklab,var(--color-on-background)10%,transparent)}}.btn[loading]{pointer-events:none;opacity:.8}.btn-primary{--btn-color:var(--color-primary);--btn-fg:var(--color-on-primary)}.btn-secondary{--btn-color:var(--color-secondary);--btn-fg:var(--color-on-secondary)}.btn-accent{--btn-color:var(--color-accent);--btn-fg:var(--color-on-accent)}.btn-neutral{--btn-color:var(--color-neutral);--btn-fg:var(--color-on-neutral)}.btn-info{--btn-color:var(--color-info);--btn-fg:var(--color-on-info)}.btn-success{--btn-color:var(--color-success);--btn-fg:var(--color-on-success)}.btn-warning{--btn-color:var(--color-warning);--btn-fg:var(--color-on-warning)}.btn-danger{--btn-color:var(--color-danger);--btn-fg:var(--color-on-danger)}.btn-soft:not(:hover,:active,:disabled,[disabled]){--btn-fg:var(--btn-color,var(--color-on-background));--btn-bg:var(--btn-color);--btn-border:var(--btn-color)}@supports (color:color-mix(in lab,red,red)){.btn-soft:not(:hover,:active,:disabled,[disabled]){--btn-bg:color-mix(in oklab,var(--btn-color)8%,var(--color-background));--btn-border:color-mix(in oklab,var(--btn-color)10%,var(--color-background))}}.btn-soft:hover:not(:active,:disabled,[disabled]){--btn-fg:var(--btn-color);--btn-bg:var(--btn-color);--btn-border:var(--btn-color)}@supports (color:color-mix(in lab,red,red)){.btn-soft:hover:not(:active,:disabled,[disabled]){--btn-bg:color-mix(in oklab,var(--btn-color)18%,var(--color-background));--btn-border:color-mix(in oklab,var(--btn-color)20%,var(--color-background))}}.btn-soft:active{--btn-fg:var(--btn-color);--btn-bg:var(--btn-color);--btn-border:var(--btn-color)}@supports (color:color-mix(in lab,red,red)){.btn-soft:active{--btn-bg:color-mix(in oklab,var(--btn-color)25%,var(--color-background));--btn-border:color-mix(in oklab,var(--btn-color)27%,var(--color-background))}}.btn-outline:not(:hover,:active,:disabled,[disabled]){--btn-bg:transparent;--btn-fg:var(--btn-color);--btn-border:var(--btn-color);border-width:max(1px,var(--btn-border-width,1px))}.btn-outline:hover:not(:active,:disabled,[disabled]){border-width:max(1px,var(--btn-border-width,1px));--btn-bg:var(--btn-color,var(--color-on-background));--btn-fg:var(--btn-color);--btn-border:var(--btn-color)}@supports (color:color-mix(in lab,red,red)){.btn-outline:hover:not(:active,:disabled,[disabled]){--btn-bg:color-mix(in oklab,var(--btn-color,var(--color-on-background))10%,var(--color-background))}}.btn-outline:active{border-width:max(1px,var(--btn-border-width,1px));--btn-bg:var(--btn-color,var(--color-on-background));--btn-fg:var(--btn-color);--btn-border:var(--btn-color)}@supports (color:color-mix(in lab,red,red)){.btn-outline:active{--btn-bg:color-mix(in oklab,var(--btn-color,var(--color-on-background))15%,var(--color-background))}}.btn-ghost:not(:hover,:active){--btn-bg:transparent;--btn-border:transparent;--btn-fg:var(--btn-color);border-width:0}.btn-ghost:hover:not(:active,:disabled,[disabled]){--btn-border:transparent;--btn-bg:var(--btn-color,var(--color-on-background));--btn-fg:var(--btn-color);border-width:0}@supports (color:color-mix(in lab,red,red)){.btn-ghost:hover:not(:active,:disabled,[disabled]){--btn-bg:color-mix(in oklab,var(--btn-color,var(--color-on-background))7%,var(--color-background))}}.btn-ghost:active{--btn-border:transparent;--btn-bg:var(--btn-color,var(--color-on-background));--btn-fg:var(--btn-color);border-width:0}@supports (color:color-mix(in lab,red,red)){.btn-ghost:active{--btn-bg:color-mix(in oklab,var(--btn-color,var(--color-on-background))12%,var(--color-background))}}.btn-link{--btn-border:transparent;--btn-bg:transparent;--btn-fg:var(--btn-color);--btn-noise:none;--btn-shadow:none}.btn-link:is(.btn-active,:hover,:active:focus,:focus-visible){--btn-border:transparent;--btn-bg:transparent;text-decoration-line:underline}.btn-xs{--btn-font-size:.6875rem;--btn-p:.5rem;--btn-size:calc(var(--btn-size-base)*6)}.btn-sm{--btn-font-size:.75rem;--btn-p:.75rem;--btn-size:calc(var(--btn-size-base)*8)}.btn-md{--btn-font-size:.875rem;--btn-p:1rem;--btn-size:calc(var(--btn-size-base)*10)}.btn-lg{--btn-font-size:1.125rem;--btn-p:1.25rem;--btn-size:calc(var(--btn-size-base)*12)}.btn-xl{--btn-font-size:1.375rem;--btn-p:1.5rem;--btn-size:calc(var(--btn-size-base)*14)}.btn-square{padding-inline:calc(var(--spacing)*0);width:var(--btn-size);height:var(--btn-size)}.btn-circle{padding-inline:calc(var(--spacing)*0);width:var(--btn-size);height:var(--btn-size);border-radius:3.40282e38px}.btn-wide{width:100%;max-width:calc(var(--spacing)*64)}.btn-block{width:100%}.loading{pointer-events:none;aspect-ratio:1;vertical-align:middle;width:calc(var(--btn-size-base)*6);background-color:currentColor;display:inline-block;-webkit-mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");mask-image:url("data:image/svg+xml,%3Csvg width='24' height='24' stroke='black' viewBox='0 0 24 24' xmlns='http://www.w3.org/2000/svg'%3E%3Cg transform-origin='center'%3E%3Ccircle cx='12' cy='12' r='9.5' fill='none' stroke-width='3' stroke-linecap='round'%3E%3CanimateTransform attributeName='transform' type='rotate' from='0 12 12' to='360 12 12' dur='2s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dasharray' values='0,150;42,150;42,150' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3Canimate attributeName='stroke-dashoffset' values='0;-16;-59' keyTimes='0;0.475;1' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E");-webkit-mask-position:50%;mask-position:50%;-webkit-mask-size:100%;mask-size:100%;-webkit-mask-repeat:no-repeat;mask-repeat:no-repeat}[type=text],[type=email],[type=password],[type=number],[type=date],[type=datetime-local],[type=search],[type=tel],[type=url],input:where(:not([type])),textarea,select{background-color:var(--color-background);-webkit-appearance:none;-moz-appearance:none;appearance:none;width:100%;padding-inline:calc(var(--spacing)*4);height:var(--input-size);border:var(--input-border-width)solid #0000;border-color:var(--input-color);border-radius:var(--input-radius);box-shadow:0 1px var(--input-color) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;--input-size-base:.25rem;--input-size:calc(var(--input-size-base,.25rem)*10);--input-color:var(--color-on-background);background-repeat:no-repeat}@supports (color:color-mix(in lab,red,red)){:is([type=text],[type=email],[type=password],[type=number],[type=date],[type=datetime-local],[type=search],[type=tel],[type=url],input:where(:not([type])),textarea,select){box-shadow:0 1px color-mix(in oklab,var(--input-color)calc(var(--depth)*10%),#0000) inset,0 -1px oklch(100% 0 0/calc(var(--depth)*.1)) inset;--input-color:color-mix(in oklab,var(--color-on-background)20%,#0000)}}:is([type=text],[type=email],[type=password],[type=number],[type=date],[type=datetime-local],[type=search],[type=tel],[type=url],input:where(:not([type])),textarea,select):focus,:is([type=text],[type=email],[type=password],[type=number],[type=date],[type=datetime-local],[type=search],[type=tel],[type=url],input:where(:not([type])),textarea,select):focus-within{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-primary);--tw-outline-style:none;--input-color:var(--color-on-background);border-color:#0000;outline-style:none}:is([type=text],[type=email],[type=password],[type=number],[type=date],[type=datetime-local],[type=search],[type=tel],[type=url],input:where(:not([type])),textarea,select):is(:disabled,[disabled]){border-color:var(--color-surface);background-color:var(--color-surface);color:var(--color-on-background);box-shadow:none}@supports (color:color-mix(in lab,red,red)){:is([type=text],[type=email],[type=password],[type=number],[type=date],[type=datetime-local],[type=search],[type=tel],[type=url],input:where(:not([type])),textarea,select):is(:disabled,[disabled]){color:color-mix(in oklab,var(--color-on-background)40%,transparent)}}:is([type=text],[type=email],[type=password],[type=number],[type=date],[type=datetime-local],[type=search],[type=tel],[type=url],input:where(:not([type])),textarea,select):is(:disabled,[disabled])::placeholder{color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){:is([type=text],[type=email],[type=password],[type=number],[type=date],[type=datetime-local],[type=search],[type=tel],[type=url],input:where(:not([type])),textarea,select):is(:disabled,[disabled])::placeholder{color:color-mix(in oklab,var(--color-on-background)20%,transparent)}}:is([type=text],[type=email],[type=password],[type=number],[type=date],[type=datetime-local],[type=search],[type=tel],[type=url],input:where(:not([type])),textarea,select):is(:disabled,[disabled]):disabled{cursor:not-allowed}textarea{height:auto;padding-block:calc(var(--spacing)*3)}select[multiple]{--input-size:calc(var(--input-size-base,.25rem)*20)}select:not([multiple]){background-image:url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");background-position:right .5rem center;background-size:1.5em 1.5em;padding-right:2.5rem}select:not([multiple]):where(:dir(rtl),[dir=rtl],[dir=rtl] *){background-position:0}[type=checkbox],[type=radio]{-webkit-appearance:none;-moz-appearance:none;appearance:none;padding:calc(var(--spacing)*0);-webkit-user-select:none;user-select:none;height:calc(var(--spacing)*4);width:calc(var(--spacing)*4);border-style:var(--tw-border-style);background-color:var(--color-background);color:var(--color-primary);border-width:1px;border:max(var(--input-border-width),1px)solid #0000;border-color:var(--input-color);--input-color:var(--color-on-background);display:inline-block}:is([type=checkbox],[type=radio]):disabled{cursor:not-allowed;opacity:.5}:is([type=checkbox],[type=radio]):focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow);--tw-ring-color:var(--color-primary);--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color);--tw-outline-style:none;outline-style:none}@supports (color:color-mix(in lab,red,red)){:is([type=checkbox],[type=radio]){--input-color:color-mix(in oklab,var(--color-on-background)20%,#0000)}}[type=checkbox]{border-radius:min(var(--input-radius),.25rem)}[type=radio]{border-radius:3.40282e38px}[type=checkbox]:checked,[type=radio]:checked{background-color:currentColor;background-position:50%;background-repeat:no-repeat;background-size:100% 100%;border-color:#0000}input[type=checkbox]:checked{background-image:url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M12.207 4.793a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0l-2-2a1 1 0 011.414-1.414L6.5 9.086l4.293-4.293a1 1 0 011.414 0z'/%3e%3c/svg%3e")}input[type=radio]:checked{background-image:url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3ccircle cx='8' cy='8' r='3'/%3e%3c/svg%3e")}required:after,label[required]:after{content:"*"}.input-xs{--input-size:calc(var(--input-size-base,.25rem)*6);font-size:.6875rem}.input-xs[type=number]::-webkit-inner-spin-button{margin-block:calc(var(--spacing)*-1);margin-inline-end:calc(var(--spacing)*-3)}.input-sm{--input-size:calc(var(--input-size-base,.25rem)*8);font-size:.75rem}.input-sm[type=number]::-webkit-inner-spin-button{margin-block:calc(var(--spacing)*-2);margin-inline-end:calc(var(--spacing)*-3)}.input-md{--input-size:calc(var(--input-size-base,.25rem)*10);font-size:.875rem}.input-md[type=number]::-webkit-inner-spin-button{margin-block:calc(var(--spacing)*-3);margin-inline-end:calc(var(--spacing)*-3)}.input-lg{--input-size:calc(var(--input-size-base,.25rem)*12);font-size:1.125rem}.input-lg[type=number]::-webkit-inner-spin-button{margin-block:calc(var(--spacing)*-3);margin-inline-end:calc(var(--spacing)*-3)}.input-xl{--input-size:calc(var(--input-size-base,.25rem)*14);font-size:1.375rem}.input-xl[type=number]::-webkit-inner-spin-button{margin-block:calc(var(--spacing)*-4);margin-inline-end:calc(var(--spacing)*-3)}}@layer utilities{.pointer-events-none{pointer-events:none}.collapse{visibility:collapse}.invisible{visibility:hidden}.visible{visibility:visible}.sr-only{clip:rect(0,0,0,0);white-space:nowrap;border-width:0;width:1px;height:1px;margin:-1px;padding:0;position:absolute;overflow:hidden}.\!absolute{position:absolute!important}.absolute{position:absolute}.fixed{position:fixed}.relative{position:relative}.static{position:static}.sticky{position:sticky}.inset-0{inset:calc(var(--spacing)*0)}.inset-y-0{inset-block:calc(var(--spacing)*0)}.start-0{inset-inline-start:calc(var(--spacing)*0)}.start-7{inset-inline-start:calc(var(--spacing)*7)}.end-0{inset-inline-end:calc(var(--spacing)*0)}.-top-2{top:calc(var(--spacing)*-2)}.top-0{top:calc(var(--spacing)*0)}.top-1\/2{top:50%}.top-2{top:calc(var(--spacing)*2)}.top-4{top:calc(var(--spacing)*4)}.top-16{top:calc(var(--spacing)*16)}.top-full{top:100%}.-right-2{right:calc(var(--spacing)*-2)}.right-0{right:calc(var(--spacing)*0)}.right-2{right:calc(var(--spacing)*2)}.right-4{right:calc(var(--spacing)*4)}.bottom-0{bottom:calc(var(--spacing)*0)}.bottom-2{bottom:calc(var(--spacing)*2)}.left-0{left:calc(var(--spacing)*0)}.left-1\/2{left:50%}.left-2{left:calc(var(--spacing)*2)}.left-3{left:calc(var(--spacing)*3)}.left-4{left:calc(var(--spacing)*4)}.isolate{isolation:isolate}.z-0{z-index:0}.z-5{z-index:5}.z-10{z-index:10}.z-20{z-index:20}.z-30{z-index:30}.z-50{z-index:50}.z-\[1000\]{z-index:1000}.z-\[1100\]{z-index:1100}.order-first{order:-9999}.order-last{order:9999}.col-span-1{grid-column:span 1/span 1}.col-span-full{grid-column:1/-1}.row-span-full{grid-row:1/-1}.container{width:100%}@media (min-width:40rem){.container{max-width:40rem}}@media (min-width:48rem){.container{max-width:48rem}}@media (min-width:64rem){.container{max-width:64rem}}@media (min-width:80rem){.container{max-width:80rem}}@media (min-width:96rem){.container{max-width:96rem}}.mx-2{margin-inline:calc(var(--spacing)*2)}.mx-auto{margin-inline:auto}.my-4{margin-block:calc(var(--spacing)*4)}.my-6{margin-block:calc(var(--spacing)*6)}.ms-1{margin-inline-start:calc(var(--spacing)*1)}.ms-2{margin-inline-start:calc(var(--spacing)*2)}.ms-3{margin-inline-start:calc(var(--spacing)*3)}.ms-auto{margin-inline-start:auto}.prose{color:var(--tw-prose-body);--tw-prose-body:var(--color-on-background);--tw-prose-headings:var(--color-on-background);--tw-prose-lead:var(--color-on-background);--tw-prose-links:var(--color-primary);--tw-prose-bold:var(--color-on-background);--tw-prose-counters:oklch(55.1% .027 264.364);--tw-prose-bullets:var(--color-neutral-300);--tw-prose-hr:var(--color-neutral-200);--tw-prose-quotes:var(--color-on-background);--tw-prose-quote-borders:var(--color-neutral-200);--tw-prose-captions:var(--color-neutral-500);--tw-prose-kbd:var(--color-neutral-900);--tw-prose-kbd-shadows:var(--color-neutral-900);--tw-prose-code:var(--color-neutral-900);--tw-prose-pre-code:var(--color-neutral-200);--tw-prose-pre-bg:var(--color-neutral-800);--tw-prose-th-borders:var(--color-neutral-300);--tw-prose-td-borders:var(--color-neutral-200);--tw-prose-invert-body:oklch(87.2% .01 258.338);--tw-prose-invert-headings:#fff;--tw-prose-invert-lead:oklch(70.7% .022 261.325);--tw-prose-invert-links:#fff;--tw-prose-invert-bold:#fff;--tw-prose-invert-counters:oklch(70.7% .022 261.325);--tw-prose-invert-bullets:oklch(44.6% .03 256.802);--tw-prose-invert-hr:oklch(37.3% .034 259.733);--tw-prose-invert-quotes:oklch(96.7% .003 264.542);--tw-prose-invert-quote-borders:oklch(37.3% .034 259.733);--tw-prose-invert-captions:oklch(70.7% .022 261.325);--tw-prose-invert-kbd:#fff;--tw-prose-invert-kbd-shadows:255 255 255;--tw-prose-invert-code:#fff;--tw-prose-invert-pre-code:oklch(87.2% .01 258.338);--tw-prose-invert-pre-bg:#00000080;--tw-prose-invert-th-borders:oklch(44.6% .03 256.802);--tw-prose-invert-td-borders:oklch(37.3% .034 259.733);max-width:65ch;font-size:1rem;line-height:1.75}.prose :where(p):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em}.prose :where([class~=lead]):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-lead);margin-top:1.2em;margin-bottom:1.2em;font-size:1.25em;line-height:1.6}.prose :where(a):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-links);font-weight:500;text-decoration:underline}.prose :where(strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-bold);font-weight:600}.prose :where(a strong):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(blockquote strong):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(thead th strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}.prose :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em;padding-inline-start:1.625em;list-style-type:decimal}.prose :where(ol[type=A]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:upper-alpha}.prose :where(ol[type=a]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:lower-alpha}.prose :where(ol[type=A s]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:upper-alpha}.prose :where(ol[type=a s]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:lower-alpha}.prose :where(ol[type=I]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:upper-roman}.prose :where(ol[type=i]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:lower-roman}.prose :where(ol[type=I s]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:upper-roman}.prose :where(ol[type=i s]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:lower-roman}.prose :where(ol[type="1"]):not(:where([class~=not-prose],[class~=not-prose] *)){list-style-type:decimal}.prose :where(ul):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em;padding-inline-start:1.625em;list-style-type:disc}.prose :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *))::marker{color:var(--tw-prose-counters);font-weight:400}.prose :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *))::marker{color:var(--tw-prose-bullets)}.prose :where(dt):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:1.25em;font-weight:600}.prose :where(hr):not(:where([class~=not-prose],[class~=not-prose] *)){border-color:var(--tw-prose-hr);border-top-width:1px;margin-top:3em;margin-bottom:3em}.prose :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-quotes);border-inline-start-width:.25rem;border-inline-start-color:var(--tw-prose-quote-borders);quotes:"“""”""‘""’";margin-top:1.6em;margin-bottom:1.6em;padding-inline-start:1em;font-style:italic;font-weight:500}.prose :where(blockquote p:first-of-type):not(:where([class~=not-prose],[class~=not-prose] *)):before{content:open-quote}.prose :where(blockquote p:last-of-type):not(:where([class~=not-prose],[class~=not-prose] *)):after{content:close-quote}.prose :where(h1):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:0;margin-bottom:.888889em;font-size:2.25em;font-weight:800;line-height:1.11111}.prose :where(h1 strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-weight:900}.prose :where(h2):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:2em;margin-bottom:1em;font-size:1.5em;font-weight:700;line-height:1.33333}.prose :where(h2 strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-weight:800}.prose :where(h3):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:1.6em;margin-bottom:.6em;font-size:1.25em;font-weight:600;line-height:1.6}.prose :where(h3 strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-weight:700}.prose :where(h4):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);margin-top:1.5em;margin-bottom:.5em;font-weight:600;line-height:1.5}.prose :where(h4 strong):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-weight:700}.prose :where(img):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2em;margin-bottom:2em}.prose :where(picture):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2em;margin-bottom:2em;display:block}.prose :where(video):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2em;margin-bottom:2em}.prose :where(kbd):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-kbd);box-shadow:0 0 0 1px rgb(var(--tw-prose-kbd-shadows)/10%),0 3px rgb(var(--tw-prose-kbd-shadows)/10%);padding-top:.1875em;padding-inline-end:.375em;padding-bottom:.1875em;border-radius:.3125rem;padding-inline-start:.375em;font-family:inherit;font-size:.875em;font-weight:500}.prose :where(code):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-code);font-size:.875em;font-weight:600}.prose :where(code):not(:where([class~=not-prose],[class~=not-prose] *)):before,.prose :where(code):not(:where([class~=not-prose],[class~=not-prose] *)):after{content:"`"}.prose :where(a code):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h1 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}.prose :where(h2 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-size:.875em}.prose :where(h3 code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit;font-size:.9em}.prose :where(h4 code):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(blockquote code):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(thead th code):not(:where([class~=not-prose],[class~=not-prose] *)){color:inherit}.prose :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-pre-code);background-color:var(--tw-prose-pre-bg);padding-top:.857143em;padding-inline-end:1.14286em;padding-bottom:.857143em;border-radius:.375rem;margin-top:1.71429em;margin-bottom:1.71429em;padding-inline-start:1.14286em;font-size:.875em;font-weight:400;line-height:1.71429;overflow-x:auto}.prose :where(pre code):not(:where([class~=not-prose],[class~=not-prose] *)){font-weight:inherit;color:inherit;font-size:inherit;font-family:inherit;line-height:inherit;background-color:#0000;border-width:0;border-radius:0;padding:0}.prose :where(pre code):not(:where([class~=not-prose],[class~=not-prose] *)):before,.prose :where(pre code):not(:where([class~=not-prose],[class~=not-prose] *)):after{content:none}.prose :where(table):not(:where([class~=not-prose],[class~=not-prose] *)){table-layout:auto;width:100%;margin-top:2em;margin-bottom:2em;font-size:.875em;line-height:1.71429}.prose :where(thead):not(:where([class~=not-prose],[class~=not-prose] *)){border-bottom-width:1px;border-bottom-color:var(--tw-prose-th-borders)}.prose :where(thead th):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-headings);vertical-align:bottom;padding-inline-end:.571429em;padding-bottom:.571429em;padding-inline-start:.571429em;font-weight:600}.prose :where(tbody tr):not(:where([class~=not-prose],[class~=not-prose] *)){border-bottom-width:1px;border-bottom-color:var(--tw-prose-td-borders)}.prose :where(tbody tr:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){border-bottom-width:0}.prose :where(tbody td):not(:where([class~=not-prose],[class~=not-prose] *)){vertical-align:baseline}.prose :where(tfoot):not(:where([class~=not-prose],[class~=not-prose] *)){border-top-width:1px;border-top-color:var(--tw-prose-th-borders)}.prose :where(tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)){vertical-align:top}.prose :where(th,td):not(:where([class~=not-prose],[class~=not-prose] *)){text-align:start}.prose :where(figure>*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}.prose :where(figcaption):not(:where([class~=not-prose],[class~=not-prose] *)){color:var(--tw-prose-captions);margin-top:.857143em;font-size:.875em;line-height:1.42857}.prose :where(picture>img):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}.prose :where(li):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.5em;margin-bottom:.5em}.prose :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:.375em}.prose :where(.prose>ul>li p):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.75em;margin-bottom:.75em}.prose :where(.prose>ul>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em}.prose :where(.prose>ul>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:1.25em}.prose :where(.prose>ol>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em}.prose :where(.prose>ol>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:1.25em}.prose :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.75em;margin-bottom:.75em}.prose :where(dl):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.25em;margin-bottom:1.25em}.prose :where(dd):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.5em;padding-inline-start:1.625em}.prose :where(hr+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h2+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h3+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose :where(h4+*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}.prose :where(thead th:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:0}.prose :where(thead th:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:0}.prose :where(tbody td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.571429em;padding-inline-end:.571429em;padding-bottom:.571429em;padding-inline-start:.571429em}.prose :where(tbody td:first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:0}.prose :where(tbody td:last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:0}.prose :where(figure):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2em;margin-bottom:2em}.prose :where(.prose>:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}.prose :where(.prose>:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:0}.prose-sm{font-size:.875rem;line-height:1.71429}.prose-sm :where(p):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em;margin-bottom:1.14286em}.prose-sm :where([class~=lead]):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.888889em;margin-bottom:.888889em;font-size:1.28571em;line-height:1.55556}.prose-sm :where(blockquote):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.33333em;margin-bottom:1.33333em;padding-inline-start:1.11111em}.prose-sm :where(h1):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:.8em;font-size:2.14286em;line-height:1.2}.prose-sm :where(h2):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.6em;margin-bottom:.8em;font-size:1.42857em;line-height:1.4}.prose-sm :where(h3):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.55556em;margin-bottom:.444444em;font-size:1.28571em;line-height:1.55556}.prose-sm :where(h4):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.42857em;margin-bottom:.571429em;line-height:1.42857}.prose-sm :where(img):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(picture):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.71429em;margin-bottom:1.71429em}.prose-sm :where(picture>img):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}.prose-sm :where(video):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.71429em;margin-bottom:1.71429em}.prose-sm :where(kbd):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.142857em;padding-inline-end:.357143em;padding-bottom:.142857em;border-radius:.3125rem;padding-inline-start:.357143em;font-size:.857143em}.prose-sm :where(code):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.857143em}.prose-sm :where(h2 code):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.9em}.prose-sm :where(h3 code):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.888889em}.prose-sm :where(pre):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.666667em;padding-inline-end:1em;padding-bottom:.666667em;border-radius:.25rem;margin-top:1.66667em;margin-bottom:1.66667em;padding-inline-start:1em;font-size:.857143em;line-height:1.66667}.prose-sm :where(ol):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(ul):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em;margin-bottom:1.14286em;padding-inline-start:1.57143em}.prose-sm :where(li):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.285714em;margin-bottom:.285714em}.prose-sm :where(ol>li):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(ul>li):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:.428571em}.prose-sm :where(.prose-sm>ul>li p):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.571429em;margin-bottom:.571429em}.prose-sm :where(.prose-sm>ul>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em}.prose-sm :where(.prose-sm>ul>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:1.14286em}.prose-sm :where(.prose-sm>ol>li>p:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em}.prose-sm :where(.prose-sm>ol>li>p:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:1.14286em}.prose-sm :where(ul ul,ul ol,ol ul,ol ol):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.571429em;margin-bottom:.571429em}.prose-sm :where(dl):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em;margin-bottom:1.14286em}.prose-sm :where(dt):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.14286em}.prose-sm :where(dd):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.285714em;padding-inline-start:1.57143em}.prose-sm :where(hr):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:2.85714em;margin-bottom:2.85714em}.prose-sm :where(hr+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(h2+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(h3+*):not(:where([class~=not-prose],[class~=not-prose] *)),.prose-sm :where(h4+*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}.prose-sm :where(table):not(:where([class~=not-prose],[class~=not-prose] *)){font-size:.857143em;line-height:1.5}.prose-sm :where(thead th):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:1em;padding-bottom:.666667em;padding-inline-start:1em}.prose-sm :where(thead th:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:0}.prose-sm :where(thead th:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:0}.prose-sm :where(tbody td,tfoot td):not(:where([class~=not-prose],[class~=not-prose] *)){padding-top:.666667em;padding-inline-end:1em;padding-bottom:.666667em;padding-inline-start:1em}.prose-sm :where(tbody td:first-child,tfoot td:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-start:0}.prose-sm :where(tbody td:last-child,tfoot td:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){padding-inline-end:0}.prose-sm :where(figure):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:1.71429em;margin-bottom:1.71429em}.prose-sm :where(figure>*):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0;margin-bottom:0}.prose-sm :where(figcaption):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:.666667em;font-size:.857143em;line-height:1.33333}.prose-sm :where(.prose-sm>:first-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-top:0}.prose-sm :where(.prose-sm>:last-child):not(:where([class~=not-prose],[class~=not-prose] *)){margin-bottom:0}.-mt-1{margin-top:calc(var(--spacing)*-1)}.-mt-2{margin-top:calc(var(--spacing)*-2)}.mt-1{margin-top:calc(var(--spacing)*1)}.mt-1\.5{margin-top:calc(var(--spacing)*1.5)}.mt-2{margin-top:calc(var(--spacing)*2)}.mt-3{margin-top:calc(var(--spacing)*3)}.mt-4{margin-top:calc(var(--spacing)*4)}.mt-6{margin-top:calc(var(--spacing)*6)}.mt-8{margin-top:calc(var(--spacing)*8)}.mt-12{margin-top:calc(var(--spacing)*12)}.mr-1{margin-right:calc(var(--spacing)*1)}.mr-2{margin-right:calc(var(--spacing)*2)}.mr-3{margin-right:calc(var(--spacing)*3)}.mr-6{margin-right:calc(var(--spacing)*6)}.mr-auto{margin-right:auto}.-mb-px{margin-bottom:-1px}.mb-1{margin-bottom:calc(var(--spacing)*1)}.mb-2{margin-bottom:calc(var(--spacing)*2)}.mb-3{margin-bottom:calc(var(--spacing)*3)}.mb-4{margin-bottom:calc(var(--spacing)*4)}.mb-5{margin-bottom:calc(var(--spacing)*5)}.mb-6{margin-bottom:calc(var(--spacing)*6)}.mb-8{margin-bottom:calc(var(--spacing)*8)}.mb-12{margin-bottom:calc(var(--spacing)*12)}.-ml-2{margin-left:calc(var(--spacing)*-2)}.-ml-px{margin-left:-1px}.ml-2{margin-left:calc(var(--spacing)*2)}.ml-5{margin-left:calc(var(--spacing)*5)}.ml-6{margin-left:calc(var(--spacing)*6)}.ml-auto{margin-left:auto}.line-clamp-1{-webkit-line-clamp:1;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.line-clamp-2{-webkit-line-clamp:2;-webkit-box-orient:vertical;display:-webkit-box;overflow:hidden}.block{display:block}.contents{display:contents}.flex{display:flex}.grid{display:grid}.hidden{display:none}.inline{display:inline}.inline-block{display:inline-block}.inline-flex{display:inline-flex}.table{display:table}.aspect-square{aspect-ratio:1}.aspect-video{aspect-ratio:var(--aspect-video)}.size-\[1\.2em\]{width:1.2em;height:1.2em}.h-1{height:calc(var(--spacing)*1)}.h-2{height:calc(var(--spacing)*2)}.h-3{height:calc(var(--spacing)*3)}.h-4{height:calc(var(--spacing)*4)}.h-5{height:calc(var(--spacing)*5)}.h-6{height:calc(var(--spacing)*6)}.h-8{height:calc(var(--spacing)*8)}.h-10{height:calc(var(--spacing)*10)}.h-12{height:calc(var(--spacing)*12)}.h-16{height:calc(var(--spacing)*16)}.h-20{height:calc(var(--spacing)*20)}.h-24{height:calc(var(--spacing)*24)}.h-32{height:calc(var(--spacing)*32)}.h-40{height:calc(var(--spacing)*40)}.h-64{height:calc(var(--spacing)*64)}.h-96{height:calc(var(--spacing)*96)}.h-\[32rem\]{height:32rem}.h-\[40rem\]{height:40rem}.h-auto{height:auto}.h-full{height:100%}.h-max{height:max-content}.h-screen{height:100vh}.max-h-64{max-height:calc(var(--spacing)*64)}.min-h-64{min-height:calc(var(--spacing)*64)}.min-h-\[150px\]{min-height:150px}.min-h-screen{min-height:100vh}.w-3{width:calc(var(--spacing)*3)}.w-4{width:calc(var(--spacing)*4)}.w-5{width:calc(var(--spacing)*5)}.w-6{width:calc(var(--spacing)*6)}.w-8{width:calc(var(--spacing)*8)}.w-10{width:calc(var(--spacing)*10)}.w-12{width:calc(var(--spacing)*12)}.w-16{width:calc(var(--spacing)*16)}.w-20{width:calc(var(--spacing)*20)}.w-24{width:calc(var(--spacing)*24)}.w-32{width:calc(var(--spacing)*32)}.w-40{width:calc(var(--spacing)*40)}.w-48{width:calc(var(--spacing)*48)}.w-64{width:calc(var(--spacing)*64)}.w-80{width:calc(var(--spacing)*80)}.w-\[200px\]{width:200px}.w-\[250px\]{width:250px}.w-auto{width:auto}.w-full{width:100%}.w-max{width:max-content}.w-screen{width:100vw}.max-w-2xl{max-width:var(--container-2xl)}.max-w-3xl{max-width:var(--container-3xl)}.max-w-7xl{max-width:var(--container-7xl)}.max-w-72{max-width:calc(var(--spacing)*72)}.max-w-96{max-width:calc(var(--spacing)*96)}.max-w-full{max-width:100%}.max-w-lg{max-width:var(--container-lg)}.max-w-md{max-width:var(--container-md)}.max-w-none{max-width:none}.max-w-sm{max-width:var(--container-sm)}.max-w-xl{max-width:var(--container-xl)}.max-w-xs{max-width:var(--container-xs)}.min-w-0{min-width:calc(var(--spacing)*0)}.min-w-16{min-width:calc(var(--spacing)*16)}.min-w-48{min-width:calc(var(--spacing)*48)}.min-w-64{min-width:calc(var(--spacing)*64)}.min-w-96{min-width:calc(var(--spacing)*96)}.min-w-\[8rem\]{min-width:8rem}.min-w-\[20px\]{min-width:20px}.min-w-full{min-width:100%}.min-w-max{min-width:max-content}.flex-1{flex:1}.flex-none{flex:none}.flex-shrink-0{flex-shrink:0}.-translate-x-1\/2{--tw-translate-x: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-x-full{--tw-translate-x:-100%;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-0{--tw-translate-x:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-x-full{--tw-translate-x:100%;translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-1\/2{--tw-translate-y: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.-translate-y-full{--tw-translate-y:-100%;translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-0{--tw-translate-y:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-4{--tw-translate-y:calc(var(--spacing)*4);translate:var(--tw-translate-x)var(--tw-translate-y)}.translate-y-full{--tw-translate-y:100%;translate:var(--tw-translate-x)var(--tw-translate-y)}.scale-90{--tw-scale-x:90%;--tw-scale-y:90%;--tw-scale-z:90%;scale:var(--tw-scale-x)var(--tw-scale-y)}.scale-100{--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y)}.rotate-180{rotate:180deg}.transform{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.transform\!{transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)!important}.animate-spin{animation:var(--animate-spin)}.cursor-default{cursor:default}.cursor-not-allowed{cursor:not-allowed}.cursor-pointer{cursor:pointer}.resize{resize:both}.list-none{list-style-type:none}.appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.flex-col{flex-direction:column}.flex-row{flex-direction:row}.flex-wrap{flex-wrap:wrap}.place-content-center{place-content:center}.items-center{align-items:center}.items-end{align-items:flex-end}.items-start{align-items:flex-start}.items-stretch{align-items:stretch}.justify-between{justify-content:space-between}.justify-center{justify-content:center}.justify-end{justify-content:flex-end}.justify-start{justify-content:flex-start}.gap-1{gap:calc(var(--spacing)*1)}.gap-1\.5{gap:calc(var(--spacing)*1.5)}.gap-2{gap:calc(var(--spacing)*2)}.gap-3{gap:calc(var(--spacing)*3)}.gap-4{gap:calc(var(--spacing)*4)}.gap-6{gap:calc(var(--spacing)*6)}.gap-8{gap:calc(var(--spacing)*8)}.gap-12{gap:calc(var(--spacing)*12)}:where(.space-y-1>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-1\.5>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*1.5)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*1.5)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-2>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*2)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-3>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*3)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*3)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-4>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*4)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-6>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*6)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*6)*calc(1 - var(--tw-space-y-reverse)))}:where(.space-y-8>:not(:last-child)){--tw-space-y-reverse:0;margin-block-start:calc(calc(var(--spacing)*8)*var(--tw-space-y-reverse));margin-block-end:calc(calc(var(--spacing)*8)*calc(1 - var(--tw-space-y-reverse)))}.gap-x-2{column-gap:calc(var(--spacing)*2)}.gap-x-3{column-gap:calc(var(--spacing)*3)}.gap-x-6{column-gap:calc(var(--spacing)*6)}:where(.space-x-1>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*1)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*1)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-2>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*2)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*2)*calc(1 - var(--tw-space-x-reverse)))}:where(.space-x-4>:not(:last-child)){--tw-space-x-reverse:0;margin-inline-start:calc(calc(var(--spacing)*4)*var(--tw-space-x-reverse));margin-inline-end:calc(calc(var(--spacing)*4)*calc(1 - var(--tw-space-x-reverse)))}:where(.divide-y>:not(:last-child)){--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))}:where(.divide-surface-alt-600>:not(:last-child)){border-color:var(--color-surface-alt-600)}.truncate{text-overflow:ellipsis;white-space:nowrap;overflow:hidden}.overflow-hidden{overflow:hidden}.overflow-x-auto{overflow-x:auto}.overflow-y-auto{overflow-y:auto}.overflow-y-hidden{overflow-y:hidden}.rounded{border-radius:.25rem}.rounded-2xl{border-radius:var(--radius-2xl)}.rounded-\[--input-radius\]{border-radius:--input-radius}.rounded-\[var\(--box-radius\)\]{border-radius:var(--box-radius)}.rounded-full{border-radius:3.40282e38px}.rounded-lg{border-radius:var(--radius-lg)}.rounded-md{border-radius:var(--radius-md)}.rounded-none{border-radius:0}.\!border{border-style:var(--tw-border-style)!important;border-width:1px!important}.border{border-style:var(--tw-border-style);border-width:1px}.border-0{border-style:var(--tw-border-style);border-width:0}.border-2{border-style:var(--tw-border-style);border-width:2px}.border-y{border-block-style:var(--tw-border-style);border-block-width:1px}.border-t{border-top-style:var(--tw-border-style);border-top-width:1px}.border-r{border-right-style:var(--tw-border-style);border-right-width:1px}.border-b{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}.border-b-2{border-bottom-style:var(--tw-border-style);border-bottom-width:2px}.border-b-4{border-bottom-style:var(--tw-border-style);border-bottom-width:4px}.border-l{border-left-style:var(--tw-border-style);border-left-width:1px}.border-l-4{border-left-style:var(--tw-border-style);border-left-width:4px}.\!border-solid{--tw-border-style:solid!important;border-style:solid!important}.border-dashed{--tw-border-style:dashed;border-style:dashed}.border-none{--tw-border-style:none;border-style:none}.border-danger{border-color:var(--color-danger)}.border-gray-300{border-color:var(--color-gray-300)}.border-info{border-color:var(--color-info)}.border-neutral-100{border-color:var(--color-neutral-100)}.border-on-background\/8{border-color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.border-on-background\/8{border-color:color-mix(in oklab,var(--color-on-background)8%,transparent)}}.border-on-background\/10{border-color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.border-on-background\/10{border-color:color-mix(in oklab,var(--color-on-background)10%,transparent)}}.border-on-background\/70{border-color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.border-on-background\/70{border-color:color-mix(in oklab,var(--color-on-background)70%,transparent)}}.border-on-secondary\/8{border-color:var(--color-on-secondary)}@supports (color:color-mix(in lab,red,red)){.border-on-secondary\/8{border-color:color-mix(in oklab,var(--color-on-secondary)8%,transparent)}}.border-on-surface-alt\/8{border-color:var(--color-on-surface-alt)}@supports (color:color-mix(in lab,red,red)){.border-on-surface-alt\/8{border-color:color-mix(in oklab,var(--color-on-surface-alt)8%,transparent)}}.border-on-surface-alt\/10{border-color:var(--color-on-surface-alt)}@supports (color:color-mix(in lab,red,red)){.border-on-surface-alt\/10{border-color:color-mix(in oklab,var(--color-on-surface-alt)10%,transparent)}}.border-on-surface\/7{border-color:var(--color-on-surface)}@supports (color:color-mix(in lab,red,red)){.border-on-surface\/7{border-color:color-mix(in oklab,var(--color-on-surface)7%,transparent)}}.border-on-surface\/8{border-color:var(--color-on-surface)}@supports (color:color-mix(in lab,red,red)){.border-on-surface\/8{border-color:color-mix(in oklab,var(--color-on-surface)8%,transparent)}}.border-primary{border-color:var(--color-primary)}.border-secondary{border-color:var(--color-secondary)}.border-success{border-color:var(--color-success)}.border-surface\/70{border-color:var(--color-surface)}@supports (color:color-mix(in lab,red,red)){.border-surface\/70{border-color:color-mix(in oklab,var(--color-surface)70%,transparent)}}.border-transparent{border-color:#0000}.border-warning{border-color:var(--color-warning)}.bg-accent{background-color:var(--color-accent)}.bg-background{background-color:var(--color-background)}.bg-black{background-color:var(--color-black)}.bg-black\/20{background-color:#0003}@supports (color:color-mix(in lab,red,red)){.bg-black\/20{background-color:color-mix(in oklab,var(--color-black)20%,transparent)}}.bg-black\/30{background-color:#0000004d}@supports (color:color-mix(in lab,red,red)){.bg-black\/30{background-color:color-mix(in oklab,var(--color-black)30%,transparent)}}.bg-black\/40{background-color:#0006}@supports (color:color-mix(in lab,red,red)){.bg-black\/40{background-color:color-mix(in oklab,var(--color-black)40%,transparent)}}.bg-black\/50{background-color:#00000080}@supports (color:color-mix(in lab,red,red)){.bg-black\/50{background-color:color-mix(in oklab,var(--color-black)50%,transparent)}}.bg-black\/70{background-color:#000000b3}@supports (color:color-mix(in lab,red,red)){.bg-black\/70{background-color:color-mix(in oklab,var(--color-black)70%,transparent)}}.bg-danger{background-color:var(--color-danger)}.bg-danger-50{background-color:var(--color-danger-50)}.bg-danger-100{background-color:var(--color-danger-100)}.bg-gray-200{background-color:var(--color-gray-200)}.bg-gray-900{background-color:var(--color-gray-900)}.bg-info-100{background-color:var(--color-info-100)}.bg-neutral{background-color:var(--color-neutral)}.bg-neutral-50{background-color:var(--color-neutral-50)}.bg-neutral-100{background-color:var(--color-neutral-100)}.bg-primary{background-color:var(--color-primary)}.bg-primary-50{background-color:var(--color-primary-50)}.bg-primary\/10{background-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.bg-primary\/10{background-color:color-mix(in oklab,var(--color-primary)10%,transparent)}}.bg-secondary{background-color:var(--color-secondary)}.bg-success-100{background-color:var(--color-success-100)}.bg-success\/10{background-color:var(--color-success)}@supports (color:color-mix(in lab,red,red)){.bg-success\/10{background-color:color-mix(in oklab,var(--color-success)10%,transparent)}}.bg-surface{background-color:var(--color-surface)}.bg-surface-alt{background-color:var(--color-surface-alt)}.bg-surface-alt-600{background-color:var(--color-surface-alt-600)}.bg-transparent{background-color:#0000}.bg-warning-100{background-color:var(--color-warning-100)}.bg-cover{background-size:cover}.bg-center{background-position:50%}.fill-accent{fill:var(--color-accent)}.fill-surface{fill:var(--color-surface)}.stroke-accent{stroke:var(--color-accent)}.stroke-on-background{stroke:var(--color-on-background)}.object-cover{object-fit:cover}.object-center{object-position:center}.p-1{padding:calc(var(--spacing)*1)}.p-1\.5{padding:calc(var(--spacing)*1.5)}.p-2{padding:calc(var(--spacing)*2)}.p-3{padding:calc(var(--spacing)*3)}.p-4{padding:calc(var(--spacing)*4)}.p-5{padding:calc(var(--spacing)*5)}.p-6{padding:calc(var(--spacing)*6)}.p-8{padding:calc(var(--spacing)*8)}.p-px{padding:1px}.px-2{padding-inline:calc(var(--spacing)*2)}.px-3{padding-inline:calc(var(--spacing)*3)}.px-3\.5{padding-inline:calc(var(--spacing)*3.5)}.px-4{padding-inline:calc(var(--spacing)*4)}.px-5{padding-inline:calc(var(--spacing)*5)}.px-6{padding-inline:calc(var(--spacing)*6)}.px-12{padding-inline:calc(var(--spacing)*12)}.px-16{padding-inline:calc(var(--spacing)*16)}.\!py-1{padding-block:calc(var(--spacing)*1)!important}.py-0\.5{padding-block:calc(var(--spacing)*.5)}.py-1{padding-block:calc(var(--spacing)*1)}.py-1\.5{padding-block:calc(var(--spacing)*1.5)}.py-2{padding-block:calc(var(--spacing)*2)}.py-3{padding-block:calc(var(--spacing)*3)}.py-4{padding-block:calc(var(--spacing)*4)}.py-5{padding-block:calc(var(--spacing)*5)}.py-6{padding-block:calc(var(--spacing)*6)}.py-8{padding-block:calc(var(--spacing)*8)}.py-10{padding-block:calc(var(--spacing)*10)}.py-12{padding-block:calc(var(--spacing)*12)}.py-16{padding-block:calc(var(--spacing)*16)}.ps-3{padding-inline-start:calc(var(--spacing)*3)}.ps-10{padding-inline-start:calc(var(--spacing)*10)}.pe-3{padding-inline-end:calc(var(--spacing)*3)}.pe-12{padding-inline-end:calc(var(--spacing)*12)}.pt-1{padding-top:calc(var(--spacing)*1)}.pt-2{padding-top:calc(var(--spacing)*2)}.pt-3{padding-top:calc(var(--spacing)*3)}.pt-4{padding-top:calc(var(--spacing)*4)}.pt-8{padding-top:calc(var(--spacing)*8)}.pr-2{padding-right:calc(var(--spacing)*2)}.pr-3{padding-right:calc(var(--spacing)*3)}.pr-6{padding-right:calc(var(--spacing)*6)}.pr-8{padding-right:calc(var(--spacing)*8)}.pr-10{padding-right:calc(var(--spacing)*10)}.pr-12{padding-right:calc(var(--spacing)*12)}.pb-2{padding-bottom:calc(var(--spacing)*2)}.pb-3{padding-bottom:calc(var(--spacing)*3)}.pb-4{padding-bottom:calc(var(--spacing)*4)}.pb-5{padding-bottom:calc(var(--spacing)*5)}.pl-3{padding-left:calc(var(--spacing)*3)}.pl-6{padding-left:calc(var(--spacing)*6)}.pl-8{padding-left:calc(var(--spacing)*8)}.pl-10{padding-left:calc(var(--spacing)*10)}.pl-12{padding-left:calc(var(--spacing)*12)}.text-center{text-align:center}.text-end{text-align:end}.text-left{text-align:left}.text-right{text-align:right}.text-start{text-align:start}.align-middle{vertical-align:middle}.font-serif{font-family:var(--font-serif)}.text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.text-3xl{font-size:var(--text-3xl);line-height:var(--tw-leading,var(--text-3xl--line-height))}.text-4xl{font-size:var(--text-4xl);line-height:var(--tw-leading,var(--text-4xl--line-height))}.text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}.text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.text-xs{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.text-\[0\.6rem\]{font-size:.6rem}.leading-5{--tw-leading:calc(var(--spacing)*5);line-height:calc(var(--spacing)*5)}.leading-relaxed{--tw-leading:var(--leading-relaxed);line-height:var(--leading-relaxed)}.leading-tight{--tw-leading:var(--leading-tight);line-height:var(--leading-tight)}.font-bold{--tw-font-weight:var(--font-weight-bold);font-weight:var(--font-weight-bold)}.font-extrabold{--tw-font-weight:var(--font-weight-extrabold);font-weight:var(--font-weight-extrabold)}.font-medium{--tw-font-weight:var(--font-weight-medium);font-weight:var(--font-weight-medium)}.font-normal{--tw-font-weight:var(--font-weight-normal);font-weight:var(--font-weight-normal)}.font-semibold{--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.tracking-tight{--tw-tracking:var(--tracking-tight);letter-spacing:var(--tracking-tight)}.whitespace-nowrap{white-space:nowrap}.text-accent{color:var(--color-accent)}.text-danger{color:var(--color-danger)}.text-danger-100{color:var(--color-danger-100)}.text-danger-500{color:var(--color-danger-500)}.text-danger-900{color:var(--color-danger-900)}.text-gray-400{color:var(--color-gray-400)}.text-gray-400\/50{color:#99a1af80}@supports (color:color-mix(in lab,red,red)){.text-gray-400\/50{color:color-mix(in oklab,var(--color-gray-400)50%,transparent)}}.text-gray-500{color:var(--color-gray-500)}.text-gray-600{color:var(--color-gray-600)}.text-info-900{color:var(--color-info-900)}.text-neutral{color:var(--color-neutral)}.text-on-accent{color:var(--color-on-accent)}.text-on-background,.text-on-background\/50{color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.text-on-background\/50{color:color-mix(in oklab,var(--color-on-background)50%,transparent)}}.text-on-background\/60{color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.text-on-background\/60{color:color-mix(in oklab,var(--color-on-background)60%,transparent)}}.text-on-background\/70{color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.text-on-background\/70{color:color-mix(in oklab,var(--color-on-background)70%,transparent)}}.text-on-background\/80{color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.text-on-background\/80{color:color-mix(in oklab,var(--color-on-background)80%,transparent)}}.text-on-background\/90{color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.text-on-background\/90{color:color-mix(in oklab,var(--color-on-background)90%,transparent)}}.text-on-neutral{color:var(--color-on-neutral)}.text-on-primary{color:var(--color-on-primary)}.text-on-secondary{color:var(--color-on-secondary)}.text-on-surface{color:var(--color-on-surface)}.text-on-surface-alt,.text-on-surface-alt\/70{color:var(--color-on-surface-alt)}@supports (color:color-mix(in lab,red,red)){.text-on-surface-alt\/70{color:color-mix(in oklab,var(--color-on-surface-alt)70%,transparent)}}.text-on-surface-alt\/80{color:var(--color-on-surface-alt)}@supports (color:color-mix(in lab,red,red)){.text-on-surface-alt\/80{color:color-mix(in oklab,var(--color-on-surface-alt)80%,transparent)}}.text-on-surface\/70{color:var(--color-on-surface)}@supports (color:color-mix(in lab,red,red)){.text-on-surface\/70{color:color-mix(in oklab,var(--color-on-surface)70%,transparent)}}.text-on-surface\/80{color:var(--color-on-surface)}@supports (color:color-mix(in lab,red,red)){.text-on-surface\/80{color:color-mix(in oklab,var(--color-on-surface)80%,transparent)}}.text-primary{color:var(--color-primary)}.text-primary-50{color:var(--color-primary-50)}.text-primary-100{color:var(--color-primary-100)}.text-secondary{color:var(--color-secondary)}.text-secondary-700{color:var(--color-secondary-700)}.text-success{color:var(--color-success)}.text-success-900{color:var(--color-success-900)}.text-surface-200,.text-surface-200\/80{color:var(--color-surface-200)}@supports (color:color-mix(in lab,red,red)){.text-surface-200\/80{color:color-mix(in oklab,var(--color-surface-200)80%,transparent)}}.text-warning-900{color:var(--color-warning-900)}.text-white{color:var(--color-white)}.text-white\/80{color:#fffc}@supports (color:color-mix(in lab,red,red)){.text-white\/80{color:color-mix(in oklab,var(--color-white)80%,transparent)}}.text-white\/85{color:#ffffffd9}@supports (color:color-mix(in lab,red,red)){.text-white\/85{color:color-mix(in oklab,var(--color-white)85%,transparent)}}.text-white\/90{color:#ffffffe6}@supports (color:color-mix(in lab,red,red)){.text-white\/90{color:color-mix(in oklab,var(--color-white)90%,transparent)}}.capitalize{text-transform:capitalize}.uppercase{text-transform:uppercase}.italic{font-style:italic}.line-through{text-decoration-line:line-through}.opacity-0{opacity:0}.opacity-100{opacity:1}.shadow{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-lg{--tw-shadow:0 10px 15px -3px var(--tw-shadow-color,#0000001a),0 4px 6px -4px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-md{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-sm{--tw-shadow:0 1px 3px 0 var(--tw-shadow-color,#0000001a),0 1px 2px -1px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xl{--tw-shadow:0 20px 25px -5px var(--tw-shadow-color,#0000001a),0 8px 10px -6px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.shadow-xs{--tw-shadow:0 1px 2px 0 var(--tw-shadow-color,#0000000d);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.ring-2{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.\!ring-primary-300{--tw-ring-color:var(--color-primary-300)!important}.ring-primary{--tw-ring-color:var(--color-primary)}.ring-offset-2{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.outline{outline-style:var(--tw-outline-style);outline-width:1px}.blur{--tw-blur:blur(8px);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.brightness-50{--tw-brightness:brightness(50%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.brightness-60{--tw-brightness:brightness(60%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.brightness-70{--tw-brightness:brightness(70%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.brightness-80{--tw-brightness:brightness(80%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.filter{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.filter\!{filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)!important}.backdrop-blur-sm{--tw-backdrop-blur:blur(var(--blur-sm));-webkit-backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,);backdrop-filter:var(--tw-backdrop-blur,)var(--tw-backdrop-brightness,)var(--tw-backdrop-contrast,)var(--tw-backdrop-grayscale,)var(--tw-backdrop-hue-rotate,)var(--tw-backdrop-invert,)var(--tw-backdrop-opacity,)var(--tw-backdrop-saturate,)var(--tw-backdrop-sepia,)}.transition{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to,opacity,box-shadow,transform,translate,scale,rotate,filter,-webkit-backdrop-filter,backdrop-filter,display,visibility,content-visibility,overlay,pointer-events;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-colors{transition-property:color,background-color,border-color,outline-color,text-decoration-color,fill,stroke,--tw-gradient-from,--tw-gradient-via,--tw-gradient-to;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-opacity{transition-property:opacity;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-shadow{transition-property:box-shadow;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.transition-transform{transition-property:transform,translate,scale,rotate;transition-timing-function:var(--tw-ease,var(--default-transition-timing-function));transition-duration:var(--tw-duration,var(--default-transition-duration))}.duration-100{--tw-duration:.1s;transition-duration:.1s}.duration-150{--tw-duration:.15s;transition-duration:.15s}.duration-200{--tw-duration:.2s;transition-duration:.2s}.duration-300{--tw-duration:.3s;transition-duration:.3s}.ease-in{--tw-ease:var(--ease-in);transition-timing-function:var(--ease-in)}.ease-in-out{--tw-ease:var(--ease-in-out);transition-timing-function:var(--ease-in-out)}.ease-out{--tw-ease:var(--ease-out);transition-timing-function:var(--ease-out)}.outline-none{--tw-outline-style:none;outline-style:none}.select-none{-webkit-user-select:none;user-select:none}.\[wire\:model\=\"\'\+a\+\'\"\]{wire:model="'+a+'"}.\[wire\:model\]{wire:model}.not-last\:border-b:not(:last-child){border-bottom-style:var(--tw-border-style);border-bottom-width:1px}@media (hover:hover){.group-hover\:visible:is(:where(.group):hover *){visibility:visible}.group-hover\:mr-0:is(:where(.group):hover *){margin-right:calc(var(--spacing)*0)}.group-hover\:flex:is(:where(.group):hover *){display:flex}.group-hover\:scale-105:is(:where(.group):hover *){--tw-scale-x:105%;--tw-scale-y:105%;--tw-scale-z:105%;scale:var(--tw-scale-x)var(--tw-scale-y)}.group-hover\:bg-black\/40:is(:where(.group):hover *){background-color:#0006}@supports (color:color-mix(in lab,red,red)){.group-hover\:bg-black\/40:is(:where(.group):hover *){background-color:color-mix(in oklab,var(--color-black)40%,transparent)}}.group-hover\:opacity-100:is(:where(.group):hover *){opacity:1}.group-hover\:brightness-60:is(:where(.group):hover *){--tw-brightness:brightness(60%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}.group-hover\:brightness-70:is(:where(.group):hover *){--tw-brightness:brightness(70%);filter:var(--tw-blur,)var(--tw-brightness,)var(--tw-contrast,)var(--tw-grayscale,)var(--tw-hue-rotate,)var(--tw-invert,)var(--tw-saturate,)var(--tw-sepia,)var(--tw-drop-shadow,)}}.before\:absolute:before{content:var(--tw-content);position:absolute}.before\:inset-0:before{content:var(--tw-content);inset:calc(var(--spacing)*0)}.before\:z-10:before{content:var(--tw-content);z-index:10}.last\:border-b-0:last-child{border-bottom-style:var(--tw-border-style);border-bottom-width:0}.last\:border-none:last-child{--tw-border-style:none;border-style:none}.focus-within\:ring-2:focus-within{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus-within\:ring-primary:focus-within{--tw-ring-color:var(--color-primary)}@media (hover:hover){.hover\:border-on-background\/12:hover{border-color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.hover\:border-on-background\/12:hover{border-color:color-mix(in oklab,var(--color-on-background)12%,transparent)}}.hover\:border-on-surface\/10:hover{border-color:var(--color-on-surface)}@supports (color:color-mix(in lab,red,red)){.hover\:border-on-surface\/10:hover{border-color:color-mix(in oklab,var(--color-on-surface)10%,transparent)}}.hover\:border-primary:hover{border-color:var(--color-primary)}.hover\:bg-danger:hover{background-color:var(--color-danger)}.hover\:bg-danger-200:hover{background-color:var(--color-danger-200)}.hover\:bg-danger\/10:hover{background-color:var(--color-danger)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-danger\/10:hover{background-color:color-mix(in oklab,var(--color-danger)10%,transparent)}}.hover\:bg-info-200:hover{background-color:var(--color-info-200)}.hover\:bg-neutral-100:hover{background-color:var(--color-neutral-100)}.hover\:bg-primary:hover{background-color:var(--color-primary)}.hover\:bg-secondary-400:hover{background-color:var(--color-secondary-400)}.hover\:bg-success-200:hover{background-color:var(--color-success-200)}.hover\:bg-surface:hover{background-color:var(--color-surface)}.hover\:bg-surface-600:hover,.hover\:bg-surface-600\/50:hover{background-color:var(--color-surface-600)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-surface-600\/50:hover{background-color:color-mix(in oklab,var(--color-surface-600)50%,transparent)}}.hover\:bg-surface-alt:hover{background-color:var(--color-surface-alt)}.hover\:bg-surface\/90:hover{background-color:var(--color-surface)}@supports (color:color-mix(in lab,red,red)){.hover\:bg-surface\/90:hover{background-color:color-mix(in oklab,var(--color-surface)90%,transparent)}}.hover\:bg-warning-200:hover{background-color:var(--color-warning-200)}.hover\:text-danger:hover{color:var(--color-danger)}.hover\:text-danger-50:hover{color:var(--color-danger-50)}.hover\:text-on-background:hover{color:var(--color-on-background)}.hover\:text-primary:hover,.hover\:text-primary\/80:hover{color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.hover\:text-primary\/80:hover{color:color-mix(in oklab,var(--color-primary)80%,transparent)}}.hover\:text-secondary-200:hover{color:var(--color-secondary-200)}.hover\:text-white:hover{color:var(--color-white)}.hover\:underline:hover{text-decoration-line:underline}.hover\:opacity-75:hover{opacity:.75}.hover\:opacity-80:hover{opacity:.8}.hover\:opacity-90:hover{opacity:.9}.hover\:shadow-md:hover{--tw-shadow:0 4px 6px -1px var(--tw-shadow-color,#0000001a),0 2px 4px -2px var(--tw-shadow-color,#0000001a);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\:ring-2:hover{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.hover\:ring-neutral-200:hover{--tw-ring-color:var(--color-neutral-200)}.hover\:ring-offset-2:hover{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}}.focus\:z-10:focus{z-index:10}.focus\:border-primary:focus{border-color:var(--color-primary)}.focus\:ring-0:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(0px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-2:focus{--tw-ring-shadow:var(--tw-ring-inset,)0 0 0 calc(2px + var(--tw-ring-offset-width))var(--tw-ring-color,currentcolor);box-shadow:var(--tw-inset-shadow),var(--tw-inset-ring-shadow),var(--tw-ring-offset-shadow),var(--tw-ring-shadow),var(--tw-shadow)}.focus\:ring-primary:focus{--tw-ring-color:var(--color-primary)}.focus\:ring-offset-2:focus{--tw-ring-offset-width:2px;--tw-ring-offset-shadow:var(--tw-ring-inset,)0 0 0 var(--tw-ring-offset-width)var(--tw-ring-offset-color)}.focus\:outline-none:focus{--tw-outline-style:none;outline-style:none}.active\:bg-surface-100:active{background-color:var(--color-surface-100)}.active\:bg-surface-alt-600:active{background-color:var(--color-surface-alt-600)}.active\:text-on-background:active{color:var(--color-on-background)}.disabled\:cursor-default:disabled{cursor:default}.disabled\:cursor-not-allowed:disabled{cursor:not-allowed}.disabled\:text-on-background\/30:disabled{color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.disabled\:text-on-background\/30:disabled{color:color-mix(in oklab,var(--color-on-background)30%,transparent)}}@media (hover:hover){.disabled\:hover\:bg-transparent:disabled:hover{background-color:#0000}.disabled\:hover\:text-neutral:disabled:hover{color:var(--color-neutral)}}.has-\[\:checked\]\:border-primary:has(:checked){border-color:var(--color-primary)}.has-\[\:checked\]\:bg-primary\/10:has(:checked){background-color:var(--color-primary)}@supports (color:color-mix(in lab,red,red)){.has-\[\:checked\]\:bg-primary\/10:has(:checked){background-color:color-mix(in oklab,var(--color-primary)10%,transparent)}}.data-\[active\]\:text-primary[data-active]{color:var(--color-primary)}.data-\[align\=center\]\:left-1\/2[data-align=center]{left:50%}.data-\[align\=center\]\:-translate-x-1\/2[data-align=center]{--tw-translate-x: -50% ;translate:var(--tw-translate-x)var(--tw-translate-y)}.data-\[align\=end\]\:right-4[data-align=end]{right:calc(var(--spacing)*4)}.data-\[align\=start\]\:left-4[data-align=start]{left:calc(var(--spacing)*4)}.data-\[placement\=end\]\:end-0[data-placement=end]{inset-inline-end:calc(var(--spacing)*0)}.data-\[placement\=start\]\:start-0[data-placement=start]{inset-inline-start:calc(var(--spacing)*0)}.data-\[side\=bottom\]\:bottom-4[data-side=bottom]{bottom:calc(var(--spacing)*4)}.data-\[side\=top\]\:top-4[data-side=top]{top:calc(var(--spacing)*4)}.data-\[state\=closed\]\:animate-slide-out-down[data-state=closed]{animation:var(--animate-slide-out-down)}.data-\[state\=closed\]\:animate-slide-out-up[data-state=closed]{animation:var(--animate-slide-out-up)}.data-\[state\=open\]\:rotate-90[data-state=open]{rotate:90deg}.data-\[state\=open\]\:rotate-180[data-state=open]{rotate:180deg}.data-\[state\=open\]\:animate-slide-in-down[data-state=open]{animation:var(--animate-slide-in-down)}.data-\[state\=open\]\:animate-slide-in-up[data-state=open]{animation:var(--animate-slide-in-up)}.data-\[state\=open\]\:border-b[data-state=open]{border-bottom-style:var(--tw-border-style);border-bottom-width:1px}@media not all and (min-width:48rem){.max-md\:text-lg{font-size:var(--text-lg);line-height:var(--tw-leading,var(--text-lg--line-height))}}@media not all and (min-width:40rem){.max-sm\:mb-4{margin-bottom:calc(var(--spacing)*4)}.max-sm\:block{display:block}.max-sm\:flex{display:flex}.max-sm\:hidden{display:none}.max-sm\:justify-between{justify-content:space-between}.max-sm\:overflow-x-auto{overflow-x:auto}.max-sm\:rounded{border-radius:.25rem}.max-sm\:border{border-style:var(--tw-border-style);border-width:1px}.max-sm\:p-2{padding:calc(var(--spacing)*2)}.max-sm\:text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.max-sm\:leading-4{--tw-leading:calc(var(--spacing)*4);line-height:calc(var(--spacing)*4)}.max-sm\:before\:pr-2:before{content:var(--tw-content);padding-right:calc(var(--spacing)*2)}.max-sm\:before\:text-sm:before{content:var(--tw-content);font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.max-sm\:before\:font-semibold:before{content:var(--tw-content);--tw-font-weight:var(--font-weight-semibold);font-weight:var(--font-weight-semibold)}.max-sm\:before\:text-on-background:before{content:var(--tw-content);color:var(--color-on-background)}.max-sm\:before\:content-\[attr\(data-label\)\]:before{--tw-content:attr(data-label);content:var(--tw-content)}}@media (min-width:40rem){.sm\:mt-6{margin-top:calc(var(--spacing)*6)}.sm\:block{display:block}.sm\:flex{display:flex}.sm\:hidden{display:none}.sm\:inline{display:inline}.sm\:max-w-64{max-width:calc(var(--spacing)*64)}.sm\:flex-1{flex:1}.sm\:translate-y-0{--tw-translate-y:calc(var(--spacing)*0);translate:var(--tw-translate-x)var(--tw-translate-y)}.sm\:scale-95{--tw-scale-x:95%;--tw-scale-y:95%;--tw-scale-z:95%;scale:var(--tw-scale-x)var(--tw-scale-y)}.sm\:scale-100{--tw-scale-x:100%;--tw-scale-y:100%;--tw-scale-z:100%;scale:var(--tw-scale-x)var(--tw-scale-y)}.sm\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.sm\:flex-row{flex-direction:row}.sm\:items-center{align-items:center}.sm\:justify-between{justify-content:space-between}:where(.sm\:divide-y>:not(:last-child)){--tw-divide-y-reverse:0;border-bottom-style:var(--tw-border-style);border-top-style:var(--tw-border-style);border-top-width:calc(1px*var(--tw-divide-y-reverse));border-bottom-width:calc(1px*calc(1 - var(--tw-divide-y-reverse)))}.sm\:p-6{padding:calc(var(--spacing)*6)}.sm\:px-6{padding-inline:calc(var(--spacing)*6)}.sm\:py-4{padding-block:calc(var(--spacing)*4)}.sm\:py-8{padding-block:calc(var(--spacing)*8)}.sm\:text-right{text-align:right}.sm\:text-5xl{font-size:var(--text-5xl);line-height:var(--tw-leading,var(--text-5xl--line-height))}.sm\:text-base{font-size:var(--text-base);line-height:var(--tw-leading,var(--text-base--line-height))}.sm\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.sm\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}.sm\:duration-300{--tw-duration:.3s;transition-duration:.3s}}@media (min-width:48rem){.md\:absolute{position:absolute}.md\:sticky{position:sticky}.md\:inset-y-0{inset-block:calc(var(--spacing)*0)}.md\:top-4{top:calc(var(--spacing)*4)}.md\:order-first{order:-9999}.md\:col-span-2{grid-column:span 2/span 2}.md\:row-span-1{grid-row:span 1/span 1}.md\:row-span-2{grid-row:span 2/span 2}.md\:ms-24{margin-inline-start:calc(var(--spacing)*24)}.md\:mt-0{margin-top:calc(var(--spacing)*0)}.md\:block{display:block}.md\:flex{display:flex}.md\:hidden{display:none}.md\:h-32{height:calc(var(--spacing)*32)}.md\:w-1\/2{width:50%}.md\:w-1\/3{width:33.3333%}.md\:w-2\/3{width:66.6667%}.md\:w-32{width:calc(var(--spacing)*32)}.md\:grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.md\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.md\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.md\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.md\:grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.md\:grid-cols-6{grid-template-columns:repeat(6,minmax(0,1fr))}.md\:flex-col{flex-direction:column}.md\:flex-row{flex-direction:row}.md\:flex-row-reverse{flex-direction:row-reverse}.md\:items-center{align-items:center}.md\:items-end{align-items:flex-end}.md\:items-start{align-items:flex-start}.md\:gap-2{gap:calc(var(--spacing)*2)}.md\:p-6{padding:calc(var(--spacing)*6)}.md\:text-center{text-align:center}.md\:text-end{text-align:end}.md\:text-start{text-align:start}.md\:text-2xl{font-size:var(--text-2xl);line-height:var(--tw-leading,var(--text-2xl--line-height))}.md\:text-sm{font-size:var(--text-sm);line-height:var(--tw-leading,var(--text-sm--line-height))}.md\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}}@media (min-width:64rem){.lg\:sticky{position:sticky}.lg\:top-8{top:calc(var(--spacing)*8)}.lg\:order-last{order:9999}.lg\:col-span-1{grid-column:span 1/span 1}.lg\:col-span-2{grid-column:span 2/span 2}.lg\:block{display:block}.lg\:flex{display:flex}.lg\:hidden{display:none}.lg\:w-\[300px\]{width:300px}.lg\:flex-shrink-0{flex-shrink:0}.lg\:grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.lg\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.lg\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.lg\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.lg\:flex-row{flex-direction:row}.lg\:p-2{padding:calc(var(--spacing)*2)}.lg\:px-3{padding-inline:calc(var(--spacing)*3)}.lg\:px-8{padding-inline:calc(var(--spacing)*8)}.lg\:text-6xl{font-size:var(--text-6xl);line-height:var(--tw-leading,var(--text-6xl--line-height))}}@media (min-width:80rem){.xl\:grid-cols-1{grid-template-columns:repeat(1,minmax(0,1fr))}.xl\:grid-cols-2{grid-template-columns:repeat(2,minmax(0,1fr))}.xl\:grid-cols-3{grid-template-columns:repeat(3,minmax(0,1fr))}.xl\:grid-cols-4{grid-template-columns:repeat(4,minmax(0,1fr))}.xl\:grid-cols-5{grid-template-columns:repeat(5,minmax(0,1fr))}.xl\:grid-cols-6{grid-template-columns:repeat(6,minmax(0,1fr))}.xl\:text-xl{font-size:var(--text-xl);line-height:var(--tw-leading,var(--text-xl--line-height))}}.rtl\:right-auto:where(:dir(rtl),[dir=rtl],[dir=rtl] *){right:auto}.rtl\:left-2:where(:dir(rtl),[dir=rtl],[dir=rtl] *){left:calc(var(--spacing)*2)}.rtl\:rotate-180:where(:dir(rtl),[dir=rtl],[dir=rtl] *){rotate:180deg}.rtl\:transform:where(:dir(rtl),[dir=rtl],[dir=rtl] *){transform:var(--tw-rotate-x,)var(--tw-rotate-y,)var(--tw-rotate-z,)var(--tw-skew-x,)var(--tw-skew-y,)}.rtl\:text-right:where(:dir(rtl),[dir=rtl],[dir=rtl] *){text-align:right}.\[\&_\.control-error\]\:text-xs .control-error{font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.\[\&_\.control-error\]\:text-danger .control-error{color:var(--color-danger)}.\[\&_\.line-through\]\:text-on-background .line-through,.\[\&_\.line-through\]\:text-on-background\/60 .line-through{color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.\[\&_\.line-through\]\:text-on-background\/60 .line-through{color:color-mix(in oklab,var(--color-on-background)60%,transparent)}}.\[\&\>div\]\:flex>div{display:flex}.\[\&\>div\]\:items-center>div{align-items:center}.\[\&\>div\>p\:nth-of-type\(2\)\]\:text-xs>div>p:nth-of-type(2){font-size:var(--text-xs);line-height:var(--tw-leading,var(--text-xs--line-height))}.\[\&\>div\>p\:nth-of-type\(2\)\]\:text-neutral>div>p:nth-of-type(2){color:var(--color-neutral)}.\[\&\>div\>p\:nth-of-type\(2\)\]\:text-on-background\/60>div>p:nth-of-type(2){color:var(--color-on-background)}@supports (color:color-mix(in lab,red,red)){.\[\&\>div\>p\:nth-of-type\(2\)\]\:text-on-background\/60>div>p:nth-of-type(2){color:color-mix(in oklab,var(--color-on-background)60%,transparent)}}}@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}@property --tw-rotate-x{syntax:"*";inherits:false}@property --tw-rotate-y{syntax:"*";inherits:false}@property --tw-rotate-z{syntax:"*";inherits:false}@property --tw-skew-x{syntax:"*";inherits:false}@property --tw-skew-y{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-divide-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-leading{syntax:"*";inherits:false}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-drop-shadow-color{syntax:"*";inherits:false}@property --tw-drop-shadow-alpha{syntax:"<percentage>";inherits:false;initial-value:100%}@property --tw-drop-shadow-size{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-duration{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@property --tw-content{syntax:"*";inherits:false;initial-value:""}@keyframes spin{to{transform:rotate(360deg)}}@keyframes slideInDown{0%{opacity:0;transform:translateY(-100%)}to{opacity:1;transform:translateY(0)}}@keyframes slideOutUp{0%{opacity:1;transform:translateY(0)}to{opacity:0;transform:translateY(-100%)}}@keyframes slideInUp{0%{opacity:0;transform:translateY(100%)}to{opacity:1;transform:translateY(0)}}@keyframes slideOutDown{0%{opacity:1;transform:translateY(0)}to{opacity:0;transform:translateY(100%)}}
