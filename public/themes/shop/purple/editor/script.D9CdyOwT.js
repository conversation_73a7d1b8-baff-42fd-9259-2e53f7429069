(function(e,n){typeof exports=="object"&&typeof module<"u"?n(require("vue")):typeof define=="function"&&define.amd?define(["vue"],n):(e=typeof globalThis<"u"?globalThis:e||self,n(e.Vue))})(this,function(e){"use strict";const n={class:"wrapper"},s=["data-active","onClick"],d=e.defineComponent({__name:"RadiusPicker",props:{modelValue:{default:"none"},modelModifiers:{}},emits:["update:modelValue"],setup(o){const i=["none","xs","sm","md","xl","full"],l=e.useModel(o,"modelValue");return(a,c)=>(e.openBlock(),e.createElementBlock("div",n,[(e.openBlock(),e.createElementBlock(e.Fragment,null,e.renderList(i,t=>e.createElementVNode("div",{"data-active":t===l.value,class:e.normalizeClass({active:t===l.value}),onClick:r=>l.value=t},[e.createElementVNode("div",{class:e.normalizeClass(["radius",t])},null,2)],10,s)),64))]))}});document.addEventListener("visual:editor:booting",o=>{const{vueApp:i}=o.detail;i.component("radius-setting",d)})});
